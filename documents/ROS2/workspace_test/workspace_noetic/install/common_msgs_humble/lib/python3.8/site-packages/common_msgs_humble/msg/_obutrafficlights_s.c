// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from common_msgs_humble:msg/Obutrafficlights.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "common_msgs_humble/msg/detail/obutrafficlights__struct.h"
#include "common_msgs_humble/msg/detail/obutrafficlights__functions.h"

#include "rosidl_runtime_c/primitives_sequence.h"
#include "rosidl_runtime_c/primitives_sequence_functions.h"

// Nested array functions includes
#include "common_msgs_humble/msg/detail/obulight__functions.h"
// end nested array functions include
bool common_msgs_humble__msg__obulight__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * common_msgs_humble__msg__obulight__convert_to_py(void * raw_ros_message);

ROSIDL_GENERATOR_C_EXPORT
bool common_msgs_humble__msg__obutrafficlights__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[58];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("common_msgs_humble.msg._obutrafficlights.Obutrafficlights", full_classname_dest, 57) == 0);
  }
  common_msgs_humble__msg__Obutrafficlights * ros_message = _ros_message;
  {  // timestamp
    PyObject * field = PyObject_GetAttrString(_pymsg, "timestamp");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->timestamp = PyLong_AsLongLong(field);
    Py_DECREF(field);
  }
  {  // region_id
    PyObject * field = PyObject_GetAttrString(_pymsg, "region_id");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->region_id = PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // node_id
    PyObject * field = PyObject_GetAttrString(_pymsg, "node_id");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->node_id = PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // light_status
    PyObject * field = PyObject_GetAttrString(_pymsg, "light_status");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->light_status = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // phase_cnt
    PyObject * field = PyObject_GetAttrString(_pymsg, "phase_cnt");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->phase_cnt = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // light
    PyObject * field = PyObject_GetAttrString(_pymsg, "light");
    if (!field) {
      return false;
    }
    PyObject * seq_field = PySequence_Fast(field, "expected a sequence in 'light'");
    if (!seq_field) {
      Py_DECREF(field);
      return false;
    }
    Py_ssize_t size = PySequence_Size(field);
    if (-1 == size) {
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    if (!common_msgs_humble__msg__Obulight__Sequence__init(&(ros_message->light), size)) {
      PyErr_SetString(PyExc_RuntimeError, "unable to create common_msgs_humble__msg__Obulight__Sequence ros_message");
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    common_msgs_humble__msg__Obulight * dest = ros_message->light.data;
    for (Py_ssize_t i = 0; i < size; ++i) {
      if (!common_msgs_humble__msg__obulight__convert_from_py(PySequence_Fast_GET_ITEM(seq_field, i), &dest[i])) {
        Py_DECREF(seq_field);
        Py_DECREF(field);
        return false;
      }
    }
    Py_DECREF(seq_field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * common_msgs_humble__msg__obutrafficlights__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of Obutrafficlights */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("common_msgs_humble.msg._obutrafficlights");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "Obutrafficlights");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  common_msgs_humble__msg__Obutrafficlights * ros_message = (common_msgs_humble__msg__Obutrafficlights *)raw_ros_message;
  {  // timestamp
    PyObject * field = NULL;
    field = PyLong_FromLongLong(ros_message->timestamp);
    {
      int rc = PyObject_SetAttrString(_pymessage, "timestamp", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // region_id
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->region_id);
    {
      int rc = PyObject_SetAttrString(_pymessage, "region_id", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // node_id
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->node_id);
    {
      int rc = PyObject_SetAttrString(_pymessage, "node_id", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // light_status
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->light_status);
    {
      int rc = PyObject_SetAttrString(_pymessage, "light_status", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // phase_cnt
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->phase_cnt);
    {
      int rc = PyObject_SetAttrString(_pymessage, "phase_cnt", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // light
    PyObject * field = NULL;
    size_t size = ros_message->light.size;
    field = PyList_New(size);
    if (!field) {
      return NULL;
    }
    common_msgs_humble__msg__Obulight * item;
    for (size_t i = 0; i < size; ++i) {
      item = &(ros_message->light.data[i]);
      PyObject * pyitem = common_msgs_humble__msg__obulight__convert_to_py(item);
      if (!pyitem) {
        Py_DECREF(field);
        return NULL;
      }
      int rc = PyList_SetItem(field, i, pyitem);
      (void)rc;
      assert(rc == 0);
    }
    assert(PySequence_Check(field));
    {
      int rc = PyObject_SetAttrString(_pymessage, "light", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
