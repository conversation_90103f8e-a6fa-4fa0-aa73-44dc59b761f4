# generated from rosidl_generator_py/resource/_idl.py.em
# with input from common_msgs_humble:msg/Cloudpants.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_Cloudpants(type):
    """Metaclass of message 'Cloudpants'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('common_msgs_humble')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'common_msgs_humble.msg.Cloudpants')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__cloudpants
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__cloudpants
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__cloudpants
            cls._TYPE_SUPPORT = module.type_support_msg__msg__cloudpants
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__cloudpants

            from common_msgs_humble.msg import Cloudpant
            if Cloudpant.__class__._TYPE_SUPPORT is None:
                Cloudpant.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class Cloudpants(metaclass=Metaclass_Cloudpants):
    """Message class 'Cloudpants'."""

    __slots__ = [
        '_timestamp',
        '_frameid',
        '_count',
        '_pants',
    ]

    _fields_and_field_types = {
        'timestamp': 'int64',
        'frameid': 'int32',
        'count': 'int32',
        'pants': 'sequence<common_msgs_humble/Cloudpant>',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('int64'),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['common_msgs_humble', 'msg'], 'Cloudpant')),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.timestamp = kwargs.get('timestamp', int())
        self.frameid = kwargs.get('frameid', int())
        self.count = kwargs.get('count', int())
        self.pants = kwargs.get('pants', [])

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.timestamp != other.timestamp:
            return False
        if self.frameid != other.frameid:
            return False
        if self.count != other.count:
            return False
        if self.pants != other.pants:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def timestamp(self):
        """Message field 'timestamp'."""
        return self._timestamp

    @timestamp.setter
    def timestamp(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'timestamp' field must be of type 'int'"
            assert value >= -9223372036854775808 and value < 9223372036854775808, \
                "The 'timestamp' field must be an integer in [-9223372036854775808, 9223372036854775807]"
        self._timestamp = value

    @builtins.property
    def frameid(self):
        """Message field 'frameid'."""
        return self._frameid

    @frameid.setter
    def frameid(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'frameid' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'frameid' field must be an integer in [-2147483648, 2147483647]"
        self._frameid = value

    @builtins.property
    def count(self):
        """Message field 'count'."""
        return self._count

    @count.setter
    def count(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'count' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'count' field must be an integer in [-2147483648, 2147483647]"
        self._count = value

    @builtins.property
    def pants(self):
        """Message field 'pants'."""
        return self._pants

    @pants.setter
    def pants(self, value):
        if __debug__:
            from common_msgs_humble.msg import Cloudpant
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, Cloudpant) for v in value) and
                 True), \
                "The 'pants' field must be a set or sequence and each value of type 'Cloudpant'"
        self._pants = value
