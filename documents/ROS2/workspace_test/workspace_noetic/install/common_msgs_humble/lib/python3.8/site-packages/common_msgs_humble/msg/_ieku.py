# generated from rosidl_generator_py/resource/_idl.py.em
# with input from common_msgs_humble:msg/Ieku.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_Ieku(type):
    """Metaclass of message 'Ieku'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('common_msgs_humble')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'common_msgs_humble.msg.Ieku')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__ieku
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__ieku
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__ieku
            cls._TYPE_SUPPORT = module.type_support_msg__msg__ieku
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__ieku

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class Ieku(metaclass=Metaclass_Ieku):
    """Message class 'Ieku'."""

    __slots__ = [
        '_id',
        '_lata',
        '_lona',
        '_latb',
        '_lonb',
        '_width',
    ]

    _fields_and_field_types = {
        'id': 'uint8',
        'lata': 'double',
        'lona': 'double',
        'latb': 'double',
        'lonb': 'double',
        'width': 'float',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.id = kwargs.get('id', int())
        self.lata = kwargs.get('lata', float())
        self.lona = kwargs.get('lona', float())
        self.latb = kwargs.get('latb', float())
        self.lonb = kwargs.get('lonb', float())
        self.width = kwargs.get('width', float())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.id != other.id:
            return False
        if self.lata != other.lata:
            return False
        if self.lona != other.lona:
            return False
        if self.latb != other.latb:
            return False
        if self.lonb != other.lonb:
            return False
        if self.width != other.width:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property  # noqa: A003
    def id(self):  # noqa: A003
        """Message field 'id'."""
        return self._id

    @id.setter  # noqa: A003
    def id(self, value):  # noqa: A003
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'id' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'id' field must be an unsigned integer in [0, 255]"
        self._id = value

    @builtins.property
    def lata(self):
        """Message field 'lata'."""
        return self._lata

    @lata.setter
    def lata(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'lata' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'lata' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._lata = value

    @builtins.property
    def lona(self):
        """Message field 'lona'."""
        return self._lona

    @lona.setter
    def lona(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'lona' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'lona' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._lona = value

    @builtins.property
    def latb(self):
        """Message field 'latb'."""
        return self._latb

    @latb.setter
    def latb(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'latb' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'latb' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._latb = value

    @builtins.property
    def lonb(self):
        """Message field 'lonb'."""
        return self._lonb

    @lonb.setter
    def lonb(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'lonb' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'lonb' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._lonb = value

    @builtins.property
    def width(self):
        """Message field 'width'."""
        return self._width

    @width.setter
    def width(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'width' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'width' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._width = value
