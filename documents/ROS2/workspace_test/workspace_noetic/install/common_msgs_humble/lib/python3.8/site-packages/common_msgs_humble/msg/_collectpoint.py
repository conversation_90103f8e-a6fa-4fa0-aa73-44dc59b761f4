# generated from rosidl_generator_py/resource/_idl.py.em
# with input from common_msgs_humble:msg/Collectpoint.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_Collectpoint(type):
    """Metaclass of message 'Collectpoint'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('common_msgs_humble')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'common_msgs_humble.msg.Collectpoint')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__collectpoint
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__collectpoint
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__collectpoint
            cls._TYPE_SUPPORT = module.type_support_msg__msg__collectpoint
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__collectpoint

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class Collectpoint(metaclass=Metaclass_Collectpoint):
    """Message class 'Collectpoint'."""

    __slots__ = [
        '_zonename',
        '_index',
        '_stoptime',
        '_property',
        '_orientation',
    ]

    _fields_and_field_types = {
        'zonename': 'uint8',
        'index': 'double',
        'stoptime': 'int32',
        'property': 'uint8',
        'orientation': 'uint8',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.zonename = kwargs.get('zonename', int())
        self.index = kwargs.get('index', float())
        self.stoptime = kwargs.get('stoptime', int())
        self.property = kwargs.get('property', int())
        self.orientation = kwargs.get('orientation', int())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.zonename != other.zonename:
            return False
        if self.index != other.index:
            return False
        if self.stoptime != other.stoptime:
            return False
        if self.property != other.property:
            return False
        if self.orientation != other.orientation:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def zonename(self):
        """Message field 'zonename'."""
        return self._zonename

    @zonename.setter
    def zonename(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'zonename' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'zonename' field must be an unsigned integer in [0, 255]"
        self._zonename = value

    @builtins.property
    def index(self):
        """Message field 'index'."""
        return self._index

    @index.setter
    def index(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'index' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'index' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._index = value

    @builtins.property
    def stoptime(self):
        """Message field 'stoptime'."""
        return self._stoptime

    @stoptime.setter
    def stoptime(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'stoptime' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'stoptime' field must be an integer in [-2147483648, 2147483647]"
        self._stoptime = value

    @builtins.property  # noqa: A003
    def property(self):  # noqa: A003
        """Message field 'property'."""
        return self._property

    @property.setter  # noqa: A003
    def property(self, value):  # noqa: A003
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'property' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'property' field must be an unsigned integer in [0, 255]"
        self._property = value

    @builtins.property
    def orientation(self):
        """Message field 'orientation'."""
        return self._orientation

    @orientation.setter
    def orientation(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'orientation' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'orientation' field must be an unsigned integer in [0, 255]"
        self._orientation = value
