// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from common_msgs_humble:msg/Objecthistory.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "common_msgs_humble/msg/detail/objecthistory__struct.h"
#include "common_msgs_humble/msg/detail/objecthistory__functions.h"

bool common_msgs_humble__msg__point3d__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * common_msgs_humble__msg__point3d__convert_to_py(void * raw_ros_message);

ROSIDL_GENERATOR_C_EXPORT
bool common_msgs_humble__msg__objecthistory__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[52];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("common_msgs_humble.msg._objecthistory.Objecthistory", full_classname_dest, 51) == 0);
  }
  common_msgs_humble__msg__Objecthistory * ros_message = _ros_message;
  {  // timestamp
    PyObject * field = PyObject_GetAttrString(_pymsg, "timestamp");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->timestamp = PyLong_AsLongLong(field);
    Py_DECREF(field);
  }
  {  // trajectorypoint
    PyObject * field = PyObject_GetAttrString(_pymsg, "trajectorypoint");
    if (!field) {
      return false;
    }
    if (!common_msgs_humble__msg__point3d__convert_from_py(field, &ros_message->trajectorypoint)) {
      Py_DECREF(field);
      return false;
    }
    Py_DECREF(field);
  }
  {  // lon
    PyObject * field = PyObject_GetAttrString(_pymsg, "lon");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->lon = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // lat
    PyObject * field = PyObject_GetAttrString(_pymsg, "lat");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->lat = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // alt
    PyObject * field = PyObject_GetAttrString(_pymsg, "alt");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->alt = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // roll
    PyObject * field = PyObject_GetAttrString(_pymsg, "roll");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->roll = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // pitch
    PyObject * field = PyObject_GetAttrString(_pymsg, "pitch");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->pitch = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // heading
    PyObject * field = PyObject_GetAttrString(_pymsg, "heading");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->heading = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // relavx
    PyObject * field = PyObject_GetAttrString(_pymsg, "relavx");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->relavx = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // relavy
    PyObject * field = PyObject_GetAttrString(_pymsg, "relavy");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->relavy = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // absvx
    PyObject * field = PyObject_GetAttrString(_pymsg, "absvx");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->absvx = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // absvy
    PyObject * field = PyObject_GetAttrString(_pymsg, "absvy");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->absvy = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // s
    PyObject * field = PyObject_GetAttrString(_pymsg, "s");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->s = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // l
    PyObject * field = PyObject_GetAttrString(_pymsg, "l");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->l = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // speeds
    PyObject * field = PyObject_GetAttrString(_pymsg, "speeds");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->speeds = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // speedl
    PyObject * field = PyObject_GetAttrString(_pymsg, "speedl");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->speedl = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * common_msgs_humble__msg__objecthistory__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of Objecthistory */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("common_msgs_humble.msg._objecthistory");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "Objecthistory");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  common_msgs_humble__msg__Objecthistory * ros_message = (common_msgs_humble__msg__Objecthistory *)raw_ros_message;
  {  // timestamp
    PyObject * field = NULL;
    field = PyLong_FromLongLong(ros_message->timestamp);
    {
      int rc = PyObject_SetAttrString(_pymessage, "timestamp", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // trajectorypoint
    PyObject * field = NULL;
    field = common_msgs_humble__msg__point3d__convert_to_py(&ros_message->trajectorypoint);
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "trajectorypoint", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lon
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->lon);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lon", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lat
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->lat);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lat", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // alt
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->alt);
    {
      int rc = PyObject_SetAttrString(_pymessage, "alt", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // roll
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->roll);
    {
      int rc = PyObject_SetAttrString(_pymessage, "roll", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // pitch
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->pitch);
    {
      int rc = PyObject_SetAttrString(_pymessage, "pitch", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // heading
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->heading);
    {
      int rc = PyObject_SetAttrString(_pymessage, "heading", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // relavx
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->relavx);
    {
      int rc = PyObject_SetAttrString(_pymessage, "relavx", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // relavy
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->relavy);
    {
      int rc = PyObject_SetAttrString(_pymessage, "relavy", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // absvx
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->absvx);
    {
      int rc = PyObject_SetAttrString(_pymessage, "absvx", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // absvy
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->absvy);
    {
      int rc = PyObject_SetAttrString(_pymessage, "absvy", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // s
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->s);
    {
      int rc = PyObject_SetAttrString(_pymessage, "s", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // l
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->l);
    {
      int rc = PyObject_SetAttrString(_pymessage, "l", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // speeds
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->speeds);
    {
      int rc = PyObject_SetAttrString(_pymessage, "speeds", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // speedl
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->speedl);
    {
      int rc = PyObject_SetAttrString(_pymessage, "speedl", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
