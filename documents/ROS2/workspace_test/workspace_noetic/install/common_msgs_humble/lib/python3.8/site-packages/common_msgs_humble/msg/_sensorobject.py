# generated from rosidl_generator_py/resource/_idl.py.em
# with input from common_msgs_humble:msg/Sensorobject.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_Sensorobject(type):
    """Metaclass of message 'Sensorobject'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('common_msgs_humble')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'common_msgs_humble.msg.Sensorobject')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__sensorobject
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__sensorobject
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__sensorobject
            cls._TYPE_SUPPORT = module.type_support_msg__msg__sensorobject
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__sensorobject

            from common_msgs_humble.msg import Objecthistory
            if Objecthistory.__class__._TYPE_SUPPORT is None:
                Objecthistory.__class__.__import_type_support__()

            from common_msgs_humble.msg import Objectprediction
            if Objectprediction.__class__._TYPE_SUPPORT is None:
                Objectprediction.__class__.__import_type_support__()

            from common_msgs_humble.msg import Point3d
            if Point3d.__class__._TYPE_SUPPORT is None:
                Point3d.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class Sensorobject(metaclass=Metaclass_Sensorobject):
    """Message class 'Sensorobject'."""

    __slots__ = [
        '_id',
        '_x',
        '_y',
        '_z',
        '_longtitude',
        '_latitude',
        '_altitude',
        '_relspeedy',
        '_relspeedx',
        '_rollrad',
        '_pitchrad',
        '_azimuth',
        '_pitchrate',
        '_rollrate',
        '_yawrate',
        '_width',
        '_length',
        '_height',
        '_classification',
        '_value',
        '_confidence',
        '_points',
        '_driving_intent',
        '_behavior_state',
        '_radarindex',
        '_radarobjectid',
        '_s',
        '_l',
        '_speeds',
        '_speedl',
        '_object_decision',
        '_object_history',
        '_object_prediction',
    ]

    _fields_and_field_types = {
        'id': 'uint32',
        'x': 'float',
        'y': 'float',
        'z': 'float',
        'longtitude': 'double',
        'latitude': 'double',
        'altitude': 'double',
        'relspeedy': 'float',
        'relspeedx': 'float',
        'rollrad': 'float',
        'pitchrad': 'float',
        'azimuth': 'float',
        'pitchrate': 'double',
        'rollrate': 'double',
        'yawrate': 'double',
        'width': 'float',
        'length': 'float',
        'height': 'float',
        'classification': 'uint8',
        'value': 'uint8',
        'confidence': 'float',
        'points': 'sequence<common_msgs_humble/Point3d>',
        'driving_intent': 'uint8',
        'behavior_state': 'uint8',
        'radarindex': 'uint8',
        'radarobjectid': 'uint8',
        's': 'float',
        'l': 'float',
        'speeds': 'float',
        'speedl': 'float',
        'object_decision': 'uint8',
        'object_history': 'sequence<common_msgs_humble/Objecthistory>',
        'object_prediction': 'sequence<common_msgs_humble/Objectprediction>',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('uint32'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['common_msgs_humble', 'msg'], 'Point3d')),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['common_msgs_humble', 'msg'], 'Objecthistory')),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['common_msgs_humble', 'msg'], 'Objectprediction')),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.id = kwargs.get('id', int())
        self.x = kwargs.get('x', float())
        self.y = kwargs.get('y', float())
        self.z = kwargs.get('z', float())
        self.longtitude = kwargs.get('longtitude', float())
        self.latitude = kwargs.get('latitude', float())
        self.altitude = kwargs.get('altitude', float())
        self.relspeedy = kwargs.get('relspeedy', float())
        self.relspeedx = kwargs.get('relspeedx', float())
        self.rollrad = kwargs.get('rollrad', float())
        self.pitchrad = kwargs.get('pitchrad', float())
        self.azimuth = kwargs.get('azimuth', float())
        self.pitchrate = kwargs.get('pitchrate', float())
        self.rollrate = kwargs.get('rollrate', float())
        self.yawrate = kwargs.get('yawrate', float())
        self.width = kwargs.get('width', float())
        self.length = kwargs.get('length', float())
        self.height = kwargs.get('height', float())
        self.classification = kwargs.get('classification', int())
        self.value = kwargs.get('value', int())
        self.confidence = kwargs.get('confidence', float())
        self.points = kwargs.get('points', [])
        self.driving_intent = kwargs.get('driving_intent', int())
        self.behavior_state = kwargs.get('behavior_state', int())
        self.radarindex = kwargs.get('radarindex', int())
        self.radarobjectid = kwargs.get('radarobjectid', int())
        self.s = kwargs.get('s', float())
        self.l = kwargs.get('l', float())
        self.speeds = kwargs.get('speeds', float())
        self.speedl = kwargs.get('speedl', float())
        self.object_decision = kwargs.get('object_decision', int())
        self.object_history = kwargs.get('object_history', [])
        self.object_prediction = kwargs.get('object_prediction', [])

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.id != other.id:
            return False
        if self.x != other.x:
            return False
        if self.y != other.y:
            return False
        if self.z != other.z:
            return False
        if self.longtitude != other.longtitude:
            return False
        if self.latitude != other.latitude:
            return False
        if self.altitude != other.altitude:
            return False
        if self.relspeedy != other.relspeedy:
            return False
        if self.relspeedx != other.relspeedx:
            return False
        if self.rollrad != other.rollrad:
            return False
        if self.pitchrad != other.pitchrad:
            return False
        if self.azimuth != other.azimuth:
            return False
        if self.pitchrate != other.pitchrate:
            return False
        if self.rollrate != other.rollrate:
            return False
        if self.yawrate != other.yawrate:
            return False
        if self.width != other.width:
            return False
        if self.length != other.length:
            return False
        if self.height != other.height:
            return False
        if self.classification != other.classification:
            return False
        if self.value != other.value:
            return False
        if self.confidence != other.confidence:
            return False
        if self.points != other.points:
            return False
        if self.driving_intent != other.driving_intent:
            return False
        if self.behavior_state != other.behavior_state:
            return False
        if self.radarindex != other.radarindex:
            return False
        if self.radarobjectid != other.radarobjectid:
            return False
        if self.s != other.s:
            return False
        if self.l != other.l:
            return False
        if self.speeds != other.speeds:
            return False
        if self.speedl != other.speedl:
            return False
        if self.object_decision != other.object_decision:
            return False
        if self.object_history != other.object_history:
            return False
        if self.object_prediction != other.object_prediction:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property  # noqa: A003
    def id(self):  # noqa: A003
        """Message field 'id'."""
        return self._id

    @id.setter  # noqa: A003
    def id(self, value):  # noqa: A003
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'id' field must be of type 'int'"
            assert value >= 0 and value < 4294967296, \
                "The 'id' field must be an unsigned integer in [0, 4294967295]"
        self._id = value

    @builtins.property
    def x(self):
        """Message field 'x'."""
        return self._x

    @x.setter
    def x(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'x' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'x' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._x = value

    @builtins.property
    def y(self):
        """Message field 'y'."""
        return self._y

    @y.setter
    def y(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'y' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'y' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._y = value

    @builtins.property
    def z(self):
        """Message field 'z'."""
        return self._z

    @z.setter
    def z(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'z' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'z' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._z = value

    @builtins.property
    def longtitude(self):
        """Message field 'longtitude'."""
        return self._longtitude

    @longtitude.setter
    def longtitude(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'longtitude' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'longtitude' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._longtitude = value

    @builtins.property
    def latitude(self):
        """Message field 'latitude'."""
        return self._latitude

    @latitude.setter
    def latitude(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'latitude' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'latitude' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._latitude = value

    @builtins.property
    def altitude(self):
        """Message field 'altitude'."""
        return self._altitude

    @altitude.setter
    def altitude(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'altitude' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'altitude' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._altitude = value

    @builtins.property
    def relspeedy(self):
        """Message field 'relspeedy'."""
        return self._relspeedy

    @relspeedy.setter
    def relspeedy(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'relspeedy' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'relspeedy' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._relspeedy = value

    @builtins.property
    def relspeedx(self):
        """Message field 'relspeedx'."""
        return self._relspeedx

    @relspeedx.setter
    def relspeedx(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'relspeedx' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'relspeedx' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._relspeedx = value

    @builtins.property
    def rollrad(self):
        """Message field 'rollrad'."""
        return self._rollrad

    @rollrad.setter
    def rollrad(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'rollrad' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'rollrad' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._rollrad = value

    @builtins.property
    def pitchrad(self):
        """Message field 'pitchrad'."""
        return self._pitchrad

    @pitchrad.setter
    def pitchrad(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'pitchrad' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'pitchrad' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._pitchrad = value

    @builtins.property
    def azimuth(self):
        """Message field 'azimuth'."""
        return self._azimuth

    @azimuth.setter
    def azimuth(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'azimuth' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'azimuth' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._azimuth = value

    @builtins.property
    def pitchrate(self):
        """Message field 'pitchrate'."""
        return self._pitchrate

    @pitchrate.setter
    def pitchrate(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'pitchrate' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'pitchrate' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._pitchrate = value

    @builtins.property
    def rollrate(self):
        """Message field 'rollrate'."""
        return self._rollrate

    @rollrate.setter
    def rollrate(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'rollrate' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'rollrate' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._rollrate = value

    @builtins.property
    def yawrate(self):
        """Message field 'yawrate'."""
        return self._yawrate

    @yawrate.setter
    def yawrate(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'yawrate' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'yawrate' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._yawrate = value

    @builtins.property
    def width(self):
        """Message field 'width'."""
        return self._width

    @width.setter
    def width(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'width' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'width' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._width = value

    @builtins.property
    def length(self):
        """Message field 'length'."""
        return self._length

    @length.setter
    def length(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'length' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'length' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._length = value

    @builtins.property
    def height(self):
        """Message field 'height'."""
        return self._height

    @height.setter
    def height(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'height' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'height' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._height = value

    @builtins.property
    def classification(self):
        """Message field 'classification'."""
        return self._classification

    @classification.setter
    def classification(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'classification' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'classification' field must be an unsigned integer in [0, 255]"
        self._classification = value

    @builtins.property
    def value(self):
        """Message field 'value'."""
        return self._value

    @value.setter
    def value(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'value' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'value' field must be an unsigned integer in [0, 255]"
        self._value = value

    @builtins.property
    def confidence(self):
        """Message field 'confidence'."""
        return self._confidence

    @confidence.setter
    def confidence(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'confidence' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'confidence' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._confidence = value

    @builtins.property
    def points(self):
        """Message field 'points'."""
        return self._points

    @points.setter
    def points(self, value):
        if __debug__:
            from common_msgs_humble.msg import Point3d
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, Point3d) for v in value) and
                 True), \
                "The 'points' field must be a set or sequence and each value of type 'Point3d'"
        self._points = value

    @builtins.property
    def driving_intent(self):
        """Message field 'driving_intent'."""
        return self._driving_intent

    @driving_intent.setter
    def driving_intent(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'driving_intent' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'driving_intent' field must be an unsigned integer in [0, 255]"
        self._driving_intent = value

    @builtins.property
    def behavior_state(self):
        """Message field 'behavior_state'."""
        return self._behavior_state

    @behavior_state.setter
    def behavior_state(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'behavior_state' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'behavior_state' field must be an unsigned integer in [0, 255]"
        self._behavior_state = value

    @builtins.property
    def radarindex(self):
        """Message field 'radarindex'."""
        return self._radarindex

    @radarindex.setter
    def radarindex(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'radarindex' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'radarindex' field must be an unsigned integer in [0, 255]"
        self._radarindex = value

    @builtins.property
    def radarobjectid(self):
        """Message field 'radarobjectid'."""
        return self._radarobjectid

    @radarobjectid.setter
    def radarobjectid(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'radarobjectid' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'radarobjectid' field must be an unsigned integer in [0, 255]"
        self._radarobjectid = value

    @builtins.property
    def s(self):
        """Message field 's'."""
        return self._s

    @s.setter
    def s(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 's' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 's' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._s = value

    @builtins.property
    def l(self):
        """Message field 'l'."""
        return self._l

    @l.setter
    def l(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'l' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'l' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._l = value

    @builtins.property
    def speeds(self):
        """Message field 'speeds'."""
        return self._speeds

    @speeds.setter
    def speeds(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'speeds' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'speeds' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._speeds = value

    @builtins.property
    def speedl(self):
        """Message field 'speedl'."""
        return self._speedl

    @speedl.setter
    def speedl(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'speedl' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'speedl' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._speedl = value

    @builtins.property
    def object_decision(self):
        """Message field 'object_decision'."""
        return self._object_decision

    @object_decision.setter
    def object_decision(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'object_decision' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'object_decision' field must be an unsigned integer in [0, 255]"
        self._object_decision = value

    @builtins.property
    def object_history(self):
        """Message field 'object_history'."""
        return self._object_history

    @object_history.setter
    def object_history(self, value):
        if __debug__:
            from common_msgs_humble.msg import Objecthistory
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, Objecthistory) for v in value) and
                 True), \
                "The 'object_history' field must be a set or sequence and each value of type 'Objecthistory'"
        self._object_history = value

    @builtins.property
    def object_prediction(self):
        """Message field 'object_prediction'."""
        return self._object_prediction

    @object_prediction.setter
    def object_prediction(self, value):
        if __debug__:
            from common_msgs_humble.msg import Objectprediction
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, Objectprediction) for v in value) and
                 True), \
                "The 'object_prediction' field must be a set or sequence and each value of type 'Objectprediction'"
        self._object_prediction = value
