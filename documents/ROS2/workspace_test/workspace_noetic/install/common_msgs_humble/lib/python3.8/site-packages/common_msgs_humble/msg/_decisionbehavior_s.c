// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from common_msgs_humble:msg/Decisionbehavior.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "common_msgs_humble/msg/detail/decisionbehavior__struct.h"
#include "common_msgs_humble/msg/detail/decisionbehavior__functions.h"

#include "rosidl_runtime_c/primitives_sequence.h"
#include "rosidl_runtime_c/primitives_sequence_functions.h"

// Nested array functions includes
#include "common_msgs_humble/msg/detail/sensorobject__functions.h"
// end nested array functions include
bool common_msgs_humble__msg__sensorobject__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * common_msgs_humble__msg__sensorobject__convert_to_py(void * raw_ros_message);

ROSIDL_GENERATOR_C_EXPORT
bool common_msgs_humble__msg__decisionbehavior__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[58];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("common_msgs_humble.msg._decisionbehavior.Decisionbehavior", full_classname_dest, 57) == 0);
  }
  common_msgs_humble__msg__Decisionbehavior * ros_message = _ros_message;
  {  // drivebehavior
    PyObject * field = PyObject_GetAttrString(_pymsg, "drivebehavior");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->drivebehavior = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // obs
    PyObject * field = PyObject_GetAttrString(_pymsg, "obs");
    if (!field) {
      return false;
    }
    PyObject * seq_field = PySequence_Fast(field, "expected a sequence in 'obs'");
    if (!seq_field) {
      Py_DECREF(field);
      return false;
    }
    Py_ssize_t size = PySequence_Size(field);
    if (-1 == size) {
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    if (!common_msgs_humble__msg__Sensorobject__Sequence__init(&(ros_message->obs), size)) {
      PyErr_SetString(PyExc_RuntimeError, "unable to create common_msgs_humble__msg__Sensorobject__Sequence ros_message");
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    common_msgs_humble__msg__Sensorobject * dest = ros_message->obs.data;
    for (Py_ssize_t i = 0; i < size; ++i) {
      if (!common_msgs_humble__msg__sensorobject__convert_from_py(PySequence_Fast_GET_ITEM(seq_field, i), &dest[i])) {
        Py_DECREF(seq_field);
        Py_DECREF(field);
        return false;
      }
    }
    Py_DECREF(seq_field);
    Py_DECREF(field);
  }
  {  // isvalid
    PyObject * field = PyObject_GetAttrString(_pymsg, "isvalid");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->isvalid = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // turnlights
    PyObject * field = PyObject_GetAttrString(_pymsg, "turnlights");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->turnlights = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // laneblock
    PyObject * field = PyObject_GetAttrString(_pymsg, "laneblock");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->laneblock = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // door
    PyObject * field = PyObject_GetAttrString(_pymsg, "door");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->door = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // timestamp
    PyObject * field = PyObject_GetAttrString(_pymsg, "timestamp");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->timestamp = PyLong_AsLongLong(field);
    Py_DECREF(field);
  }
  {  // mergetrigger
    PyObject * field = PyObject_GetAttrString(_pymsg, "mergetrigger");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->mergetrigger = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // guidespeed
    PyObject * field = PyObject_GetAttrString(_pymsg, "guidespeed");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->guidespeed = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // avoidsituation
    PyObject * field = PyObject_GetAttrString(_pymsg, "avoidsituation");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->avoidsituation = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // alert
    PyObject * field = PyObject_GetAttrString(_pymsg, "alert");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->alert = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // deviation
    PyObject * field = PyObject_GetAttrString(_pymsg, "deviation");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->deviation = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // starttime
    PyObject * field = PyObject_GetAttrString(_pymsg, "starttime");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->starttime = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // endtime
    PyObject * field = PyObject_GetAttrString(_pymsg, "endtime");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->endtime = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // carworkstatus
    PyObject * field = PyObject_GetAttrString(_pymsg, "carworkstatus");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->carworkstatus = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // stationblock
    PyObject * field = PyObject_GetAttrString(_pymsg, "stationblock");
    if (!field) {
      return false;
    }
    assert(PyBool_Check(field));
    ros_message->stationblock = (Py_True == field);
    Py_DECREF(field);
  }
  {  // needreplan
    PyObject * field = PyObject_GetAttrString(_pymsg, "needreplan");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->needreplan = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // virtualpointtype
    PyObject * field = PyObject_GetAttrString(_pymsg, "virtualpointtype");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->virtualpointtype = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * common_msgs_humble__msg__decisionbehavior__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of Decisionbehavior */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("common_msgs_humble.msg._decisionbehavior");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "Decisionbehavior");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  common_msgs_humble__msg__Decisionbehavior * ros_message = (common_msgs_humble__msg__Decisionbehavior *)raw_ros_message;
  {  // drivebehavior
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->drivebehavior);
    {
      int rc = PyObject_SetAttrString(_pymessage, "drivebehavior", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // obs
    PyObject * field = NULL;
    size_t size = ros_message->obs.size;
    field = PyList_New(size);
    if (!field) {
      return NULL;
    }
    common_msgs_humble__msg__Sensorobject * item;
    for (size_t i = 0; i < size; ++i) {
      item = &(ros_message->obs.data[i]);
      PyObject * pyitem = common_msgs_humble__msg__sensorobject__convert_to_py(item);
      if (!pyitem) {
        Py_DECREF(field);
        return NULL;
      }
      int rc = PyList_SetItem(field, i, pyitem);
      (void)rc;
      assert(rc == 0);
    }
    assert(PySequence_Check(field));
    {
      int rc = PyObject_SetAttrString(_pymessage, "obs", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // isvalid
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->isvalid);
    {
      int rc = PyObject_SetAttrString(_pymessage, "isvalid", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // turnlights
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->turnlights);
    {
      int rc = PyObject_SetAttrString(_pymessage, "turnlights", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // laneblock
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->laneblock);
    {
      int rc = PyObject_SetAttrString(_pymessage, "laneblock", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // door
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->door);
    {
      int rc = PyObject_SetAttrString(_pymessage, "door", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // timestamp
    PyObject * field = NULL;
    field = PyLong_FromLongLong(ros_message->timestamp);
    {
      int rc = PyObject_SetAttrString(_pymessage, "timestamp", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // mergetrigger
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->mergetrigger);
    {
      int rc = PyObject_SetAttrString(_pymessage, "mergetrigger", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // guidespeed
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->guidespeed);
    {
      int rc = PyObject_SetAttrString(_pymessage, "guidespeed", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // avoidsituation
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->avoidsituation);
    {
      int rc = PyObject_SetAttrString(_pymessage, "avoidsituation", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // alert
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->alert);
    {
      int rc = PyObject_SetAttrString(_pymessage, "alert", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // deviation
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->deviation);
    {
      int rc = PyObject_SetAttrString(_pymessage, "deviation", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // starttime
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->starttime);
    {
      int rc = PyObject_SetAttrString(_pymessage, "starttime", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // endtime
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->endtime);
    {
      int rc = PyObject_SetAttrString(_pymessage, "endtime", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // carworkstatus
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->carworkstatus);
    {
      int rc = PyObject_SetAttrString(_pymessage, "carworkstatus", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // stationblock
    PyObject * field = NULL;
    field = PyBool_FromLong(ros_message->stationblock ? 1 : 0);
    {
      int rc = PyObject_SetAttrString(_pymessage, "stationblock", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // needreplan
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->needreplan);
    {
      int rc = PyObject_SetAttrString(_pymessage, "needreplan", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // virtualpointtype
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->virtualpointtype);
    {
      int rc = PyObject_SetAttrString(_pymessage, "virtualpointtype", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
