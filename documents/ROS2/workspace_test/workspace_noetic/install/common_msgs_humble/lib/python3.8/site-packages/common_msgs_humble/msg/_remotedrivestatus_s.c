// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from common_msgs_humble:msg/Remotedrivestatus.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "common_msgs_humble/msg/detail/remotedrivestatus__struct.h"
#include "common_msgs_humble/msg/detail/remotedrivestatus__functions.h"


ROSIDL_GENERATOR_C_EXPORT
bool common_msgs_humble__msg__remotedrivestatus__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[60];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("common_msgs_humble.msg._remotedrivestatus.Remotedrivestatus", full_classname_dest, 59) == 0);
  }
  common_msgs_humble__msg__Remotedrivestatus * ros_message = _ros_message;
  {  // timestamp
    PyObject * field = PyObject_GetAttrString(_pymsg, "timestamp");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->timestamp = PyLong_AsLongLong(field);
    Py_DECREF(field);
  }
  {  // drivemode
    PyObject * field = PyObject_GetAttrString(_pymsg, "drivemode");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->drivemode = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // remotedrivestatus
    PyObject * field = PyObject_GetAttrString(_pymsg, "remotedrivestatus");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->remotedrivestatus = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // systemstatus
    PyObject * field = PyObject_GetAttrString(_pymsg, "systemstatus");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->systemstatus = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * common_msgs_humble__msg__remotedrivestatus__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of Remotedrivestatus */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("common_msgs_humble.msg._remotedrivestatus");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "Remotedrivestatus");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  common_msgs_humble__msg__Remotedrivestatus * ros_message = (common_msgs_humble__msg__Remotedrivestatus *)raw_ros_message;
  {  // timestamp
    PyObject * field = NULL;
    field = PyLong_FromLongLong(ros_message->timestamp);
    {
      int rc = PyObject_SetAttrString(_pymessage, "timestamp", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // drivemode
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->drivemode);
    {
      int rc = PyObject_SetAttrString(_pymessage, "drivemode", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // remotedrivestatus
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->remotedrivestatus);
    {
      int rc = PyObject_SetAttrString(_pymessage, "remotedrivestatus", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // systemstatus
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->systemstatus);
    {
      int rc = PyObject_SetAttrString(_pymessage, "systemstatus", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
