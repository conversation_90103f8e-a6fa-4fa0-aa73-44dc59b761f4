# generated from rosidl_generator_py/resource/_idl.py.em
# with input from common_msgs_humble:msg/Controllon.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_Controllon(type):
    """Metaclass of message 'Controllon'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('common_msgs_humble')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'common_msgs_humble.msg.Controllon')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__controllon
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__controllon
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__controllon
            cls._TYPE_SUPPORT = module.type_support_msg__msg__controllon
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__controllon

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class Controllon(metaclass=Metaclass_Controllon):
    """Message class 'Controllon'."""

    __slots__ = [
        '_espmethod',
        '_gpsdis',
        '_epbmethod',
        '_epb',
        '_geermethod',
        '_gear',
        '_brakemethod',
        '_brakepedal',
        '_gaspedal',
        '_station',
        '_light',
        '_pcmethod',
        '_objdis',
        '_objrel',
        '_mode',
        '_isvalid',
        '_timestamp',
        '_targetspeed',
        '_apadis',
        '_objtype',
    ]

    _fields_and_field_types = {
        'espmethod': 'uint8',
        'gpsdis': 'float',
        'epbmethod': 'uint8',
        'epb': 'uint8',
        'geermethod': 'uint8',
        'gear': 'uint8',
        'brakemethod': 'uint8',
        'brakepedal': 'float',
        'gaspedal': 'float',
        'station': 'uint8',
        'light': 'uint8',
        'pcmethod': 'uint8',
        'objdis': 'float',
        'objrel': 'float',
        'mode': 'uint8',
        'isvalid': 'uint8',
        'timestamp': 'int64',
        'targetspeed': 'float',
        'apadis': 'float',
        'objtype': 'uint8',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('int64'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.espmethod = kwargs.get('espmethod', int())
        self.gpsdis = kwargs.get('gpsdis', float())
        self.epbmethod = kwargs.get('epbmethod', int())
        self.epb = kwargs.get('epb', int())
        self.geermethod = kwargs.get('geermethod', int())
        self.gear = kwargs.get('gear', int())
        self.brakemethod = kwargs.get('brakemethod', int())
        self.brakepedal = kwargs.get('brakepedal', float())
        self.gaspedal = kwargs.get('gaspedal', float())
        self.station = kwargs.get('station', int())
        self.light = kwargs.get('light', int())
        self.pcmethod = kwargs.get('pcmethod', int())
        self.objdis = kwargs.get('objdis', float())
        self.objrel = kwargs.get('objrel', float())
        self.mode = kwargs.get('mode', int())
        self.isvalid = kwargs.get('isvalid', int())
        self.timestamp = kwargs.get('timestamp', int())
        self.targetspeed = kwargs.get('targetspeed', float())
        self.apadis = kwargs.get('apadis', float())
        self.objtype = kwargs.get('objtype', int())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.espmethod != other.espmethod:
            return False
        if self.gpsdis != other.gpsdis:
            return False
        if self.epbmethod != other.epbmethod:
            return False
        if self.epb != other.epb:
            return False
        if self.geermethod != other.geermethod:
            return False
        if self.gear != other.gear:
            return False
        if self.brakemethod != other.brakemethod:
            return False
        if self.brakepedal != other.brakepedal:
            return False
        if self.gaspedal != other.gaspedal:
            return False
        if self.station != other.station:
            return False
        if self.light != other.light:
            return False
        if self.pcmethod != other.pcmethod:
            return False
        if self.objdis != other.objdis:
            return False
        if self.objrel != other.objrel:
            return False
        if self.mode != other.mode:
            return False
        if self.isvalid != other.isvalid:
            return False
        if self.timestamp != other.timestamp:
            return False
        if self.targetspeed != other.targetspeed:
            return False
        if self.apadis != other.apadis:
            return False
        if self.objtype != other.objtype:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def espmethod(self):
        """Message field 'espmethod'."""
        return self._espmethod

    @espmethod.setter
    def espmethod(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'espmethod' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'espmethod' field must be an unsigned integer in [0, 255]"
        self._espmethod = value

    @builtins.property
    def gpsdis(self):
        """Message field 'gpsdis'."""
        return self._gpsdis

    @gpsdis.setter
    def gpsdis(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'gpsdis' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'gpsdis' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._gpsdis = value

    @builtins.property
    def epbmethod(self):
        """Message field 'epbmethod'."""
        return self._epbmethod

    @epbmethod.setter
    def epbmethod(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'epbmethod' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'epbmethod' field must be an unsigned integer in [0, 255]"
        self._epbmethod = value

    @builtins.property
    def epb(self):
        """Message field 'epb'."""
        return self._epb

    @epb.setter
    def epb(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'epb' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'epb' field must be an unsigned integer in [0, 255]"
        self._epb = value

    @builtins.property
    def geermethod(self):
        """Message field 'geermethod'."""
        return self._geermethod

    @geermethod.setter
    def geermethod(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'geermethod' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'geermethod' field must be an unsigned integer in [0, 255]"
        self._geermethod = value

    @builtins.property
    def gear(self):
        """Message field 'gear'."""
        return self._gear

    @gear.setter
    def gear(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'gear' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'gear' field must be an unsigned integer in [0, 255]"
        self._gear = value

    @builtins.property
    def brakemethod(self):
        """Message field 'brakemethod'."""
        return self._brakemethod

    @brakemethod.setter
    def brakemethod(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'brakemethod' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'brakemethod' field must be an unsigned integer in [0, 255]"
        self._brakemethod = value

    @builtins.property
    def brakepedal(self):
        """Message field 'brakepedal'."""
        return self._brakepedal

    @brakepedal.setter
    def brakepedal(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'brakepedal' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'brakepedal' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._brakepedal = value

    @builtins.property
    def gaspedal(self):
        """Message field 'gaspedal'."""
        return self._gaspedal

    @gaspedal.setter
    def gaspedal(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'gaspedal' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'gaspedal' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._gaspedal = value

    @builtins.property
    def station(self):
        """Message field 'station'."""
        return self._station

    @station.setter
    def station(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'station' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'station' field must be an unsigned integer in [0, 255]"
        self._station = value

    @builtins.property
    def light(self):
        """Message field 'light'."""
        return self._light

    @light.setter
    def light(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'light' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'light' field must be an unsigned integer in [0, 255]"
        self._light = value

    @builtins.property
    def pcmethod(self):
        """Message field 'pcmethod'."""
        return self._pcmethod

    @pcmethod.setter
    def pcmethod(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'pcmethod' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'pcmethod' field must be an unsigned integer in [0, 255]"
        self._pcmethod = value

    @builtins.property
    def objdis(self):
        """Message field 'objdis'."""
        return self._objdis

    @objdis.setter
    def objdis(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'objdis' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'objdis' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._objdis = value

    @builtins.property
    def objrel(self):
        """Message field 'objrel'."""
        return self._objrel

    @objrel.setter
    def objrel(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'objrel' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'objrel' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._objrel = value

    @builtins.property
    def mode(self):
        """Message field 'mode'."""
        return self._mode

    @mode.setter
    def mode(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'mode' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'mode' field must be an unsigned integer in [0, 255]"
        self._mode = value

    @builtins.property
    def isvalid(self):
        """Message field 'isvalid'."""
        return self._isvalid

    @isvalid.setter
    def isvalid(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'isvalid' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'isvalid' field must be an unsigned integer in [0, 255]"
        self._isvalid = value

    @builtins.property
    def timestamp(self):
        """Message field 'timestamp'."""
        return self._timestamp

    @timestamp.setter
    def timestamp(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'timestamp' field must be of type 'int'"
            assert value >= -9223372036854775808 and value < 9223372036854775808, \
                "The 'timestamp' field must be an integer in [-9223372036854775808, 9223372036854775807]"
        self._timestamp = value

    @builtins.property
    def targetspeed(self):
        """Message field 'targetspeed'."""
        return self._targetspeed

    @targetspeed.setter
    def targetspeed(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'targetspeed' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'targetspeed' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._targetspeed = value

    @builtins.property
    def apadis(self):
        """Message field 'apadis'."""
        return self._apadis

    @apadis.setter
    def apadis(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'apadis' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'apadis' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._apadis = value

    @builtins.property
    def objtype(self):
        """Message field 'objtype'."""
        return self._objtype

    @objtype.setter
    def objtype(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'objtype' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'objtype' field must be an unsigned integer in [0, 255]"
        self._objtype = value
