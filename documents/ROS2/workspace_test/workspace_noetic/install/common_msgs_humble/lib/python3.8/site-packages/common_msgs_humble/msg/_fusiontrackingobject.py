# generated from rosidl_generator_py/resource/_idl.py.em
# with input from common_msgs_humble:msg/Fusiontrackingobject.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_Fusiontrackingobject(type):
    """Metaclass of message 'Fusiontrackingobject'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('common_msgs_humble')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'common_msgs_humble.msg.Fusiontrackingobject')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__fusiontrackingobject
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__fusiontrackingobject
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__fusiontrackingobject
            cls._TYPE_SUPPORT = module.type_support_msg__msg__fusiontrackingobject
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__fusiontrackingobject

            from common_msgs_humble.msg import Obupant
            if Obupant.__class__._TYPE_SUPPORT is None:
                Obupant.__class__.__import_type_support__()

            from common_msgs_humble.msg import Sensorobject
            if Sensorobject.__class__._TYPE_SUPPORT is None:
                Sensorobject.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class Fusiontrackingobject(metaclass=Metaclass_Fusiontrackingobject):
    """Message class 'Fusiontrackingobject'."""

    __slots__ = [
        '_objectsource',
        '_lidartimestamp',
        '_lidargpstime',
        '_lidarobject',
        '_radartimestamp',
        '_radargpstime',
        '_radarobject',
        '_obutimestamp',
        '_obugpstime',
        '_obuobject',
        '_obupantobject',
    ]

    _fields_and_field_types = {
        'objectsource': 'uint8',
        'lidartimestamp': 'int64',
        'lidargpstime': 'int64',
        'lidarobject': 'common_msgs_humble/Sensorobject',
        'radartimestamp': 'int64',
        'radargpstime': 'int64',
        'radarobject': 'common_msgs_humble/Sensorobject',
        'obutimestamp': 'int64',
        'obugpstime': 'int64',
        'obuobject': 'common_msgs_humble/Sensorobject',
        'obupantobject': 'common_msgs_humble/Obupant',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('int64'),  # noqa: E501
        rosidl_parser.definition.BasicType('int64'),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['common_msgs_humble', 'msg'], 'Sensorobject'),  # noqa: E501
        rosidl_parser.definition.BasicType('int64'),  # noqa: E501
        rosidl_parser.definition.BasicType('int64'),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['common_msgs_humble', 'msg'], 'Sensorobject'),  # noqa: E501
        rosidl_parser.definition.BasicType('int64'),  # noqa: E501
        rosidl_parser.definition.BasicType('int64'),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['common_msgs_humble', 'msg'], 'Sensorobject'),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['common_msgs_humble', 'msg'], 'Obupant'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.objectsource = kwargs.get('objectsource', int())
        self.lidartimestamp = kwargs.get('lidartimestamp', int())
        self.lidargpstime = kwargs.get('lidargpstime', int())
        from common_msgs_humble.msg import Sensorobject
        self.lidarobject = kwargs.get('lidarobject', Sensorobject())
        self.radartimestamp = kwargs.get('radartimestamp', int())
        self.radargpstime = kwargs.get('radargpstime', int())
        from common_msgs_humble.msg import Sensorobject
        self.radarobject = kwargs.get('radarobject', Sensorobject())
        self.obutimestamp = kwargs.get('obutimestamp', int())
        self.obugpstime = kwargs.get('obugpstime', int())
        from common_msgs_humble.msg import Sensorobject
        self.obuobject = kwargs.get('obuobject', Sensorobject())
        from common_msgs_humble.msg import Obupant
        self.obupantobject = kwargs.get('obupantobject', Obupant())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.objectsource != other.objectsource:
            return False
        if self.lidartimestamp != other.lidartimestamp:
            return False
        if self.lidargpstime != other.lidargpstime:
            return False
        if self.lidarobject != other.lidarobject:
            return False
        if self.radartimestamp != other.radartimestamp:
            return False
        if self.radargpstime != other.radargpstime:
            return False
        if self.radarobject != other.radarobject:
            return False
        if self.obutimestamp != other.obutimestamp:
            return False
        if self.obugpstime != other.obugpstime:
            return False
        if self.obuobject != other.obuobject:
            return False
        if self.obupantobject != other.obupantobject:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def objectsource(self):
        """Message field 'objectsource'."""
        return self._objectsource

    @objectsource.setter
    def objectsource(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'objectsource' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'objectsource' field must be an unsigned integer in [0, 255]"
        self._objectsource = value

    @builtins.property
    def lidartimestamp(self):
        """Message field 'lidartimestamp'."""
        return self._lidartimestamp

    @lidartimestamp.setter
    def lidartimestamp(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'lidartimestamp' field must be of type 'int'"
            assert value >= -9223372036854775808 and value < 9223372036854775808, \
                "The 'lidartimestamp' field must be an integer in [-9223372036854775808, 9223372036854775807]"
        self._lidartimestamp = value

    @builtins.property
    def lidargpstime(self):
        """Message field 'lidargpstime'."""
        return self._lidargpstime

    @lidargpstime.setter
    def lidargpstime(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'lidargpstime' field must be of type 'int'"
            assert value >= -9223372036854775808 and value < 9223372036854775808, \
                "The 'lidargpstime' field must be an integer in [-9223372036854775808, 9223372036854775807]"
        self._lidargpstime = value

    @builtins.property
    def lidarobject(self):
        """Message field 'lidarobject'."""
        return self._lidarobject

    @lidarobject.setter
    def lidarobject(self, value):
        if __debug__:
            from common_msgs_humble.msg import Sensorobject
            assert \
                isinstance(value, Sensorobject), \
                "The 'lidarobject' field must be a sub message of type 'Sensorobject'"
        self._lidarobject = value

    @builtins.property
    def radartimestamp(self):
        """Message field 'radartimestamp'."""
        return self._radartimestamp

    @radartimestamp.setter
    def radartimestamp(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'radartimestamp' field must be of type 'int'"
            assert value >= -9223372036854775808 and value < 9223372036854775808, \
                "The 'radartimestamp' field must be an integer in [-9223372036854775808, 9223372036854775807]"
        self._radartimestamp = value

    @builtins.property
    def radargpstime(self):
        """Message field 'radargpstime'."""
        return self._radargpstime

    @radargpstime.setter
    def radargpstime(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'radargpstime' field must be of type 'int'"
            assert value >= -9223372036854775808 and value < 9223372036854775808, \
                "The 'radargpstime' field must be an integer in [-9223372036854775808, 9223372036854775807]"
        self._radargpstime = value

    @builtins.property
    def radarobject(self):
        """Message field 'radarobject'."""
        return self._radarobject

    @radarobject.setter
    def radarobject(self, value):
        if __debug__:
            from common_msgs_humble.msg import Sensorobject
            assert \
                isinstance(value, Sensorobject), \
                "The 'radarobject' field must be a sub message of type 'Sensorobject'"
        self._radarobject = value

    @builtins.property
    def obutimestamp(self):
        """Message field 'obutimestamp'."""
        return self._obutimestamp

    @obutimestamp.setter
    def obutimestamp(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'obutimestamp' field must be of type 'int'"
            assert value >= -9223372036854775808 and value < 9223372036854775808, \
                "The 'obutimestamp' field must be an integer in [-9223372036854775808, 9223372036854775807]"
        self._obutimestamp = value

    @builtins.property
    def obugpstime(self):
        """Message field 'obugpstime'."""
        return self._obugpstime

    @obugpstime.setter
    def obugpstime(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'obugpstime' field must be of type 'int'"
            assert value >= -9223372036854775808 and value < 9223372036854775808, \
                "The 'obugpstime' field must be an integer in [-9223372036854775808, 9223372036854775807]"
        self._obugpstime = value

    @builtins.property
    def obuobject(self):
        """Message field 'obuobject'."""
        return self._obuobject

    @obuobject.setter
    def obuobject(self, value):
        if __debug__:
            from common_msgs_humble.msg import Sensorobject
            assert \
                isinstance(value, Sensorobject), \
                "The 'obuobject' field must be a sub message of type 'Sensorobject'"
        self._obuobject = value

    @builtins.property
    def obupantobject(self):
        """Message field 'obupantobject'."""
        return self._obupantobject

    @obupantobject.setter
    def obupantobject(self, value):
        if __debug__:
            from common_msgs_humble.msg import Obupant
            assert \
                isinstance(value, Obupant), \
                "The 'obupantobject' field must be a sub message of type 'Obupant'"
        self._obupantobject = value
