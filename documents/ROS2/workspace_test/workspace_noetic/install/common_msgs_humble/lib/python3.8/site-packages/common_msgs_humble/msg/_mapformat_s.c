// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from common_msgs_humble:msg/Mapformat.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "common_msgs_humble/msg/detail/mapformat__struct.h"
#include "common_msgs_humble/msg/detail/mapformat__functions.h"


ROSIDL_GENERATOR_C_EXPORT
bool common_msgs_humble__msg__mapformat__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[44];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("common_msgs_humble.msg._mapformat.Mapformat", full_classname_dest, 43) == 0);
  }
  common_msgs_humble__msg__Mapformat * ros_message = _ros_message;
  {  // lon
    PyObject * field = PyObject_GetAttrString(_pymsg, "lon");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->lon = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // lat
    PyObject * field = PyObject_GetAttrString(_pymsg, "lat");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->lat = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // roadtype
    PyObject * field = PyObject_GetAttrString(_pymsg, "roadtype");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->roadtype = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // speed
    PyObject * field = PyObject_GetAttrString(_pymsg, "speed");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->speed = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // lanetype
    PyObject * field = PyObject_GetAttrString(_pymsg, "lanetype");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->lanetype = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // mergelanetype
    PyObject * field = PyObject_GetAttrString(_pymsg, "mergelanetype");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->mergelanetype = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // sensorlanetype
    PyObject * field = PyObject_GetAttrString(_pymsg, "sensorlanetype");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->sensorlanetype = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // turnlight
    PyObject * field = PyObject_GetAttrString(_pymsg, "turnlight");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->turnlight = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // sideroadwidth
    PyObject * field = PyObject_GetAttrString(_pymsg, "sideroadwidth");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->sideroadwidth = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // lanewidth
    PyObject * field = PyObject_GetAttrString(_pymsg, "lanewidth");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->lanewidth = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // leftlanewidth
    PyObject * field = PyObject_GetAttrString(_pymsg, "leftlanewidth");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->leftlanewidth = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // rightlanewidth
    PyObject * field = PyObject_GetAttrString(_pymsg, "rightlanewidth");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->rightlanewidth = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // leftsearchdis
    PyObject * field = PyObject_GetAttrString(_pymsg, "leftsearchdis");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->leftsearchdis = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // rightsearchdis
    PyObject * field = PyObject_GetAttrString(_pymsg, "rightsearchdis");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->rightsearchdis = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // heading
    PyObject * field = PyObject_GetAttrString(_pymsg, "heading");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->heading = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // curvature
    PyObject * field = PyObject_GetAttrString(_pymsg, "curvature");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->curvature = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // gpstime
    PyObject * field = PyObject_GetAttrString(_pymsg, "gpstime");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->gpstime = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // switchflag
    PyObject * field = PyObject_GetAttrString(_pymsg, "switchflag");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->switchflag = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // borrowflag
    PyObject * field = PyObject_GetAttrString(_pymsg, "borrowflag");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->borrowflag = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // lanesum
    PyObject * field = PyObject_GetAttrString(_pymsg, "lanesum");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->lanesum = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // lanenum
    PyObject * field = PyObject_GetAttrString(_pymsg, "lanenum");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->lanenum = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // backup1
    PyObject * field = PyObject_GetAttrString(_pymsg, "backup1");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->backup1 = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // backup2
    PyObject * field = PyObject_GetAttrString(_pymsg, "backup2");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->backup2 = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // backup3
    PyObject * field = PyObject_GetAttrString(_pymsg, "backup3");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->backup3 = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // backup4
    PyObject * field = PyObject_GetAttrString(_pymsg, "backup4");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->backup4 = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // backup5
    PyObject * field = PyObject_GetAttrString(_pymsg, "backup5");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->backup5 = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // backup6
    PyObject * field = PyObject_GetAttrString(_pymsg, "backup6");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->backup6 = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // backup7
    PyObject * field = PyObject_GetAttrString(_pymsg, "backup7");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->backup7 = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // backup8
    PyObject * field = PyObject_GetAttrString(_pymsg, "backup8");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->backup8 = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // backup9
    PyObject * field = PyObject_GetAttrString(_pymsg, "backup9");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->backup9 = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * common_msgs_humble__msg__mapformat__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of Mapformat */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("common_msgs_humble.msg._mapformat");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "Mapformat");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  common_msgs_humble__msg__Mapformat * ros_message = (common_msgs_humble__msg__Mapformat *)raw_ros_message;
  {  // lon
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->lon);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lon", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lat
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->lat);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lat", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // roadtype
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->roadtype);
    {
      int rc = PyObject_SetAttrString(_pymessage, "roadtype", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // speed
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->speed);
    {
      int rc = PyObject_SetAttrString(_pymessage, "speed", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lanetype
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->lanetype);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lanetype", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // mergelanetype
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->mergelanetype);
    {
      int rc = PyObject_SetAttrString(_pymessage, "mergelanetype", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // sensorlanetype
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->sensorlanetype);
    {
      int rc = PyObject_SetAttrString(_pymessage, "sensorlanetype", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // turnlight
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->turnlight);
    {
      int rc = PyObject_SetAttrString(_pymessage, "turnlight", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // sideroadwidth
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->sideroadwidth);
    {
      int rc = PyObject_SetAttrString(_pymessage, "sideroadwidth", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lanewidth
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->lanewidth);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lanewidth", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // leftlanewidth
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->leftlanewidth);
    {
      int rc = PyObject_SetAttrString(_pymessage, "leftlanewidth", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // rightlanewidth
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->rightlanewidth);
    {
      int rc = PyObject_SetAttrString(_pymessage, "rightlanewidth", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // leftsearchdis
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->leftsearchdis);
    {
      int rc = PyObject_SetAttrString(_pymessage, "leftsearchdis", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // rightsearchdis
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->rightsearchdis);
    {
      int rc = PyObject_SetAttrString(_pymessage, "rightsearchdis", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // heading
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->heading);
    {
      int rc = PyObject_SetAttrString(_pymessage, "heading", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // curvature
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->curvature);
    {
      int rc = PyObject_SetAttrString(_pymessage, "curvature", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // gpstime
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->gpstime);
    {
      int rc = PyObject_SetAttrString(_pymessage, "gpstime", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // switchflag
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->switchflag);
    {
      int rc = PyObject_SetAttrString(_pymessage, "switchflag", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // borrowflag
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->borrowflag);
    {
      int rc = PyObject_SetAttrString(_pymessage, "borrowflag", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lanesum
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->lanesum);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lanesum", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lanenum
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->lanenum);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lanenum", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // backup1
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->backup1);
    {
      int rc = PyObject_SetAttrString(_pymessage, "backup1", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // backup2
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->backup2);
    {
      int rc = PyObject_SetAttrString(_pymessage, "backup2", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // backup3
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->backup3);
    {
      int rc = PyObject_SetAttrString(_pymessage, "backup3", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // backup4
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->backup4);
    {
      int rc = PyObject_SetAttrString(_pymessage, "backup4", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // backup5
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->backup5);
    {
      int rc = PyObject_SetAttrString(_pymessage, "backup5", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // backup6
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->backup6);
    {
      int rc = PyObject_SetAttrString(_pymessage, "backup6", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // backup7
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->backup7);
    {
      int rc = PyObject_SetAttrString(_pymessage, "backup7", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // backup8
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->backup8);
    {
      int rc = PyObject_SetAttrString(_pymessage, "backup8", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // backup9
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->backup9);
    {
      int rc = PyObject_SetAttrString(_pymessage, "backup9", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
