// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from common_msgs_humble:msg/Pullover.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "common_msgs_humble/msg/detail/pullover__struct.h"
#include "common_msgs_humble/msg/detail/pullover__functions.h"


ROSIDL_GENERATOR_C_EXPORT
bool common_msgs_humble__msg__pullover__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[42];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("common_msgs_humble.msg._pullover.Pullover", full_classname_dest, 41) == 0);
  }
  common_msgs_humble__msg__Pullover * ros_message = _ros_message;
  {  // timestamp
    PyObject * field = PyObject_GetAttrString(_pymsg, "timestamp");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->timestamp = PyLong_AsLongLong(field);
    Py_DECREF(field);
  }
  {  // po_decision
    PyObject * field = PyObject_GetAttrString(_pymsg, "po_decision");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->po_decision = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // po_button
    PyObject * field = PyObject_GetAttrString(_pymsg, "po_button");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->po_button = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // doorstatus
    PyObject * field = PyObject_GetAttrString(_pymsg, "doorstatus");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->doorstatus = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // reserve
    PyObject * field = PyObject_GetAttrString(_pymsg, "reserve");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->reserve = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * common_msgs_humble__msg__pullover__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of Pullover */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("common_msgs_humble.msg._pullover");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "Pullover");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  common_msgs_humble__msg__Pullover * ros_message = (common_msgs_humble__msg__Pullover *)raw_ros_message;
  {  // timestamp
    PyObject * field = NULL;
    field = PyLong_FromLongLong(ros_message->timestamp);
    {
      int rc = PyObject_SetAttrString(_pymessage, "timestamp", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // po_decision
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->po_decision);
    {
      int rc = PyObject_SetAttrString(_pymessage, "po_decision", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // po_button
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->po_button);
    {
      int rc = PyObject_SetAttrString(_pymessage, "po_button", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // doorstatus
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->doorstatus);
    {
      int rc = PyObject_SetAttrString(_pymessage, "doorstatus", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // reserve
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->reserve);
    {
      int rc = PyObject_SetAttrString(_pymessage, "reserve", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
