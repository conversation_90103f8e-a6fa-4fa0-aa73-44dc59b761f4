# generated from rosidl_generator_py/resource/_idl.py.em
# with input from common_msgs_humble:msg/Rdcontrol.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_Rdcontrol(type):
    """Metaclass of message 'Rdcontrol'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('common_msgs_humble')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'common_msgs_humble.msg.Rdcontrol')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__rdcontrol
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__rdcontrol
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__rdcontrol
            cls._TYPE_SUPPORT = module.type_support_msg__msg__rdcontrol
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__rdcontrol

            from std_msgs.msg import Header
            if Header.__class__._TYPE_SUPPORT is None:
                Header.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class Rdcontrol(metaclass=Metaclass_Rdcontrol):
    """Message class 'Rdcontrol'."""

    __slots__ = [
        '_header',
        '_timestamp',
        '_drivemode',
        '_drivestate',
        '_angle',
        '_gas',
        '_brake',
        '_turnlignt',
        '_gear',
        '_epb',
    ]

    _fields_and_field_types = {
        'header': 'std_msgs/Header',
        'timestamp': 'int64',
        'drivemode': 'uint8',
        'drivestate': 'uint8',
        'angle': 'float',
        'gas': 'float',
        'brake': 'float',
        'turnlignt': 'uint8',
        'gear': 'uint8',
        'epb': 'uint8',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.NamespacedType(['std_msgs', 'msg'], 'Header'),  # noqa: E501
        rosidl_parser.definition.BasicType('int64'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        from std_msgs.msg import Header
        self.header = kwargs.get('header', Header())
        self.timestamp = kwargs.get('timestamp', int())
        self.drivemode = kwargs.get('drivemode', int())
        self.drivestate = kwargs.get('drivestate', int())
        self.angle = kwargs.get('angle', float())
        self.gas = kwargs.get('gas', float())
        self.brake = kwargs.get('brake', float())
        self.turnlignt = kwargs.get('turnlignt', int())
        self.gear = kwargs.get('gear', int())
        self.epb = kwargs.get('epb', int())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.header != other.header:
            return False
        if self.timestamp != other.timestamp:
            return False
        if self.drivemode != other.drivemode:
            return False
        if self.drivestate != other.drivestate:
            return False
        if self.angle != other.angle:
            return False
        if self.gas != other.gas:
            return False
        if self.brake != other.brake:
            return False
        if self.turnlignt != other.turnlignt:
            return False
        if self.gear != other.gear:
            return False
        if self.epb != other.epb:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def header(self):
        """Message field 'header'."""
        return self._header

    @header.setter
    def header(self, value):
        if __debug__:
            from std_msgs.msg import Header
            assert \
                isinstance(value, Header), \
                "The 'header' field must be a sub message of type 'Header'"
        self._header = value

    @builtins.property
    def timestamp(self):
        """Message field 'timestamp'."""
        return self._timestamp

    @timestamp.setter
    def timestamp(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'timestamp' field must be of type 'int'"
            assert value >= -9223372036854775808 and value < 9223372036854775808, \
                "The 'timestamp' field must be an integer in [-9223372036854775808, 9223372036854775807]"
        self._timestamp = value

    @builtins.property
    def drivemode(self):
        """Message field 'drivemode'."""
        return self._drivemode

    @drivemode.setter
    def drivemode(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'drivemode' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'drivemode' field must be an unsigned integer in [0, 255]"
        self._drivemode = value

    @builtins.property
    def drivestate(self):
        """Message field 'drivestate'."""
        return self._drivestate

    @drivestate.setter
    def drivestate(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'drivestate' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'drivestate' field must be an unsigned integer in [0, 255]"
        self._drivestate = value

    @builtins.property
    def angle(self):
        """Message field 'angle'."""
        return self._angle

    @angle.setter
    def angle(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'angle' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'angle' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._angle = value

    @builtins.property
    def gas(self):
        """Message field 'gas'."""
        return self._gas

    @gas.setter
    def gas(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'gas' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'gas' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._gas = value

    @builtins.property
    def brake(self):
        """Message field 'brake'."""
        return self._brake

    @brake.setter
    def brake(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'brake' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'brake' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._brake = value

    @builtins.property
    def turnlignt(self):
        """Message field 'turnlignt'."""
        return self._turnlignt

    @turnlignt.setter
    def turnlignt(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'turnlignt' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'turnlignt' field must be an unsigned integer in [0, 255]"
        self._turnlignt = value

    @builtins.property
    def gear(self):
        """Message field 'gear'."""
        return self._gear

    @gear.setter
    def gear(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'gear' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'gear' field must be an unsigned integer in [0, 255]"
        self._gear = value

    @builtins.property
    def epb(self):
        """Message field 'epb'."""
        return self._epb

    @epb.setter
    def epb(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'epb' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'epb' field must be an unsigned integer in [0, 255]"
        self._epb = value
