# generated from rosidl_generator_py/resource/_idl.py.em
# with input from common_msgs_humble:msg/Objecthistory.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_Objecthistory(type):
    """Metaclass of message 'Objecthistory'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('common_msgs_humble')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'common_msgs_humble.msg.Objecthistory')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__objecthistory
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__objecthistory
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__objecthistory
            cls._TYPE_SUPPORT = module.type_support_msg__msg__objecthistory
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__objecthistory

            from common_msgs_humble.msg import Point3d
            if Point3d.__class__._TYPE_SUPPORT is None:
                Point3d.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class Objecthistory(metaclass=Metaclass_Objecthistory):
    """Message class 'Objecthistory'."""

    __slots__ = [
        '_timestamp',
        '_trajectorypoint',
        '_lon',
        '_lat',
        '_alt',
        '_roll',
        '_pitch',
        '_heading',
        '_relavx',
        '_relavy',
        '_absvx',
        '_absvy',
        '_s',
        '_l',
        '_speeds',
        '_speedl',
    ]

    _fields_and_field_types = {
        'timestamp': 'int64',
        'trajectorypoint': 'common_msgs_humble/Point3d',
        'lon': 'double',
        'lat': 'double',
        'alt': 'double',
        'roll': 'float',
        'pitch': 'float',
        'heading': 'float',
        'relavx': 'float',
        'relavy': 'float',
        'absvx': 'float',
        'absvy': 'float',
        's': 'float',
        'l': 'float',
        'speeds': 'float',
        'speedl': 'float',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('int64'),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['common_msgs_humble', 'msg'], 'Point3d'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.timestamp = kwargs.get('timestamp', int())
        from common_msgs_humble.msg import Point3d
        self.trajectorypoint = kwargs.get('trajectorypoint', Point3d())
        self.lon = kwargs.get('lon', float())
        self.lat = kwargs.get('lat', float())
        self.alt = kwargs.get('alt', float())
        self.roll = kwargs.get('roll', float())
        self.pitch = kwargs.get('pitch', float())
        self.heading = kwargs.get('heading', float())
        self.relavx = kwargs.get('relavx', float())
        self.relavy = kwargs.get('relavy', float())
        self.absvx = kwargs.get('absvx', float())
        self.absvy = kwargs.get('absvy', float())
        self.s = kwargs.get('s', float())
        self.l = kwargs.get('l', float())
        self.speeds = kwargs.get('speeds', float())
        self.speedl = kwargs.get('speedl', float())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.timestamp != other.timestamp:
            return False
        if self.trajectorypoint != other.trajectorypoint:
            return False
        if self.lon != other.lon:
            return False
        if self.lat != other.lat:
            return False
        if self.alt != other.alt:
            return False
        if self.roll != other.roll:
            return False
        if self.pitch != other.pitch:
            return False
        if self.heading != other.heading:
            return False
        if self.relavx != other.relavx:
            return False
        if self.relavy != other.relavy:
            return False
        if self.absvx != other.absvx:
            return False
        if self.absvy != other.absvy:
            return False
        if self.s != other.s:
            return False
        if self.l != other.l:
            return False
        if self.speeds != other.speeds:
            return False
        if self.speedl != other.speedl:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def timestamp(self):
        """Message field 'timestamp'."""
        return self._timestamp

    @timestamp.setter
    def timestamp(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'timestamp' field must be of type 'int'"
            assert value >= -9223372036854775808 and value < 9223372036854775808, \
                "The 'timestamp' field must be an integer in [-9223372036854775808, 9223372036854775807]"
        self._timestamp = value

    @builtins.property
    def trajectorypoint(self):
        """Message field 'trajectorypoint'."""
        return self._trajectorypoint

    @trajectorypoint.setter
    def trajectorypoint(self, value):
        if __debug__:
            from common_msgs_humble.msg import Point3d
            assert \
                isinstance(value, Point3d), \
                "The 'trajectorypoint' field must be a sub message of type 'Point3d'"
        self._trajectorypoint = value

    @builtins.property
    def lon(self):
        """Message field 'lon'."""
        return self._lon

    @lon.setter
    def lon(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'lon' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'lon' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._lon = value

    @builtins.property
    def lat(self):
        """Message field 'lat'."""
        return self._lat

    @lat.setter
    def lat(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'lat' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'lat' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._lat = value

    @builtins.property
    def alt(self):
        """Message field 'alt'."""
        return self._alt

    @alt.setter
    def alt(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'alt' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'alt' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._alt = value

    @builtins.property
    def roll(self):
        """Message field 'roll'."""
        return self._roll

    @roll.setter
    def roll(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'roll' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'roll' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._roll = value

    @builtins.property
    def pitch(self):
        """Message field 'pitch'."""
        return self._pitch

    @pitch.setter
    def pitch(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'pitch' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'pitch' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._pitch = value

    @builtins.property
    def heading(self):
        """Message field 'heading'."""
        return self._heading

    @heading.setter
    def heading(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'heading' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'heading' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._heading = value

    @builtins.property
    def relavx(self):
        """Message field 'relavx'."""
        return self._relavx

    @relavx.setter
    def relavx(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'relavx' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'relavx' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._relavx = value

    @builtins.property
    def relavy(self):
        """Message field 'relavy'."""
        return self._relavy

    @relavy.setter
    def relavy(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'relavy' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'relavy' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._relavy = value

    @builtins.property
    def absvx(self):
        """Message field 'absvx'."""
        return self._absvx

    @absvx.setter
    def absvx(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'absvx' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'absvx' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._absvx = value

    @builtins.property
    def absvy(self):
        """Message field 'absvy'."""
        return self._absvy

    @absvy.setter
    def absvy(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'absvy' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'absvy' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._absvy = value

    @builtins.property
    def s(self):
        """Message field 's'."""
        return self._s

    @s.setter
    def s(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 's' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 's' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._s = value

    @builtins.property
    def l(self):
        """Message field 'l'."""
        return self._l

    @l.setter
    def l(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'l' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'l' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._l = value

    @builtins.property
    def speeds(self):
        """Message field 'speeds'."""
        return self._speeds

    @speeds.setter
    def speeds(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'speeds' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'speeds' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._speeds = value

    @builtins.property
    def speedl(self):
        """Message field 'speedl'."""
        return self._speedl

    @speedl.setter
    def speedl(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'speedl' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'speedl' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._speedl = value
