# generated from rosidl_generator_py/resource/_idl.py.em
# with input from common_msgs_humble:msg/Obutrafficlights.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_Obutrafficlights(type):
    """Metaclass of message 'Obutrafficlights'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('common_msgs_humble')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'common_msgs_humble.msg.Obutrafficlights')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__obutrafficlights
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__obutrafficlights
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__obutrafficlights
            cls._TYPE_SUPPORT = module.type_support_msg__msg__obutrafficlights
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__obutrafficlights

            from common_msgs_humble.msg import Obulight
            if Obulight.__class__._TYPE_SUPPORT is None:
                Obulight.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class Obutrafficlights(metaclass=Metaclass_Obutrafficlights):
    """Message class 'Obutrafficlights'."""

    __slots__ = [
        '_timestamp',
        '_region_id',
        '_node_id',
        '_light_status',
        '_phase_cnt',
        '_light',
    ]

    _fields_and_field_types = {
        'timestamp': 'int64',
        'region_id': 'uint32',
        'node_id': 'uint32',
        'light_status': 'uint8',
        'phase_cnt': 'uint8',
        'light': 'sequence<common_msgs_humble/Obulight>',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('int64'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint32'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint32'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['common_msgs_humble', 'msg'], 'Obulight')),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.timestamp = kwargs.get('timestamp', int())
        self.region_id = kwargs.get('region_id', int())
        self.node_id = kwargs.get('node_id', int())
        self.light_status = kwargs.get('light_status', int())
        self.phase_cnt = kwargs.get('phase_cnt', int())
        self.light = kwargs.get('light', [])

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.timestamp != other.timestamp:
            return False
        if self.region_id != other.region_id:
            return False
        if self.node_id != other.node_id:
            return False
        if self.light_status != other.light_status:
            return False
        if self.phase_cnt != other.phase_cnt:
            return False
        if self.light != other.light:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def timestamp(self):
        """Message field 'timestamp'."""
        return self._timestamp

    @timestamp.setter
    def timestamp(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'timestamp' field must be of type 'int'"
            assert value >= -9223372036854775808 and value < 9223372036854775808, \
                "The 'timestamp' field must be an integer in [-9223372036854775808, 9223372036854775807]"
        self._timestamp = value

    @builtins.property
    def region_id(self):
        """Message field 'region_id'."""
        return self._region_id

    @region_id.setter
    def region_id(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'region_id' field must be of type 'int'"
            assert value >= 0 and value < 4294967296, \
                "The 'region_id' field must be an unsigned integer in [0, 4294967295]"
        self._region_id = value

    @builtins.property
    def node_id(self):
        """Message field 'node_id'."""
        return self._node_id

    @node_id.setter
    def node_id(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'node_id' field must be of type 'int'"
            assert value >= 0 and value < 4294967296, \
                "The 'node_id' field must be an unsigned integer in [0, 4294967295]"
        self._node_id = value

    @builtins.property
    def light_status(self):
        """Message field 'light_status'."""
        return self._light_status

    @light_status.setter
    def light_status(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'light_status' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'light_status' field must be an unsigned integer in [0, 255]"
        self._light_status = value

    @builtins.property
    def phase_cnt(self):
        """Message field 'phase_cnt'."""
        return self._phase_cnt

    @phase_cnt.setter
    def phase_cnt(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'phase_cnt' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'phase_cnt' field must be an unsigned integer in [0, 255]"
        self._phase_cnt = value

    @builtins.property
    def light(self):
        """Message field 'light'."""
        return self._light

    @light.setter
    def light(self, value):
        if __debug__:
            from common_msgs_humble.msg import Obulight
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, Obulight) for v in value) and
                 True), \
                "The 'light' field must be a set or sequence and each value of type 'Obulight'"
        self._light = value
