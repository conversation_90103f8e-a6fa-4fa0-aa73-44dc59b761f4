// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from common_msgs_humble:msg/Controllat.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "common_msgs_humble/msg/detail/controllat__struct.h"
#include "common_msgs_humble/msg/detail/controllat__functions.h"


ROSIDL_GENERATOR_C_EXPORT
bool common_msgs_humble__msg__controllat__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[46];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("common_msgs_humble.msg._controllat.Controllat", full_classname_dest, 45) == 0);
  }
  common_msgs_humble__msg__Controllat * ros_message = _ros_message;
  {  // epsmethod
    PyObject * field = PyObject_GetAttrString(_pymsg, "epsmethod");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->epsmethod = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // epsangle
    PyObject * field = PyObject_GetAttrString(_pymsg, "epsangle");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->epsangle = (int16_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // limitspeed
    PyObject * field = PyObject_GetAttrString(_pymsg, "limitspeed");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->limitspeed = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // epstorque
    PyObject * field = PyObject_GetAttrString(_pymsg, "epstorque");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->epstorque = (int16_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // lightmethod
    PyObject * field = PyObject_GetAttrString(_pymsg, "lightmethod");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->lightmethod = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // lights
    PyObject * field = PyObject_GetAttrString(_pymsg, "lights");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->lights = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // isvalid
    PyObject * field = PyObject_GetAttrString(_pymsg, "isvalid");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->isvalid = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // deviation
    PyObject * field = PyObject_GetAttrString(_pymsg, "deviation");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->deviation = (int16_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // timestamp
    PyObject * field = PyObject_GetAttrString(_pymsg, "timestamp");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->timestamp = PyLong_AsLongLong(field);
    Py_DECREF(field);
  }
  {  // apavstatus
    PyObject * field = PyObject_GetAttrString(_pymsg, "apavstatus");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->apavstatus = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // apsstate
    PyObject * field = PyObject_GetAttrString(_pymsg, "apsstate");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->apsstate = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // curve
    PyObject * field = PyObject_GetAttrString(_pymsg, "curve");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->curve = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * common_msgs_humble__msg__controllat__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of Controllat */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("common_msgs_humble.msg._controllat");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "Controllat");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  common_msgs_humble__msg__Controllat * ros_message = (common_msgs_humble__msg__Controllat *)raw_ros_message;
  {  // epsmethod
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->epsmethod);
    {
      int rc = PyObject_SetAttrString(_pymessage, "epsmethod", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // epsangle
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->epsangle);
    {
      int rc = PyObject_SetAttrString(_pymessage, "epsangle", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // limitspeed
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->limitspeed);
    {
      int rc = PyObject_SetAttrString(_pymessage, "limitspeed", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // epstorque
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->epstorque);
    {
      int rc = PyObject_SetAttrString(_pymessage, "epstorque", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lightmethod
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->lightmethod);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lightmethod", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lights
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->lights);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lights", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // isvalid
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->isvalid);
    {
      int rc = PyObject_SetAttrString(_pymessage, "isvalid", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // deviation
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->deviation);
    {
      int rc = PyObject_SetAttrString(_pymessage, "deviation", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // timestamp
    PyObject * field = NULL;
    field = PyLong_FromLongLong(ros_message->timestamp);
    {
      int rc = PyObject_SetAttrString(_pymessage, "timestamp", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // apavstatus
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->apavstatus);
    {
      int rc = PyObject_SetAttrString(_pymessage, "apavstatus", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // apsstate
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->apsstate);
    {
      int rc = PyObject_SetAttrString(_pymessage, "apsstate", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // curve
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->curve);
    {
      int rc = PyObject_SetAttrString(_pymessage, "curve", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
