# generated from rosidl_generator_py/resource/_idl.py.em
# with input from common_msgs_humble:msg/Roadpoint.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_Roadpoint(type):
    """Metaclass of message 'Roadpoint'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('common_msgs_humble')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'common_msgs_humble.msg.Roadpoint')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__roadpoint
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__roadpoint
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__roadpoint
            cls._TYPE_SUPPORT = module.type_support_msg__msg__roadpoint
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__roadpoint

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class Roadpoint(metaclass=Metaclass_Roadpoint):
    """Message class 'Roadpoint'."""

    __slots__ = [
        '_x',
        '_y',
        '_gx',
        '_gy',
        '_roadtype',
        '_speed',
        '_a',
        '_jerk',
        '_lanetype',
        '_turnlight',
        '_mergelanetype',
        '_sensorlanetype',
        '_heading',
        '_curvature',
        '_dkappa',
        '_ddkappa',
        '_leftsearchdis',
        '_rightsearchdis',
        '_s',
        '_sideroadwidth',
        '_lanewidth',
        '_leftlanewidth',
        '_rightlanewidth',
        '_relativetime',
        '_laneswitch',
        '_laneborrow',
        '_lanenum',
        '_lanesite',
    ]

    _fields_and_field_types = {
        'x': 'float',
        'y': 'float',
        'gx': 'double',
        'gy': 'double',
        'roadtype': 'uint8',
        'speed': 'float',
        'a': 'float',
        'jerk': 'float',
        'lanetype': 'uint8',
        'turnlight': 'uint8',
        'mergelanetype': 'uint8',
        'sensorlanetype': 'uint8',
        'heading': 'float',
        'curvature': 'float',
        'dkappa': 'float',
        'ddkappa': 'float',
        'leftsearchdis': 'float',
        'rightsearchdis': 'float',
        's': 'double',
        'sideroadwidth': 'float',
        'lanewidth': 'float',
        'leftlanewidth': 'float',
        'rightlanewidth': 'float',
        'relativetime': 'float',
        'laneswitch': 'uint8',
        'laneborrow': 'uint8',
        'lanenum': 'uint8',
        'lanesite': 'uint8',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.x = kwargs.get('x', float())
        self.y = kwargs.get('y', float())
        self.gx = kwargs.get('gx', float())
        self.gy = kwargs.get('gy', float())
        self.roadtype = kwargs.get('roadtype', int())
        self.speed = kwargs.get('speed', float())
        self.a = kwargs.get('a', float())
        self.jerk = kwargs.get('jerk', float())
        self.lanetype = kwargs.get('lanetype', int())
        self.turnlight = kwargs.get('turnlight', int())
        self.mergelanetype = kwargs.get('mergelanetype', int())
        self.sensorlanetype = kwargs.get('sensorlanetype', int())
        self.heading = kwargs.get('heading', float())
        self.curvature = kwargs.get('curvature', float())
        self.dkappa = kwargs.get('dkappa', float())
        self.ddkappa = kwargs.get('ddkappa', float())
        self.leftsearchdis = kwargs.get('leftsearchdis', float())
        self.rightsearchdis = kwargs.get('rightsearchdis', float())
        self.s = kwargs.get('s', float())
        self.sideroadwidth = kwargs.get('sideroadwidth', float())
        self.lanewidth = kwargs.get('lanewidth', float())
        self.leftlanewidth = kwargs.get('leftlanewidth', float())
        self.rightlanewidth = kwargs.get('rightlanewidth', float())
        self.relativetime = kwargs.get('relativetime', float())
        self.laneswitch = kwargs.get('laneswitch', int())
        self.laneborrow = kwargs.get('laneborrow', int())
        self.lanenum = kwargs.get('lanenum', int())
        self.lanesite = kwargs.get('lanesite', int())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.x != other.x:
            return False
        if self.y != other.y:
            return False
        if self.gx != other.gx:
            return False
        if self.gy != other.gy:
            return False
        if self.roadtype != other.roadtype:
            return False
        if self.speed != other.speed:
            return False
        if self.a != other.a:
            return False
        if self.jerk != other.jerk:
            return False
        if self.lanetype != other.lanetype:
            return False
        if self.turnlight != other.turnlight:
            return False
        if self.mergelanetype != other.mergelanetype:
            return False
        if self.sensorlanetype != other.sensorlanetype:
            return False
        if self.heading != other.heading:
            return False
        if self.curvature != other.curvature:
            return False
        if self.dkappa != other.dkappa:
            return False
        if self.ddkappa != other.ddkappa:
            return False
        if self.leftsearchdis != other.leftsearchdis:
            return False
        if self.rightsearchdis != other.rightsearchdis:
            return False
        if self.s != other.s:
            return False
        if self.sideroadwidth != other.sideroadwidth:
            return False
        if self.lanewidth != other.lanewidth:
            return False
        if self.leftlanewidth != other.leftlanewidth:
            return False
        if self.rightlanewidth != other.rightlanewidth:
            return False
        if self.relativetime != other.relativetime:
            return False
        if self.laneswitch != other.laneswitch:
            return False
        if self.laneborrow != other.laneborrow:
            return False
        if self.lanenum != other.lanenum:
            return False
        if self.lanesite != other.lanesite:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def x(self):
        """Message field 'x'."""
        return self._x

    @x.setter
    def x(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'x' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'x' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._x = value

    @builtins.property
    def y(self):
        """Message field 'y'."""
        return self._y

    @y.setter
    def y(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'y' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'y' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._y = value

    @builtins.property
    def gx(self):
        """Message field 'gx'."""
        return self._gx

    @gx.setter
    def gx(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'gx' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'gx' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._gx = value

    @builtins.property
    def gy(self):
        """Message field 'gy'."""
        return self._gy

    @gy.setter
    def gy(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'gy' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'gy' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._gy = value

    @builtins.property
    def roadtype(self):
        """Message field 'roadtype'."""
        return self._roadtype

    @roadtype.setter
    def roadtype(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'roadtype' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'roadtype' field must be an unsigned integer in [0, 255]"
        self._roadtype = value

    @builtins.property
    def speed(self):
        """Message field 'speed'."""
        return self._speed

    @speed.setter
    def speed(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'speed' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'speed' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._speed = value

    @builtins.property
    def a(self):
        """Message field 'a'."""
        return self._a

    @a.setter
    def a(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'a' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'a' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._a = value

    @builtins.property
    def jerk(self):
        """Message field 'jerk'."""
        return self._jerk

    @jerk.setter
    def jerk(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'jerk' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'jerk' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._jerk = value

    @builtins.property
    def lanetype(self):
        """Message field 'lanetype'."""
        return self._lanetype

    @lanetype.setter
    def lanetype(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'lanetype' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'lanetype' field must be an unsigned integer in [0, 255]"
        self._lanetype = value

    @builtins.property
    def turnlight(self):
        """Message field 'turnlight'."""
        return self._turnlight

    @turnlight.setter
    def turnlight(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'turnlight' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'turnlight' field must be an unsigned integer in [0, 255]"
        self._turnlight = value

    @builtins.property
    def mergelanetype(self):
        """Message field 'mergelanetype'."""
        return self._mergelanetype

    @mergelanetype.setter
    def mergelanetype(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'mergelanetype' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'mergelanetype' field must be an unsigned integer in [0, 255]"
        self._mergelanetype = value

    @builtins.property
    def sensorlanetype(self):
        """Message field 'sensorlanetype'."""
        return self._sensorlanetype

    @sensorlanetype.setter
    def sensorlanetype(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'sensorlanetype' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'sensorlanetype' field must be an unsigned integer in [0, 255]"
        self._sensorlanetype = value

    @builtins.property
    def heading(self):
        """Message field 'heading'."""
        return self._heading

    @heading.setter
    def heading(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'heading' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'heading' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._heading = value

    @builtins.property
    def curvature(self):
        """Message field 'curvature'."""
        return self._curvature

    @curvature.setter
    def curvature(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'curvature' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'curvature' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._curvature = value

    @builtins.property
    def dkappa(self):
        """Message field 'dkappa'."""
        return self._dkappa

    @dkappa.setter
    def dkappa(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'dkappa' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'dkappa' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._dkappa = value

    @builtins.property
    def ddkappa(self):
        """Message field 'ddkappa'."""
        return self._ddkappa

    @ddkappa.setter
    def ddkappa(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'ddkappa' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'ddkappa' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._ddkappa = value

    @builtins.property
    def leftsearchdis(self):
        """Message field 'leftsearchdis'."""
        return self._leftsearchdis

    @leftsearchdis.setter
    def leftsearchdis(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'leftsearchdis' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'leftsearchdis' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._leftsearchdis = value

    @builtins.property
    def rightsearchdis(self):
        """Message field 'rightsearchdis'."""
        return self._rightsearchdis

    @rightsearchdis.setter
    def rightsearchdis(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'rightsearchdis' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'rightsearchdis' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._rightsearchdis = value

    @builtins.property
    def s(self):
        """Message field 's'."""
        return self._s

    @s.setter
    def s(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 's' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 's' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._s = value

    @builtins.property
    def sideroadwidth(self):
        """Message field 'sideroadwidth'."""
        return self._sideroadwidth

    @sideroadwidth.setter
    def sideroadwidth(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'sideroadwidth' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'sideroadwidth' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._sideroadwidth = value

    @builtins.property
    def lanewidth(self):
        """Message field 'lanewidth'."""
        return self._lanewidth

    @lanewidth.setter
    def lanewidth(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'lanewidth' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'lanewidth' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._lanewidth = value

    @builtins.property
    def leftlanewidth(self):
        """Message field 'leftlanewidth'."""
        return self._leftlanewidth

    @leftlanewidth.setter
    def leftlanewidth(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'leftlanewidth' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'leftlanewidth' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._leftlanewidth = value

    @builtins.property
    def rightlanewidth(self):
        """Message field 'rightlanewidth'."""
        return self._rightlanewidth

    @rightlanewidth.setter
    def rightlanewidth(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'rightlanewidth' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'rightlanewidth' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._rightlanewidth = value

    @builtins.property
    def relativetime(self):
        """Message field 'relativetime'."""
        return self._relativetime

    @relativetime.setter
    def relativetime(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'relativetime' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'relativetime' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._relativetime = value

    @builtins.property
    def laneswitch(self):
        """Message field 'laneswitch'."""
        return self._laneswitch

    @laneswitch.setter
    def laneswitch(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'laneswitch' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'laneswitch' field must be an unsigned integer in [0, 255]"
        self._laneswitch = value

    @builtins.property
    def laneborrow(self):
        """Message field 'laneborrow'."""
        return self._laneborrow

    @laneborrow.setter
    def laneborrow(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'laneborrow' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'laneborrow' field must be an unsigned integer in [0, 255]"
        self._laneborrow = value

    @builtins.property
    def lanenum(self):
        """Message field 'lanenum'."""
        return self._lanenum

    @lanenum.setter
    def lanenum(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'lanenum' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'lanenum' field must be an unsigned integer in [0, 255]"
        self._lanenum = value

    @builtins.property
    def lanesite(self):
        """Message field 'lanesite'."""
        return self._lanesite

    @lanesite.setter
    def lanesite(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'lanesite' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'lanesite' field must be an unsigned integer in [0, 255]"
        self._lanesite = value
