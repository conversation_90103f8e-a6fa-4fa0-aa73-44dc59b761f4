// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from common_msgs_humble:msg/Sensorobject.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "common_msgs_humble/msg/detail/sensorobject__struct.h"
#include "common_msgs_humble/msg/detail/sensorobject__functions.h"

#include "rosidl_runtime_c/primitives_sequence.h"
#include "rosidl_runtime_c/primitives_sequence_functions.h"

// Nested array functions includes
#include "common_msgs_humble/msg/detail/objecthistory__functions.h"
#include "common_msgs_humble/msg/detail/objectprediction__functions.h"
#include "common_msgs_humble/msg/detail/point3d__functions.h"
// end nested array functions include
bool common_msgs_humble__msg__point3d__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * common_msgs_humble__msg__point3d__convert_to_py(void * raw_ros_message);
bool common_msgs_humble__msg__objecthistory__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * common_msgs_humble__msg__objecthistory__convert_to_py(void * raw_ros_message);
bool common_msgs_humble__msg__objectprediction__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * common_msgs_humble__msg__objectprediction__convert_to_py(void * raw_ros_message);

ROSIDL_GENERATOR_C_EXPORT
bool common_msgs_humble__msg__sensorobject__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[50];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("common_msgs_humble.msg._sensorobject.Sensorobject", full_classname_dest, 49) == 0);
  }
  common_msgs_humble__msg__Sensorobject * ros_message = _ros_message;
  {  // id
    PyObject * field = PyObject_GetAttrString(_pymsg, "id");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->id = PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // x
    PyObject * field = PyObject_GetAttrString(_pymsg, "x");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->x = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // y
    PyObject * field = PyObject_GetAttrString(_pymsg, "y");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->y = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // z
    PyObject * field = PyObject_GetAttrString(_pymsg, "z");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->z = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // longtitude
    PyObject * field = PyObject_GetAttrString(_pymsg, "longtitude");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->longtitude = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // latitude
    PyObject * field = PyObject_GetAttrString(_pymsg, "latitude");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->latitude = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // altitude
    PyObject * field = PyObject_GetAttrString(_pymsg, "altitude");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->altitude = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // relspeedy
    PyObject * field = PyObject_GetAttrString(_pymsg, "relspeedy");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->relspeedy = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // relspeedx
    PyObject * field = PyObject_GetAttrString(_pymsg, "relspeedx");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->relspeedx = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // rollrad
    PyObject * field = PyObject_GetAttrString(_pymsg, "rollrad");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->rollrad = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // pitchrad
    PyObject * field = PyObject_GetAttrString(_pymsg, "pitchrad");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->pitchrad = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // azimuth
    PyObject * field = PyObject_GetAttrString(_pymsg, "azimuth");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->azimuth = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // pitchrate
    PyObject * field = PyObject_GetAttrString(_pymsg, "pitchrate");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->pitchrate = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // rollrate
    PyObject * field = PyObject_GetAttrString(_pymsg, "rollrate");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->rollrate = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // yawrate
    PyObject * field = PyObject_GetAttrString(_pymsg, "yawrate");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->yawrate = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // width
    PyObject * field = PyObject_GetAttrString(_pymsg, "width");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->width = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // length
    PyObject * field = PyObject_GetAttrString(_pymsg, "length");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->length = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // height
    PyObject * field = PyObject_GetAttrString(_pymsg, "height");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->height = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // classification
    PyObject * field = PyObject_GetAttrString(_pymsg, "classification");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->classification = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // value
    PyObject * field = PyObject_GetAttrString(_pymsg, "value");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->value = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // confidence
    PyObject * field = PyObject_GetAttrString(_pymsg, "confidence");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->confidence = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // points
    PyObject * field = PyObject_GetAttrString(_pymsg, "points");
    if (!field) {
      return false;
    }
    PyObject * seq_field = PySequence_Fast(field, "expected a sequence in 'points'");
    if (!seq_field) {
      Py_DECREF(field);
      return false;
    }
    Py_ssize_t size = PySequence_Size(field);
    if (-1 == size) {
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    if (!common_msgs_humble__msg__Point3d__Sequence__init(&(ros_message->points), size)) {
      PyErr_SetString(PyExc_RuntimeError, "unable to create common_msgs_humble__msg__Point3d__Sequence ros_message");
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    common_msgs_humble__msg__Point3d * dest = ros_message->points.data;
    for (Py_ssize_t i = 0; i < size; ++i) {
      if (!common_msgs_humble__msg__point3d__convert_from_py(PySequence_Fast_GET_ITEM(seq_field, i), &dest[i])) {
        Py_DECREF(seq_field);
        Py_DECREF(field);
        return false;
      }
    }
    Py_DECREF(seq_field);
    Py_DECREF(field);
  }
  {  // driving_intent
    PyObject * field = PyObject_GetAttrString(_pymsg, "driving_intent");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->driving_intent = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // behavior_state
    PyObject * field = PyObject_GetAttrString(_pymsg, "behavior_state");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->behavior_state = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // radarindex
    PyObject * field = PyObject_GetAttrString(_pymsg, "radarindex");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->radarindex = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // radarobjectid
    PyObject * field = PyObject_GetAttrString(_pymsg, "radarobjectid");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->radarobjectid = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // s
    PyObject * field = PyObject_GetAttrString(_pymsg, "s");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->s = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // l
    PyObject * field = PyObject_GetAttrString(_pymsg, "l");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->l = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // speeds
    PyObject * field = PyObject_GetAttrString(_pymsg, "speeds");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->speeds = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // speedl
    PyObject * field = PyObject_GetAttrString(_pymsg, "speedl");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->speedl = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // object_decision
    PyObject * field = PyObject_GetAttrString(_pymsg, "object_decision");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->object_decision = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // object_history
    PyObject * field = PyObject_GetAttrString(_pymsg, "object_history");
    if (!field) {
      return false;
    }
    PyObject * seq_field = PySequence_Fast(field, "expected a sequence in 'object_history'");
    if (!seq_field) {
      Py_DECREF(field);
      return false;
    }
    Py_ssize_t size = PySequence_Size(field);
    if (-1 == size) {
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    if (!common_msgs_humble__msg__Objecthistory__Sequence__init(&(ros_message->object_history), size)) {
      PyErr_SetString(PyExc_RuntimeError, "unable to create common_msgs_humble__msg__Objecthistory__Sequence ros_message");
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    common_msgs_humble__msg__Objecthistory * dest = ros_message->object_history.data;
    for (Py_ssize_t i = 0; i < size; ++i) {
      if (!common_msgs_humble__msg__objecthistory__convert_from_py(PySequence_Fast_GET_ITEM(seq_field, i), &dest[i])) {
        Py_DECREF(seq_field);
        Py_DECREF(field);
        return false;
      }
    }
    Py_DECREF(seq_field);
    Py_DECREF(field);
  }
  {  // object_prediction
    PyObject * field = PyObject_GetAttrString(_pymsg, "object_prediction");
    if (!field) {
      return false;
    }
    PyObject * seq_field = PySequence_Fast(field, "expected a sequence in 'object_prediction'");
    if (!seq_field) {
      Py_DECREF(field);
      return false;
    }
    Py_ssize_t size = PySequence_Size(field);
    if (-1 == size) {
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    if (!common_msgs_humble__msg__Objectprediction__Sequence__init(&(ros_message->object_prediction), size)) {
      PyErr_SetString(PyExc_RuntimeError, "unable to create common_msgs_humble__msg__Objectprediction__Sequence ros_message");
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    common_msgs_humble__msg__Objectprediction * dest = ros_message->object_prediction.data;
    for (Py_ssize_t i = 0; i < size; ++i) {
      if (!common_msgs_humble__msg__objectprediction__convert_from_py(PySequence_Fast_GET_ITEM(seq_field, i), &dest[i])) {
        Py_DECREF(seq_field);
        Py_DECREF(field);
        return false;
      }
    }
    Py_DECREF(seq_field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * common_msgs_humble__msg__sensorobject__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of Sensorobject */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("common_msgs_humble.msg._sensorobject");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "Sensorobject");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  common_msgs_humble__msg__Sensorobject * ros_message = (common_msgs_humble__msg__Sensorobject *)raw_ros_message;
  {  // id
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->id);
    {
      int rc = PyObject_SetAttrString(_pymessage, "id", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // x
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->x);
    {
      int rc = PyObject_SetAttrString(_pymessage, "x", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // y
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->y);
    {
      int rc = PyObject_SetAttrString(_pymessage, "y", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // z
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->z);
    {
      int rc = PyObject_SetAttrString(_pymessage, "z", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // longtitude
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->longtitude);
    {
      int rc = PyObject_SetAttrString(_pymessage, "longtitude", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // latitude
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->latitude);
    {
      int rc = PyObject_SetAttrString(_pymessage, "latitude", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // altitude
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->altitude);
    {
      int rc = PyObject_SetAttrString(_pymessage, "altitude", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // relspeedy
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->relspeedy);
    {
      int rc = PyObject_SetAttrString(_pymessage, "relspeedy", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // relspeedx
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->relspeedx);
    {
      int rc = PyObject_SetAttrString(_pymessage, "relspeedx", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // rollrad
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->rollrad);
    {
      int rc = PyObject_SetAttrString(_pymessage, "rollrad", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // pitchrad
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->pitchrad);
    {
      int rc = PyObject_SetAttrString(_pymessage, "pitchrad", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // azimuth
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->azimuth);
    {
      int rc = PyObject_SetAttrString(_pymessage, "azimuth", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // pitchrate
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->pitchrate);
    {
      int rc = PyObject_SetAttrString(_pymessage, "pitchrate", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // rollrate
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->rollrate);
    {
      int rc = PyObject_SetAttrString(_pymessage, "rollrate", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // yawrate
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->yawrate);
    {
      int rc = PyObject_SetAttrString(_pymessage, "yawrate", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // width
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->width);
    {
      int rc = PyObject_SetAttrString(_pymessage, "width", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // length
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->length);
    {
      int rc = PyObject_SetAttrString(_pymessage, "length", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // height
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->height);
    {
      int rc = PyObject_SetAttrString(_pymessage, "height", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // classification
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->classification);
    {
      int rc = PyObject_SetAttrString(_pymessage, "classification", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // value
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->value);
    {
      int rc = PyObject_SetAttrString(_pymessage, "value", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // confidence
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->confidence);
    {
      int rc = PyObject_SetAttrString(_pymessage, "confidence", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // points
    PyObject * field = NULL;
    size_t size = ros_message->points.size;
    field = PyList_New(size);
    if (!field) {
      return NULL;
    }
    common_msgs_humble__msg__Point3d * item;
    for (size_t i = 0; i < size; ++i) {
      item = &(ros_message->points.data[i]);
      PyObject * pyitem = common_msgs_humble__msg__point3d__convert_to_py(item);
      if (!pyitem) {
        Py_DECREF(field);
        return NULL;
      }
      int rc = PyList_SetItem(field, i, pyitem);
      (void)rc;
      assert(rc == 0);
    }
    assert(PySequence_Check(field));
    {
      int rc = PyObject_SetAttrString(_pymessage, "points", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // driving_intent
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->driving_intent);
    {
      int rc = PyObject_SetAttrString(_pymessage, "driving_intent", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // behavior_state
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->behavior_state);
    {
      int rc = PyObject_SetAttrString(_pymessage, "behavior_state", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // radarindex
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->radarindex);
    {
      int rc = PyObject_SetAttrString(_pymessage, "radarindex", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // radarobjectid
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->radarobjectid);
    {
      int rc = PyObject_SetAttrString(_pymessage, "radarobjectid", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // s
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->s);
    {
      int rc = PyObject_SetAttrString(_pymessage, "s", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // l
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->l);
    {
      int rc = PyObject_SetAttrString(_pymessage, "l", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // speeds
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->speeds);
    {
      int rc = PyObject_SetAttrString(_pymessage, "speeds", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // speedl
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->speedl);
    {
      int rc = PyObject_SetAttrString(_pymessage, "speedl", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // object_decision
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->object_decision);
    {
      int rc = PyObject_SetAttrString(_pymessage, "object_decision", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // object_history
    PyObject * field = NULL;
    size_t size = ros_message->object_history.size;
    field = PyList_New(size);
    if (!field) {
      return NULL;
    }
    common_msgs_humble__msg__Objecthistory * item;
    for (size_t i = 0; i < size; ++i) {
      item = &(ros_message->object_history.data[i]);
      PyObject * pyitem = common_msgs_humble__msg__objecthistory__convert_to_py(item);
      if (!pyitem) {
        Py_DECREF(field);
        return NULL;
      }
      int rc = PyList_SetItem(field, i, pyitem);
      (void)rc;
      assert(rc == 0);
    }
    assert(PySequence_Check(field));
    {
      int rc = PyObject_SetAttrString(_pymessage, "object_history", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // object_prediction
    PyObject * field = NULL;
    size_t size = ros_message->object_prediction.size;
    field = PyList_New(size);
    if (!field) {
      return NULL;
    }
    common_msgs_humble__msg__Objectprediction * item;
    for (size_t i = 0; i < size; ++i) {
      item = &(ros_message->object_prediction.data[i]);
      PyObject * pyitem = common_msgs_humble__msg__objectprediction__convert_to_py(item);
      if (!pyitem) {
        Py_DECREF(field);
        return NULL;
      }
      int rc = PyList_SetItem(field, i, pyitem);
      (void)rc;
      assert(rc == 0);
    }
    assert(PySequence_Check(field));
    {
      int rc = PyObject_SetAttrString(_pymessage, "object_prediction", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
