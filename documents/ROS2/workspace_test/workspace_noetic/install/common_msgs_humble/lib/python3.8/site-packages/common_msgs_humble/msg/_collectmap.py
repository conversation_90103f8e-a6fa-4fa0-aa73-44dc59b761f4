# generated from rosidl_generator_py/resource/_idl.py.em
# with input from common_msgs_humble:msg/Collectmap.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_Collectmap(type):
    """Metaclass of message 'Collectmap'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('common_msgs_humble')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'common_msgs_humble.msg.Collectmap')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__collectmap
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__collectmap
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__collectmap
            cls._TYPE_SUPPORT = module.type_support_msg__msg__collectmap
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__collectmap

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class Collectmap(metaclass=Metaclass_Collectmap):
    """Message class 'Collectmap'."""

    __slots__ = [
        '_mapname',
        '_zonename',
        '_property',
        '_laneattr',
        '_speed',
        '_sideroadwidth',
        '_mergelanetype',
        '_sensorlanetype',
        '_leftsearchdis',
        '_rightsearchdis',
        '_timestamp',
        '_lanewidth',
        '_leftlanewidth',
        '_rightlanewidth',
        '_laneswitch',
        '_sidepass',
        '_lanenum',
        '_lanesite',
    ]

    _fields_and_field_types = {
        'mapname': 'uint8',
        'zonename': 'uint8',
        'property': 'uint8',
        'laneattr': 'uint8',
        'speed': 'uint8',
        'sideroadwidth': 'float',
        'mergelanetype': 'uint8',
        'sensorlanetype': 'uint8',
        'leftsearchdis': 'float',
        'rightsearchdis': 'float',
        'timestamp': 'int64',
        'lanewidth': 'float',
        'leftlanewidth': 'float',
        'rightlanewidth': 'float',
        'laneswitch': 'uint8',
        'sidepass': 'uint8',
        'lanenum': 'uint8',
        'lanesite': 'uint8',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('int64'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.mapname = kwargs.get('mapname', int())
        self.zonename = kwargs.get('zonename', int())
        self.property = kwargs.get('property', int())
        self.laneattr = kwargs.get('laneattr', int())
        self.speed = kwargs.get('speed', int())
        self.sideroadwidth = kwargs.get('sideroadwidth', float())
        self.mergelanetype = kwargs.get('mergelanetype', int())
        self.sensorlanetype = kwargs.get('sensorlanetype', int())
        self.leftsearchdis = kwargs.get('leftsearchdis', float())
        self.rightsearchdis = kwargs.get('rightsearchdis', float())
        self.timestamp = kwargs.get('timestamp', int())
        self.lanewidth = kwargs.get('lanewidth', float())
        self.leftlanewidth = kwargs.get('leftlanewidth', float())
        self.rightlanewidth = kwargs.get('rightlanewidth', float())
        self.laneswitch = kwargs.get('laneswitch', int())
        self.sidepass = kwargs.get('sidepass', int())
        self.lanenum = kwargs.get('lanenum', int())
        self.lanesite = kwargs.get('lanesite', int())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.mapname != other.mapname:
            return False
        if self.zonename != other.zonename:
            return False
        if self.property != other.property:
            return False
        if self.laneattr != other.laneattr:
            return False
        if self.speed != other.speed:
            return False
        if self.sideroadwidth != other.sideroadwidth:
            return False
        if self.mergelanetype != other.mergelanetype:
            return False
        if self.sensorlanetype != other.sensorlanetype:
            return False
        if self.leftsearchdis != other.leftsearchdis:
            return False
        if self.rightsearchdis != other.rightsearchdis:
            return False
        if self.timestamp != other.timestamp:
            return False
        if self.lanewidth != other.lanewidth:
            return False
        if self.leftlanewidth != other.leftlanewidth:
            return False
        if self.rightlanewidth != other.rightlanewidth:
            return False
        if self.laneswitch != other.laneswitch:
            return False
        if self.sidepass != other.sidepass:
            return False
        if self.lanenum != other.lanenum:
            return False
        if self.lanesite != other.lanesite:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def mapname(self):
        """Message field 'mapname'."""
        return self._mapname

    @mapname.setter
    def mapname(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'mapname' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'mapname' field must be an unsigned integer in [0, 255]"
        self._mapname = value

    @builtins.property
    def zonename(self):
        """Message field 'zonename'."""
        return self._zonename

    @zonename.setter
    def zonename(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'zonename' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'zonename' field must be an unsigned integer in [0, 255]"
        self._zonename = value

    @builtins.property  # noqa: A003
    def property(self):  # noqa: A003
        """Message field 'property'."""
        return self._property

    @property.setter  # noqa: A003
    def property(self, value):  # noqa: A003
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'property' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'property' field must be an unsigned integer in [0, 255]"
        self._property = value

    @builtins.property
    def laneattr(self):
        """Message field 'laneattr'."""
        return self._laneattr

    @laneattr.setter
    def laneattr(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'laneattr' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'laneattr' field must be an unsigned integer in [0, 255]"
        self._laneattr = value

    @builtins.property
    def speed(self):
        """Message field 'speed'."""
        return self._speed

    @speed.setter
    def speed(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'speed' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'speed' field must be an unsigned integer in [0, 255]"
        self._speed = value

    @builtins.property
    def sideroadwidth(self):
        """Message field 'sideroadwidth'."""
        return self._sideroadwidth

    @sideroadwidth.setter
    def sideroadwidth(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'sideroadwidth' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'sideroadwidth' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._sideroadwidth = value

    @builtins.property
    def mergelanetype(self):
        """Message field 'mergelanetype'."""
        return self._mergelanetype

    @mergelanetype.setter
    def mergelanetype(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'mergelanetype' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'mergelanetype' field must be an unsigned integer in [0, 255]"
        self._mergelanetype = value

    @builtins.property
    def sensorlanetype(self):
        """Message field 'sensorlanetype'."""
        return self._sensorlanetype

    @sensorlanetype.setter
    def sensorlanetype(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'sensorlanetype' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'sensorlanetype' field must be an unsigned integer in [0, 255]"
        self._sensorlanetype = value

    @builtins.property
    def leftsearchdis(self):
        """Message field 'leftsearchdis'."""
        return self._leftsearchdis

    @leftsearchdis.setter
    def leftsearchdis(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'leftsearchdis' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'leftsearchdis' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._leftsearchdis = value

    @builtins.property
    def rightsearchdis(self):
        """Message field 'rightsearchdis'."""
        return self._rightsearchdis

    @rightsearchdis.setter
    def rightsearchdis(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'rightsearchdis' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'rightsearchdis' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._rightsearchdis = value

    @builtins.property
    def timestamp(self):
        """Message field 'timestamp'."""
        return self._timestamp

    @timestamp.setter
    def timestamp(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'timestamp' field must be of type 'int'"
            assert value >= -9223372036854775808 and value < 9223372036854775808, \
                "The 'timestamp' field must be an integer in [-9223372036854775808, 9223372036854775807]"
        self._timestamp = value

    @builtins.property
    def lanewidth(self):
        """Message field 'lanewidth'."""
        return self._lanewidth

    @lanewidth.setter
    def lanewidth(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'lanewidth' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'lanewidth' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._lanewidth = value

    @builtins.property
    def leftlanewidth(self):
        """Message field 'leftlanewidth'."""
        return self._leftlanewidth

    @leftlanewidth.setter
    def leftlanewidth(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'leftlanewidth' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'leftlanewidth' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._leftlanewidth = value

    @builtins.property
    def rightlanewidth(self):
        """Message field 'rightlanewidth'."""
        return self._rightlanewidth

    @rightlanewidth.setter
    def rightlanewidth(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'rightlanewidth' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'rightlanewidth' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._rightlanewidth = value

    @builtins.property
    def laneswitch(self):
        """Message field 'laneswitch'."""
        return self._laneswitch

    @laneswitch.setter
    def laneswitch(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'laneswitch' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'laneswitch' field must be an unsigned integer in [0, 255]"
        self._laneswitch = value

    @builtins.property
    def sidepass(self):
        """Message field 'sidepass'."""
        return self._sidepass

    @sidepass.setter
    def sidepass(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'sidepass' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'sidepass' field must be an unsigned integer in [0, 255]"
        self._sidepass = value

    @builtins.property
    def lanenum(self):
        """Message field 'lanenum'."""
        return self._lanenum

    @lanenum.setter
    def lanenum(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'lanenum' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'lanenum' field must be an unsigned integer in [0, 255]"
        self._lanenum = value

    @builtins.property
    def lanesite(self):
        """Message field 'lanesite'."""
        return self._lanesite

    @lanesite.setter
    def lanesite(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'lanesite' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'lanesite' field must be an unsigned integer in [0, 255]"
        self._lanesite = value
