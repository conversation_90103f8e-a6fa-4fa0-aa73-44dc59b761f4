# generated from rosidl_generator_py/resource/_idl.py.em
# with input from common_msgs_humble:msg/Mapformat.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_Mapformat(type):
    """Metaclass of message 'Mapformat'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('common_msgs_humble')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'common_msgs_humble.msg.Mapformat')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__mapformat
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__mapformat
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__mapformat
            cls._TYPE_SUPPORT = module.type_support_msg__msg__mapformat
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__mapformat

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class Mapformat(metaclass=Metaclass_Mapformat):
    """Message class 'Mapformat'."""

    __slots__ = [
        '_lon',
        '_lat',
        '_roadtype',
        '_speed',
        '_lanetype',
        '_mergelanetype',
        '_sensorlanetype',
        '_turnlight',
        '_sideroadwidth',
        '_lanewidth',
        '_leftlanewidth',
        '_rightlanewidth',
        '_leftsearchdis',
        '_rightsearchdis',
        '_heading',
        '_curvature',
        '_gpstime',
        '_switchflag',
        '_borrowflag',
        '_lanesum',
        '_lanenum',
        '_backup1',
        '_backup2',
        '_backup3',
        '_backup4',
        '_backup5',
        '_backup6',
        '_backup7',
        '_backup8',
        '_backup9',
    ]

    _fields_and_field_types = {
        'lon': 'double',
        'lat': 'double',
        'roadtype': 'uint8',
        'speed': 'float',
        'lanetype': 'uint8',
        'mergelanetype': 'uint8',
        'sensorlanetype': 'uint8',
        'turnlight': 'uint8',
        'sideroadwidth': 'float',
        'lanewidth': 'float',
        'leftlanewidth': 'float',
        'rightlanewidth': 'float',
        'leftsearchdis': 'float',
        'rightsearchdis': 'float',
        'heading': 'float',
        'curvature': 'float',
        'gpstime': 'double',
        'switchflag': 'uint8',
        'borrowflag': 'uint8',
        'lanesum': 'uint8',
        'lanenum': 'uint8',
        'backup1': 'uint8',
        'backup2': 'uint8',
        'backup3': 'uint8',
        'backup4': 'float',
        'backup5': 'float',
        'backup6': 'float',
        'backup7': 'double',
        'backup8': 'double',
        'backup9': 'double',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.lon = kwargs.get('lon', float())
        self.lat = kwargs.get('lat', float())
        self.roadtype = kwargs.get('roadtype', int())
        self.speed = kwargs.get('speed', float())
        self.lanetype = kwargs.get('lanetype', int())
        self.mergelanetype = kwargs.get('mergelanetype', int())
        self.sensorlanetype = kwargs.get('sensorlanetype', int())
        self.turnlight = kwargs.get('turnlight', int())
        self.sideroadwidth = kwargs.get('sideroadwidth', float())
        self.lanewidth = kwargs.get('lanewidth', float())
        self.leftlanewidth = kwargs.get('leftlanewidth', float())
        self.rightlanewidth = kwargs.get('rightlanewidth', float())
        self.leftsearchdis = kwargs.get('leftsearchdis', float())
        self.rightsearchdis = kwargs.get('rightsearchdis', float())
        self.heading = kwargs.get('heading', float())
        self.curvature = kwargs.get('curvature', float())
        self.gpstime = kwargs.get('gpstime', float())
        self.switchflag = kwargs.get('switchflag', int())
        self.borrowflag = kwargs.get('borrowflag', int())
        self.lanesum = kwargs.get('lanesum', int())
        self.lanenum = kwargs.get('lanenum', int())
        self.backup1 = kwargs.get('backup1', int())
        self.backup2 = kwargs.get('backup2', int())
        self.backup3 = kwargs.get('backup3', int())
        self.backup4 = kwargs.get('backup4', float())
        self.backup5 = kwargs.get('backup5', float())
        self.backup6 = kwargs.get('backup6', float())
        self.backup7 = kwargs.get('backup7', float())
        self.backup8 = kwargs.get('backup8', float())
        self.backup9 = kwargs.get('backup9', float())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.lon != other.lon:
            return False
        if self.lat != other.lat:
            return False
        if self.roadtype != other.roadtype:
            return False
        if self.speed != other.speed:
            return False
        if self.lanetype != other.lanetype:
            return False
        if self.mergelanetype != other.mergelanetype:
            return False
        if self.sensorlanetype != other.sensorlanetype:
            return False
        if self.turnlight != other.turnlight:
            return False
        if self.sideroadwidth != other.sideroadwidth:
            return False
        if self.lanewidth != other.lanewidth:
            return False
        if self.leftlanewidth != other.leftlanewidth:
            return False
        if self.rightlanewidth != other.rightlanewidth:
            return False
        if self.leftsearchdis != other.leftsearchdis:
            return False
        if self.rightsearchdis != other.rightsearchdis:
            return False
        if self.heading != other.heading:
            return False
        if self.curvature != other.curvature:
            return False
        if self.gpstime != other.gpstime:
            return False
        if self.switchflag != other.switchflag:
            return False
        if self.borrowflag != other.borrowflag:
            return False
        if self.lanesum != other.lanesum:
            return False
        if self.lanenum != other.lanenum:
            return False
        if self.backup1 != other.backup1:
            return False
        if self.backup2 != other.backup2:
            return False
        if self.backup3 != other.backup3:
            return False
        if self.backup4 != other.backup4:
            return False
        if self.backup5 != other.backup5:
            return False
        if self.backup6 != other.backup6:
            return False
        if self.backup7 != other.backup7:
            return False
        if self.backup8 != other.backup8:
            return False
        if self.backup9 != other.backup9:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def lon(self):
        """Message field 'lon'."""
        return self._lon

    @lon.setter
    def lon(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'lon' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'lon' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._lon = value

    @builtins.property
    def lat(self):
        """Message field 'lat'."""
        return self._lat

    @lat.setter
    def lat(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'lat' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'lat' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._lat = value

    @builtins.property
    def roadtype(self):
        """Message field 'roadtype'."""
        return self._roadtype

    @roadtype.setter
    def roadtype(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'roadtype' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'roadtype' field must be an unsigned integer in [0, 255]"
        self._roadtype = value

    @builtins.property
    def speed(self):
        """Message field 'speed'."""
        return self._speed

    @speed.setter
    def speed(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'speed' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'speed' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._speed = value

    @builtins.property
    def lanetype(self):
        """Message field 'lanetype'."""
        return self._lanetype

    @lanetype.setter
    def lanetype(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'lanetype' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'lanetype' field must be an unsigned integer in [0, 255]"
        self._lanetype = value

    @builtins.property
    def mergelanetype(self):
        """Message field 'mergelanetype'."""
        return self._mergelanetype

    @mergelanetype.setter
    def mergelanetype(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'mergelanetype' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'mergelanetype' field must be an unsigned integer in [0, 255]"
        self._mergelanetype = value

    @builtins.property
    def sensorlanetype(self):
        """Message field 'sensorlanetype'."""
        return self._sensorlanetype

    @sensorlanetype.setter
    def sensorlanetype(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'sensorlanetype' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'sensorlanetype' field must be an unsigned integer in [0, 255]"
        self._sensorlanetype = value

    @builtins.property
    def turnlight(self):
        """Message field 'turnlight'."""
        return self._turnlight

    @turnlight.setter
    def turnlight(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'turnlight' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'turnlight' field must be an unsigned integer in [0, 255]"
        self._turnlight = value

    @builtins.property
    def sideroadwidth(self):
        """Message field 'sideroadwidth'."""
        return self._sideroadwidth

    @sideroadwidth.setter
    def sideroadwidth(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'sideroadwidth' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'sideroadwidth' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._sideroadwidth = value

    @builtins.property
    def lanewidth(self):
        """Message field 'lanewidth'."""
        return self._lanewidth

    @lanewidth.setter
    def lanewidth(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'lanewidth' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'lanewidth' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._lanewidth = value

    @builtins.property
    def leftlanewidth(self):
        """Message field 'leftlanewidth'."""
        return self._leftlanewidth

    @leftlanewidth.setter
    def leftlanewidth(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'leftlanewidth' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'leftlanewidth' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._leftlanewidth = value

    @builtins.property
    def rightlanewidth(self):
        """Message field 'rightlanewidth'."""
        return self._rightlanewidth

    @rightlanewidth.setter
    def rightlanewidth(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'rightlanewidth' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'rightlanewidth' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._rightlanewidth = value

    @builtins.property
    def leftsearchdis(self):
        """Message field 'leftsearchdis'."""
        return self._leftsearchdis

    @leftsearchdis.setter
    def leftsearchdis(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'leftsearchdis' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'leftsearchdis' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._leftsearchdis = value

    @builtins.property
    def rightsearchdis(self):
        """Message field 'rightsearchdis'."""
        return self._rightsearchdis

    @rightsearchdis.setter
    def rightsearchdis(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'rightsearchdis' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'rightsearchdis' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._rightsearchdis = value

    @builtins.property
    def heading(self):
        """Message field 'heading'."""
        return self._heading

    @heading.setter
    def heading(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'heading' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'heading' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._heading = value

    @builtins.property
    def curvature(self):
        """Message field 'curvature'."""
        return self._curvature

    @curvature.setter
    def curvature(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'curvature' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'curvature' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._curvature = value

    @builtins.property
    def gpstime(self):
        """Message field 'gpstime'."""
        return self._gpstime

    @gpstime.setter
    def gpstime(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'gpstime' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'gpstime' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._gpstime = value

    @builtins.property
    def switchflag(self):
        """Message field 'switchflag'."""
        return self._switchflag

    @switchflag.setter
    def switchflag(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'switchflag' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'switchflag' field must be an unsigned integer in [0, 255]"
        self._switchflag = value

    @builtins.property
    def borrowflag(self):
        """Message field 'borrowflag'."""
        return self._borrowflag

    @borrowflag.setter
    def borrowflag(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'borrowflag' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'borrowflag' field must be an unsigned integer in [0, 255]"
        self._borrowflag = value

    @builtins.property
    def lanesum(self):
        """Message field 'lanesum'."""
        return self._lanesum

    @lanesum.setter
    def lanesum(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'lanesum' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'lanesum' field must be an unsigned integer in [0, 255]"
        self._lanesum = value

    @builtins.property
    def lanenum(self):
        """Message field 'lanenum'."""
        return self._lanenum

    @lanenum.setter
    def lanenum(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'lanenum' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'lanenum' field must be an unsigned integer in [0, 255]"
        self._lanenum = value

    @builtins.property
    def backup1(self):
        """Message field 'backup1'."""
        return self._backup1

    @backup1.setter
    def backup1(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'backup1' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'backup1' field must be an unsigned integer in [0, 255]"
        self._backup1 = value

    @builtins.property
    def backup2(self):
        """Message field 'backup2'."""
        return self._backup2

    @backup2.setter
    def backup2(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'backup2' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'backup2' field must be an unsigned integer in [0, 255]"
        self._backup2 = value

    @builtins.property
    def backup3(self):
        """Message field 'backup3'."""
        return self._backup3

    @backup3.setter
    def backup3(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'backup3' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'backup3' field must be an unsigned integer in [0, 255]"
        self._backup3 = value

    @builtins.property
    def backup4(self):
        """Message field 'backup4'."""
        return self._backup4

    @backup4.setter
    def backup4(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'backup4' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'backup4' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._backup4 = value

    @builtins.property
    def backup5(self):
        """Message field 'backup5'."""
        return self._backup5

    @backup5.setter
    def backup5(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'backup5' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'backup5' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._backup5 = value

    @builtins.property
    def backup6(self):
        """Message field 'backup6'."""
        return self._backup6

    @backup6.setter
    def backup6(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'backup6' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'backup6' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._backup6 = value

    @builtins.property
    def backup7(self):
        """Message field 'backup7'."""
        return self._backup7

    @backup7.setter
    def backup7(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'backup7' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'backup7' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._backup7 = value

    @builtins.property
    def backup8(self):
        """Message field 'backup8'."""
        return self._backup8

    @backup8.setter
    def backup8(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'backup8' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'backup8' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._backup8 = value

    @builtins.property
    def backup9(self):
        """Message field 'backup9'."""
        return self._backup9

    @backup9.setter
    def backup9(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'backup9' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'backup9' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._backup9 = value
