# generated from rosidl_generator_py/resource/_idl.py.em
# with input from common_msgs_humble:msg/Obulight.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_Obulight(type):
    """Metaclass of message 'Obulight'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('common_msgs_humble')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'common_msgs_humble.msg.Obulight')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__obulight
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__obulight
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__obulight
            cls._TYPE_SUPPORT = module.type_support_msg__msg__obulight
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__obulight

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class Obulight(metaclass=Metaclass_Obulight):
    """Message class 'Obulight'."""

    __slots__ = [
        '_phase_id',
        '_status',
        '_start_time',
        '_end_time',
        '_next_start_time',
        '_lane_speed_lower',
        '_lane_speed_upper',
    ]

    _fields_and_field_types = {
        'phase_id': 'int32',
        'status': 'uint8',
        'start_time': 'int64',
        'end_time': 'int64',
        'next_start_time': 'int64',
        'lane_speed_lower': 'float',
        'lane_speed_upper': 'float',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('int64'),  # noqa: E501
        rosidl_parser.definition.BasicType('int64'),  # noqa: E501
        rosidl_parser.definition.BasicType('int64'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.phase_id = kwargs.get('phase_id', int())
        self.status = kwargs.get('status', int())
        self.start_time = kwargs.get('start_time', int())
        self.end_time = kwargs.get('end_time', int())
        self.next_start_time = kwargs.get('next_start_time', int())
        self.lane_speed_lower = kwargs.get('lane_speed_lower', float())
        self.lane_speed_upper = kwargs.get('lane_speed_upper', float())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.phase_id != other.phase_id:
            return False
        if self.status != other.status:
            return False
        if self.start_time != other.start_time:
            return False
        if self.end_time != other.end_time:
            return False
        if self.next_start_time != other.next_start_time:
            return False
        if self.lane_speed_lower != other.lane_speed_lower:
            return False
        if self.lane_speed_upper != other.lane_speed_upper:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def phase_id(self):
        """Message field 'phase_id'."""
        return self._phase_id

    @phase_id.setter
    def phase_id(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'phase_id' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'phase_id' field must be an integer in [-2147483648, 2147483647]"
        self._phase_id = value

    @builtins.property
    def status(self):
        """Message field 'status'."""
        return self._status

    @status.setter
    def status(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'status' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'status' field must be an unsigned integer in [0, 255]"
        self._status = value

    @builtins.property
    def start_time(self):
        """Message field 'start_time'."""
        return self._start_time

    @start_time.setter
    def start_time(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'start_time' field must be of type 'int'"
            assert value >= -9223372036854775808 and value < 9223372036854775808, \
                "The 'start_time' field must be an integer in [-9223372036854775808, 9223372036854775807]"
        self._start_time = value

    @builtins.property
    def end_time(self):
        """Message field 'end_time'."""
        return self._end_time

    @end_time.setter
    def end_time(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'end_time' field must be of type 'int'"
            assert value >= -9223372036854775808 and value < 9223372036854775808, \
                "The 'end_time' field must be an integer in [-9223372036854775808, 9223372036854775807]"
        self._end_time = value

    @builtins.property
    def next_start_time(self):
        """Message field 'next_start_time'."""
        return self._next_start_time

    @next_start_time.setter
    def next_start_time(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'next_start_time' field must be of type 'int'"
            assert value >= -9223372036854775808 and value < 9223372036854775808, \
                "The 'next_start_time' field must be an integer in [-9223372036854775808, 9223372036854775807]"
        self._next_start_time = value

    @builtins.property
    def lane_speed_lower(self):
        """Message field 'lane_speed_lower'."""
        return self._lane_speed_lower

    @lane_speed_lower.setter
    def lane_speed_lower(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'lane_speed_lower' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'lane_speed_lower' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._lane_speed_lower = value

    @builtins.property
    def lane_speed_upper(self):
        """Message field 'lane_speed_upper'."""
        return self._lane_speed_upper

    @lane_speed_upper.setter
    def lane_speed_upper(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'lane_speed_upper' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'lane_speed_upper' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._lane_speed_upper = value
