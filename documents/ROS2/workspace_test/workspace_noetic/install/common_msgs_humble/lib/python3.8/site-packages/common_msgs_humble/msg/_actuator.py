# generated from rosidl_generator_py/resource/_idl.py.em
# with input from common_msgs_humble:msg/Actuator.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_Actuator(type):
    """Metaclass of message 'Actuator'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('common_msgs_humble')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'common_msgs_humble.msg.Actuator')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__actuator
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__actuator
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__actuator
            cls._TYPE_SUPPORT = module.type_support_msg__msg__actuator
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__actuator

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class Actuator(metaclass=Metaclass_Actuator):
    """Message class 'Actuator'."""

    __slots__ = [
        '_epsmethod',
        '_epsangle',
        '_espmethod',
        '_escbrakepress',
        '_gaspedal',
        '_sysstatus',
        '_speed',
        '_lights',
        '_turnlight',
        '_gear',
        '_epb',
        '_door',
        '_isvalid',
        '_timestamp',
        '_sendsuccess',
        '_brakepedal',
        '_warning',
        '_error',
        '_battery',
        '_controlover',
        '_steerspeed',
        '_accelpos',
        '_breakflag',
        '_breakpos',
        '_yaw',
        '_mil',
        '_soc',
        '_batvol',
        '_acc',
        '_oilperhour',
        '_oilhundredkmconsume',
        '_oilconsume',
        '_autoctrlsig',
        '_totalvoltage',
        '_totalcurrent',
        '_motorspeed',
        '_motortorque',
        '_wirecontrolstatus',
        '_blinkerstatus',
        '_accx',
        '_gaspedalcar',
    ]

    _fields_and_field_types = {
        'epsmethod': 'uint8',
        'epsangle': 'int16',
        'espmethod': 'uint8',
        'escbrakepress': 'float',
        'gaspedal': 'uint8',
        'sysstatus': 'uint8',
        'speed': 'float',
        'lights': 'uint8',
        'turnlight': 'uint8',
        'gear': 'uint8',
        'epb': 'uint8',
        'door': 'uint8',
        'isvalid': 'uint8',
        'timestamp': 'int64',
        'sendsuccess': 'uint8',
        'brakepedal': 'uint8',
        'warning': 'uint8',
        'error': 'int16',
        'battery': 'uint8',
        'controlover': 'uint8',
        'steerspeed': 'int32',
        'accelpos': 'int32',
        'breakflag': 'int32',
        'breakpos': 'int32',
        'yaw': 'int32',
        'mil': 'int32',
        'soc': 'float',
        'batvol': 'float',
        'acc': 'int32',
        'oilperhour': 'int32',
        'oilhundredkmconsume': 'int32',
        'oilconsume': 'int32',
        'autoctrlsig': 'int32',
        'totalvoltage': 'float',
        'totalcurrent': 'float',
        'motorspeed': 'int32',
        'motortorque': 'int32',
        'wirecontrolstatus': 'uint8',
        'blinkerstatus': 'uint8',
        'accx': 'float',
        'gaspedalcar': 'float',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('int16'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('int64'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('int16'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.epsmethod = kwargs.get('epsmethod', int())
        self.epsangle = kwargs.get('epsangle', int())
        self.espmethod = kwargs.get('espmethod', int())
        self.escbrakepress = kwargs.get('escbrakepress', float())
        self.gaspedal = kwargs.get('gaspedal', int())
        self.sysstatus = kwargs.get('sysstatus', int())
        self.speed = kwargs.get('speed', float())
        self.lights = kwargs.get('lights', int())
        self.turnlight = kwargs.get('turnlight', int())
        self.gear = kwargs.get('gear', int())
        self.epb = kwargs.get('epb', int())
        self.door = kwargs.get('door', int())
        self.isvalid = kwargs.get('isvalid', int())
        self.timestamp = kwargs.get('timestamp', int())
        self.sendsuccess = kwargs.get('sendsuccess', int())
        self.brakepedal = kwargs.get('brakepedal', int())
        self.warning = kwargs.get('warning', int())
        self.error = kwargs.get('error', int())
        self.battery = kwargs.get('battery', int())
        self.controlover = kwargs.get('controlover', int())
        self.steerspeed = kwargs.get('steerspeed', int())
        self.accelpos = kwargs.get('accelpos', int())
        self.breakflag = kwargs.get('breakflag', int())
        self.breakpos = kwargs.get('breakpos', int())
        self.yaw = kwargs.get('yaw', int())
        self.mil = kwargs.get('mil', int())
        self.soc = kwargs.get('soc', float())
        self.batvol = kwargs.get('batvol', float())
        self.acc = kwargs.get('acc', int())
        self.oilperhour = kwargs.get('oilperhour', int())
        self.oilhundredkmconsume = kwargs.get('oilhundredkmconsume', int())
        self.oilconsume = kwargs.get('oilconsume', int())
        self.autoctrlsig = kwargs.get('autoctrlsig', int())
        self.totalvoltage = kwargs.get('totalvoltage', float())
        self.totalcurrent = kwargs.get('totalcurrent', float())
        self.motorspeed = kwargs.get('motorspeed', int())
        self.motortorque = kwargs.get('motortorque', int())
        self.wirecontrolstatus = kwargs.get('wirecontrolstatus', int())
        self.blinkerstatus = kwargs.get('blinkerstatus', int())
        self.accx = kwargs.get('accx', float())
        self.gaspedalcar = kwargs.get('gaspedalcar', float())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.epsmethod != other.epsmethod:
            return False
        if self.epsangle != other.epsangle:
            return False
        if self.espmethod != other.espmethod:
            return False
        if self.escbrakepress != other.escbrakepress:
            return False
        if self.gaspedal != other.gaspedal:
            return False
        if self.sysstatus != other.sysstatus:
            return False
        if self.speed != other.speed:
            return False
        if self.lights != other.lights:
            return False
        if self.turnlight != other.turnlight:
            return False
        if self.gear != other.gear:
            return False
        if self.epb != other.epb:
            return False
        if self.door != other.door:
            return False
        if self.isvalid != other.isvalid:
            return False
        if self.timestamp != other.timestamp:
            return False
        if self.sendsuccess != other.sendsuccess:
            return False
        if self.brakepedal != other.brakepedal:
            return False
        if self.warning != other.warning:
            return False
        if self.error != other.error:
            return False
        if self.battery != other.battery:
            return False
        if self.controlover != other.controlover:
            return False
        if self.steerspeed != other.steerspeed:
            return False
        if self.accelpos != other.accelpos:
            return False
        if self.breakflag != other.breakflag:
            return False
        if self.breakpos != other.breakpos:
            return False
        if self.yaw != other.yaw:
            return False
        if self.mil != other.mil:
            return False
        if self.soc != other.soc:
            return False
        if self.batvol != other.batvol:
            return False
        if self.acc != other.acc:
            return False
        if self.oilperhour != other.oilperhour:
            return False
        if self.oilhundredkmconsume != other.oilhundredkmconsume:
            return False
        if self.oilconsume != other.oilconsume:
            return False
        if self.autoctrlsig != other.autoctrlsig:
            return False
        if self.totalvoltage != other.totalvoltage:
            return False
        if self.totalcurrent != other.totalcurrent:
            return False
        if self.motorspeed != other.motorspeed:
            return False
        if self.motortorque != other.motortorque:
            return False
        if self.wirecontrolstatus != other.wirecontrolstatus:
            return False
        if self.blinkerstatus != other.blinkerstatus:
            return False
        if self.accx != other.accx:
            return False
        if self.gaspedalcar != other.gaspedalcar:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def epsmethod(self):
        """Message field 'epsmethod'."""
        return self._epsmethod

    @epsmethod.setter
    def epsmethod(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'epsmethod' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'epsmethod' field must be an unsigned integer in [0, 255]"
        self._epsmethod = value

    @builtins.property
    def epsangle(self):
        """Message field 'epsangle'."""
        return self._epsangle

    @epsangle.setter
    def epsangle(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'epsangle' field must be of type 'int'"
            assert value >= -32768 and value < 32768, \
                "The 'epsangle' field must be an integer in [-32768, 32767]"
        self._epsangle = value

    @builtins.property
    def espmethod(self):
        """Message field 'espmethod'."""
        return self._espmethod

    @espmethod.setter
    def espmethod(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'espmethod' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'espmethod' field must be an unsigned integer in [0, 255]"
        self._espmethod = value

    @builtins.property
    def escbrakepress(self):
        """Message field 'escbrakepress'."""
        return self._escbrakepress

    @escbrakepress.setter
    def escbrakepress(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'escbrakepress' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'escbrakepress' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._escbrakepress = value

    @builtins.property
    def gaspedal(self):
        """Message field 'gaspedal'."""
        return self._gaspedal

    @gaspedal.setter
    def gaspedal(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'gaspedal' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'gaspedal' field must be an unsigned integer in [0, 255]"
        self._gaspedal = value

    @builtins.property
    def sysstatus(self):
        """Message field 'sysstatus'."""
        return self._sysstatus

    @sysstatus.setter
    def sysstatus(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'sysstatus' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'sysstatus' field must be an unsigned integer in [0, 255]"
        self._sysstatus = value

    @builtins.property
    def speed(self):
        """Message field 'speed'."""
        return self._speed

    @speed.setter
    def speed(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'speed' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'speed' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._speed = value

    @builtins.property
    def lights(self):
        """Message field 'lights'."""
        return self._lights

    @lights.setter
    def lights(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'lights' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'lights' field must be an unsigned integer in [0, 255]"
        self._lights = value

    @builtins.property
    def turnlight(self):
        """Message field 'turnlight'."""
        return self._turnlight

    @turnlight.setter
    def turnlight(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'turnlight' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'turnlight' field must be an unsigned integer in [0, 255]"
        self._turnlight = value

    @builtins.property
    def gear(self):
        """Message field 'gear'."""
        return self._gear

    @gear.setter
    def gear(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'gear' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'gear' field must be an unsigned integer in [0, 255]"
        self._gear = value

    @builtins.property
    def epb(self):
        """Message field 'epb'."""
        return self._epb

    @epb.setter
    def epb(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'epb' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'epb' field must be an unsigned integer in [0, 255]"
        self._epb = value

    @builtins.property
    def door(self):
        """Message field 'door'."""
        return self._door

    @door.setter
    def door(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'door' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'door' field must be an unsigned integer in [0, 255]"
        self._door = value

    @builtins.property
    def isvalid(self):
        """Message field 'isvalid'."""
        return self._isvalid

    @isvalid.setter
    def isvalid(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'isvalid' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'isvalid' field must be an unsigned integer in [0, 255]"
        self._isvalid = value

    @builtins.property
    def timestamp(self):
        """Message field 'timestamp'."""
        return self._timestamp

    @timestamp.setter
    def timestamp(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'timestamp' field must be of type 'int'"
            assert value >= -9223372036854775808 and value < 9223372036854775808, \
                "The 'timestamp' field must be an integer in [-9223372036854775808, 9223372036854775807]"
        self._timestamp = value

    @builtins.property
    def sendsuccess(self):
        """Message field 'sendsuccess'."""
        return self._sendsuccess

    @sendsuccess.setter
    def sendsuccess(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'sendsuccess' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'sendsuccess' field must be an unsigned integer in [0, 255]"
        self._sendsuccess = value

    @builtins.property
    def brakepedal(self):
        """Message field 'brakepedal'."""
        return self._brakepedal

    @brakepedal.setter
    def brakepedal(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'brakepedal' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'brakepedal' field must be an unsigned integer in [0, 255]"
        self._brakepedal = value

    @builtins.property
    def warning(self):
        """Message field 'warning'."""
        return self._warning

    @warning.setter
    def warning(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'warning' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'warning' field must be an unsigned integer in [0, 255]"
        self._warning = value

    @builtins.property
    def error(self):
        """Message field 'error'."""
        return self._error

    @error.setter
    def error(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'error' field must be of type 'int'"
            assert value >= -32768 and value < 32768, \
                "The 'error' field must be an integer in [-32768, 32767]"
        self._error = value

    @builtins.property
    def battery(self):
        """Message field 'battery'."""
        return self._battery

    @battery.setter
    def battery(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'battery' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'battery' field must be an unsigned integer in [0, 255]"
        self._battery = value

    @builtins.property
    def controlover(self):
        """Message field 'controlover'."""
        return self._controlover

    @controlover.setter
    def controlover(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'controlover' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'controlover' field must be an unsigned integer in [0, 255]"
        self._controlover = value

    @builtins.property
    def steerspeed(self):
        """Message field 'steerspeed'."""
        return self._steerspeed

    @steerspeed.setter
    def steerspeed(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'steerspeed' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'steerspeed' field must be an integer in [-2147483648, 2147483647]"
        self._steerspeed = value

    @builtins.property
    def accelpos(self):
        """Message field 'accelpos'."""
        return self._accelpos

    @accelpos.setter
    def accelpos(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'accelpos' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'accelpos' field must be an integer in [-2147483648, 2147483647]"
        self._accelpos = value

    @builtins.property
    def breakflag(self):
        """Message field 'breakflag'."""
        return self._breakflag

    @breakflag.setter
    def breakflag(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'breakflag' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'breakflag' field must be an integer in [-2147483648, 2147483647]"
        self._breakflag = value

    @builtins.property
    def breakpos(self):
        """Message field 'breakpos'."""
        return self._breakpos

    @breakpos.setter
    def breakpos(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'breakpos' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'breakpos' field must be an integer in [-2147483648, 2147483647]"
        self._breakpos = value

    @builtins.property
    def yaw(self):
        """Message field 'yaw'."""
        return self._yaw

    @yaw.setter
    def yaw(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'yaw' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'yaw' field must be an integer in [-2147483648, 2147483647]"
        self._yaw = value

    @builtins.property
    def mil(self):
        """Message field 'mil'."""
        return self._mil

    @mil.setter
    def mil(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'mil' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'mil' field must be an integer in [-2147483648, 2147483647]"
        self._mil = value

    @builtins.property
    def soc(self):
        """Message field 'soc'."""
        return self._soc

    @soc.setter
    def soc(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'soc' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'soc' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._soc = value

    @builtins.property
    def batvol(self):
        """Message field 'batvol'."""
        return self._batvol

    @batvol.setter
    def batvol(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'batvol' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'batvol' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._batvol = value

    @builtins.property
    def acc(self):
        """Message field 'acc'."""
        return self._acc

    @acc.setter
    def acc(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'acc' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'acc' field must be an integer in [-2147483648, 2147483647]"
        self._acc = value

    @builtins.property
    def oilperhour(self):
        """Message field 'oilperhour'."""
        return self._oilperhour

    @oilperhour.setter
    def oilperhour(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'oilperhour' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'oilperhour' field must be an integer in [-2147483648, 2147483647]"
        self._oilperhour = value

    @builtins.property
    def oilhundredkmconsume(self):
        """Message field 'oilhundredkmconsume'."""
        return self._oilhundredkmconsume

    @oilhundredkmconsume.setter
    def oilhundredkmconsume(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'oilhundredkmconsume' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'oilhundredkmconsume' field must be an integer in [-2147483648, 2147483647]"
        self._oilhundredkmconsume = value

    @builtins.property
    def oilconsume(self):
        """Message field 'oilconsume'."""
        return self._oilconsume

    @oilconsume.setter
    def oilconsume(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'oilconsume' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'oilconsume' field must be an integer in [-2147483648, 2147483647]"
        self._oilconsume = value

    @builtins.property
    def autoctrlsig(self):
        """Message field 'autoctrlsig'."""
        return self._autoctrlsig

    @autoctrlsig.setter
    def autoctrlsig(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'autoctrlsig' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'autoctrlsig' field must be an integer in [-2147483648, 2147483647]"
        self._autoctrlsig = value

    @builtins.property
    def totalvoltage(self):
        """Message field 'totalvoltage'."""
        return self._totalvoltage

    @totalvoltage.setter
    def totalvoltage(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'totalvoltage' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'totalvoltage' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._totalvoltage = value

    @builtins.property
    def totalcurrent(self):
        """Message field 'totalcurrent'."""
        return self._totalcurrent

    @totalcurrent.setter
    def totalcurrent(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'totalcurrent' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'totalcurrent' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._totalcurrent = value

    @builtins.property
    def motorspeed(self):
        """Message field 'motorspeed'."""
        return self._motorspeed

    @motorspeed.setter
    def motorspeed(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'motorspeed' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'motorspeed' field must be an integer in [-2147483648, 2147483647]"
        self._motorspeed = value

    @builtins.property
    def motortorque(self):
        """Message field 'motortorque'."""
        return self._motortorque

    @motortorque.setter
    def motortorque(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'motortorque' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'motortorque' field must be an integer in [-2147483648, 2147483647]"
        self._motortorque = value

    @builtins.property
    def wirecontrolstatus(self):
        """Message field 'wirecontrolstatus'."""
        return self._wirecontrolstatus

    @wirecontrolstatus.setter
    def wirecontrolstatus(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'wirecontrolstatus' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'wirecontrolstatus' field must be an unsigned integer in [0, 255]"
        self._wirecontrolstatus = value

    @builtins.property
    def blinkerstatus(self):
        """Message field 'blinkerstatus'."""
        return self._blinkerstatus

    @blinkerstatus.setter
    def blinkerstatus(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'blinkerstatus' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'blinkerstatus' field must be an unsigned integer in [0, 255]"
        self._blinkerstatus = value

    @builtins.property
    def accx(self):
        """Message field 'accx'."""
        return self._accx

    @accx.setter
    def accx(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'accx' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'accx' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._accx = value

    @builtins.property
    def gaspedalcar(self):
        """Message field 'gaspedalcar'."""
        return self._gaspedalcar

    @gaspedalcar.setter
    def gaspedalcar(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'gaspedalcar' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'gaspedalcar' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._gaspedalcar = value
