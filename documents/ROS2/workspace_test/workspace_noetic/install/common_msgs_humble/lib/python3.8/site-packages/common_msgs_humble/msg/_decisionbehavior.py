# generated from rosidl_generator_py/resource/_idl.py.em
# with input from common_msgs_humble:msg/Decisionbehavior.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_Decisionbehavior(type):
    """Metaclass of message 'Decisionbehavior'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('common_msgs_humble')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'common_msgs_humble.msg.Decisionbehavior')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__decisionbehavior
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__decisionbehavior
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__decisionbehavior
            cls._TYPE_SUPPORT = module.type_support_msg__msg__decisionbehavior
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__decisionbehavior

            from common_msgs_humble.msg import Sensorobject
            if Sensorobject.__class__._TYPE_SUPPORT is None:
                Sensorobject.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class Decisionbehavior(metaclass=Metaclass_Decisionbehavior):
    """Message class 'Decisionbehavior'."""

    __slots__ = [
        '_drivebehavior',
        '_obs',
        '_isvalid',
        '_turnlights',
        '_laneblock',
        '_door',
        '_timestamp',
        '_mergetrigger',
        '_guidespeed',
        '_avoidsituation',
        '_alert',
        '_deviation',
        '_starttime',
        '_endtime',
        '_carworkstatus',
        '_stationblock',
        '_needreplan',
        '_virtualpointtype',
    ]

    _fields_and_field_types = {
        'drivebehavior': 'uint8',
        'obs': 'sequence<common_msgs_humble/Sensorobject>',
        'isvalid': 'uint8',
        'turnlights': 'uint8',
        'laneblock': 'uint8',
        'door': 'uint8',
        'timestamp': 'int64',
        'mergetrigger': 'uint8',
        'guidespeed': 'float',
        'avoidsituation': 'uint8',
        'alert': 'uint8',
        'deviation': 'float',
        'starttime': 'float',
        'endtime': 'float',
        'carworkstatus': 'uint8',
        'stationblock': 'boolean',
        'needreplan': 'uint8',
        'virtualpointtype': 'uint8',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['common_msgs_humble', 'msg'], 'Sensorobject')),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('int64'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('boolean'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.drivebehavior = kwargs.get('drivebehavior', int())
        self.obs = kwargs.get('obs', [])
        self.isvalid = kwargs.get('isvalid', int())
        self.turnlights = kwargs.get('turnlights', int())
        self.laneblock = kwargs.get('laneblock', int())
        self.door = kwargs.get('door', int())
        self.timestamp = kwargs.get('timestamp', int())
        self.mergetrigger = kwargs.get('mergetrigger', int())
        self.guidespeed = kwargs.get('guidespeed', float())
        self.avoidsituation = kwargs.get('avoidsituation', int())
        self.alert = kwargs.get('alert', int())
        self.deviation = kwargs.get('deviation', float())
        self.starttime = kwargs.get('starttime', float())
        self.endtime = kwargs.get('endtime', float())
        self.carworkstatus = kwargs.get('carworkstatus', int())
        self.stationblock = kwargs.get('stationblock', bool())
        self.needreplan = kwargs.get('needreplan', int())
        self.virtualpointtype = kwargs.get('virtualpointtype', int())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.drivebehavior != other.drivebehavior:
            return False
        if self.obs != other.obs:
            return False
        if self.isvalid != other.isvalid:
            return False
        if self.turnlights != other.turnlights:
            return False
        if self.laneblock != other.laneblock:
            return False
        if self.door != other.door:
            return False
        if self.timestamp != other.timestamp:
            return False
        if self.mergetrigger != other.mergetrigger:
            return False
        if self.guidespeed != other.guidespeed:
            return False
        if self.avoidsituation != other.avoidsituation:
            return False
        if self.alert != other.alert:
            return False
        if self.deviation != other.deviation:
            return False
        if self.starttime != other.starttime:
            return False
        if self.endtime != other.endtime:
            return False
        if self.carworkstatus != other.carworkstatus:
            return False
        if self.stationblock != other.stationblock:
            return False
        if self.needreplan != other.needreplan:
            return False
        if self.virtualpointtype != other.virtualpointtype:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def drivebehavior(self):
        """Message field 'drivebehavior'."""
        return self._drivebehavior

    @drivebehavior.setter
    def drivebehavior(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'drivebehavior' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'drivebehavior' field must be an unsigned integer in [0, 255]"
        self._drivebehavior = value

    @builtins.property
    def obs(self):
        """Message field 'obs'."""
        return self._obs

    @obs.setter
    def obs(self, value):
        if __debug__:
            from common_msgs_humble.msg import Sensorobject
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, Sensorobject) for v in value) and
                 True), \
                "The 'obs' field must be a set or sequence and each value of type 'Sensorobject'"
        self._obs = value

    @builtins.property
    def isvalid(self):
        """Message field 'isvalid'."""
        return self._isvalid

    @isvalid.setter
    def isvalid(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'isvalid' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'isvalid' field must be an unsigned integer in [0, 255]"
        self._isvalid = value

    @builtins.property
    def turnlights(self):
        """Message field 'turnlights'."""
        return self._turnlights

    @turnlights.setter
    def turnlights(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'turnlights' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'turnlights' field must be an unsigned integer in [0, 255]"
        self._turnlights = value

    @builtins.property
    def laneblock(self):
        """Message field 'laneblock'."""
        return self._laneblock

    @laneblock.setter
    def laneblock(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'laneblock' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'laneblock' field must be an unsigned integer in [0, 255]"
        self._laneblock = value

    @builtins.property
    def door(self):
        """Message field 'door'."""
        return self._door

    @door.setter
    def door(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'door' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'door' field must be an unsigned integer in [0, 255]"
        self._door = value

    @builtins.property
    def timestamp(self):
        """Message field 'timestamp'."""
        return self._timestamp

    @timestamp.setter
    def timestamp(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'timestamp' field must be of type 'int'"
            assert value >= -9223372036854775808 and value < 9223372036854775808, \
                "The 'timestamp' field must be an integer in [-9223372036854775808, 9223372036854775807]"
        self._timestamp = value

    @builtins.property
    def mergetrigger(self):
        """Message field 'mergetrigger'."""
        return self._mergetrigger

    @mergetrigger.setter
    def mergetrigger(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'mergetrigger' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'mergetrigger' field must be an unsigned integer in [0, 255]"
        self._mergetrigger = value

    @builtins.property
    def guidespeed(self):
        """Message field 'guidespeed'."""
        return self._guidespeed

    @guidespeed.setter
    def guidespeed(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'guidespeed' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'guidespeed' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._guidespeed = value

    @builtins.property
    def avoidsituation(self):
        """Message field 'avoidsituation'."""
        return self._avoidsituation

    @avoidsituation.setter
    def avoidsituation(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'avoidsituation' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'avoidsituation' field must be an unsigned integer in [0, 255]"
        self._avoidsituation = value

    @builtins.property
    def alert(self):
        """Message field 'alert'."""
        return self._alert

    @alert.setter
    def alert(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'alert' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'alert' field must be an unsigned integer in [0, 255]"
        self._alert = value

    @builtins.property
    def deviation(self):
        """Message field 'deviation'."""
        return self._deviation

    @deviation.setter
    def deviation(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'deviation' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'deviation' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._deviation = value

    @builtins.property
    def starttime(self):
        """Message field 'starttime'."""
        return self._starttime

    @starttime.setter
    def starttime(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'starttime' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'starttime' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._starttime = value

    @builtins.property
    def endtime(self):
        """Message field 'endtime'."""
        return self._endtime

    @endtime.setter
    def endtime(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'endtime' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'endtime' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._endtime = value

    @builtins.property
    def carworkstatus(self):
        """Message field 'carworkstatus'."""
        return self._carworkstatus

    @carworkstatus.setter
    def carworkstatus(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'carworkstatus' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'carworkstatus' field must be an unsigned integer in [0, 255]"
        self._carworkstatus = value

    @builtins.property
    def stationblock(self):
        """Message field 'stationblock'."""
        return self._stationblock

    @stationblock.setter
    def stationblock(self, value):
        if __debug__:
            assert \
                isinstance(value, bool), \
                "The 'stationblock' field must be of type 'bool'"
        self._stationblock = value

    @builtins.property
    def needreplan(self):
        """Message field 'needreplan'."""
        return self._needreplan

    @needreplan.setter
    def needreplan(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'needreplan' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'needreplan' field must be an unsigned integer in [0, 255]"
        self._needreplan = value

    @builtins.property
    def virtualpointtype(self):
        """Message field 'virtualpointtype'."""
        return self._virtualpointtype

    @virtualpointtype.setter
    def virtualpointtype(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'virtualpointtype' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'virtualpointtype' field must be an unsigned integer in [0, 255]"
        self._virtualpointtype = value
