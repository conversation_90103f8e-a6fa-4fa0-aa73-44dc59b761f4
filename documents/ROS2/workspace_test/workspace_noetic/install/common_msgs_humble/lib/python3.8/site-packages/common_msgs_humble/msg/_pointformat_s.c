// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from common_msgs_humble:msg/Pointformat.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "common_msgs_humble/msg/detail/pointformat__struct.h"
#include "common_msgs_humble/msg/detail/pointformat__functions.h"

#include "rosidl_runtime_c/string.h"
#include "rosidl_runtime_c/string_functions.h"


ROSIDL_GENERATOR_C_EXPORT
bool common_msgs_humble__msg__pointformat__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[48];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("common_msgs_humble.msg._pointformat.Pointformat", full_classname_dest, 47) == 0);
  }
  common_msgs_humble__msg__Pointformat * ros_message = _ros_message;
  {  // lon
    PyObject * field = PyObject_GetAttrString(_pymsg, "lon");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->lon = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // lat
    PyObject * field = PyObject_GetAttrString(_pymsg, "lat");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->lat = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // heading
    PyObject * field = PyObject_GetAttrString(_pymsg, "heading");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->heading = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // index
    PyObject * field = PyObject_GetAttrString(_pymsg, "index");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->index = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // backup1
    PyObject * field = PyObject_GetAttrString(_pymsg, "backup1");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->backup1 = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // backup2
    PyObject * field = PyObject_GetAttrString(_pymsg, "backup2");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->backup2 = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // backup3
    PyObject * field = PyObject_GetAttrString(_pymsg, "backup3");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->backup3 = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // backup4
    PyObject * field = PyObject_GetAttrString(_pymsg, "backup4");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->backup4 = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // backup5
    PyObject * field = PyObject_GetAttrString(_pymsg, "backup5");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->backup5 = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // backup6
    PyObject * field = PyObject_GetAttrString(_pymsg, "backup6");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->backup6 = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // backup7
    PyObject * field = PyObject_GetAttrString(_pymsg, "backup7");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->backup7 = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // backup8
    PyObject * field = PyObject_GetAttrString(_pymsg, "backup8");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->backup8 = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // backup9
    PyObject * field = PyObject_GetAttrString(_pymsg, "backup9");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->backup9 = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // path
    PyObject * field = PyObject_GetAttrString(_pymsg, "path");
    if (!field) {
      return false;
    }
    assert(PyUnicode_Check(field));
    PyObject * encoded_field = PyUnicode_AsUTF8String(field);
    if (!encoded_field) {
      Py_DECREF(field);
      return false;
    }
    rosidl_runtime_c__String__assign(&ros_message->path, PyBytes_AS_STRING(encoded_field));
    Py_DECREF(encoded_field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * common_msgs_humble__msg__pointformat__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of Pointformat */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("common_msgs_humble.msg._pointformat");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "Pointformat");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  common_msgs_humble__msg__Pointformat * ros_message = (common_msgs_humble__msg__Pointformat *)raw_ros_message;
  {  // lon
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->lon);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lon", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lat
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->lat);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lat", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // heading
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->heading);
    {
      int rc = PyObject_SetAttrString(_pymessage, "heading", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // index
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->index);
    {
      int rc = PyObject_SetAttrString(_pymessage, "index", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // backup1
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->backup1);
    {
      int rc = PyObject_SetAttrString(_pymessage, "backup1", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // backup2
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->backup2);
    {
      int rc = PyObject_SetAttrString(_pymessage, "backup2", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // backup3
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->backup3);
    {
      int rc = PyObject_SetAttrString(_pymessage, "backup3", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // backup4
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->backup4);
    {
      int rc = PyObject_SetAttrString(_pymessage, "backup4", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // backup5
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->backup5);
    {
      int rc = PyObject_SetAttrString(_pymessage, "backup5", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // backup6
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->backup6);
    {
      int rc = PyObject_SetAttrString(_pymessage, "backup6", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // backup7
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->backup7);
    {
      int rc = PyObject_SetAttrString(_pymessage, "backup7", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // backup8
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->backup8);
    {
      int rc = PyObject_SetAttrString(_pymessage, "backup8", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // backup9
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->backup9);
    {
      int rc = PyObject_SetAttrString(_pymessage, "backup9", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // path
    PyObject * field = NULL;
    field = PyUnicode_DecodeUTF8(
      ros_message->path.data,
      strlen(ros_message->path.data),
      "replace");
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "path", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
