# generated from rosidl_generator_py/resource/_idl.py.em
# with input from common_msgs_humble:msg/Monitor.idl
# generated code does not contain a copyright notice


# Import statements for member types

# Member 'valuetext'
import array  # noqa: E402, I100

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_Monitor(type):
    """Metaclass of message 'Monitor'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('common_msgs_humble')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'common_msgs_humble.msg.Monitor')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__monitor
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__monitor
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__monitor
            cls._TYPE_SUPPORT = module.type_support_msg__msg__monitor
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__monitor

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class Monitor(metaclass=Metaclass_Monitor):
    """Message class 'Monitor'."""

    __slots__ = [
        '_valuelight',
        '_deslight',
        '_valuetext',
        '_destext',
        '_dotcnt',
        '_timestamp',
        '_status',
        '_sensorstate',
    ]

    _fields_and_field_types = {
        'valuelight': 'sequence<octet>',
        'deslight': 'sequence<string>',
        'valuetext': 'sequence<double>',
        'destext': 'sequence<string>',
        'dotcnt': 'sequence<octet>',
        'timestamp': 'int64',
        'status': 'int64',
        'sensorstate': 'int64',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.BasicType('octet')),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.UnboundedString()),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.BasicType('double')),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.UnboundedString()),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.BasicType('octet')),  # noqa: E501
        rosidl_parser.definition.BasicType('int64'),  # noqa: E501
        rosidl_parser.definition.BasicType('int64'),  # noqa: E501
        rosidl_parser.definition.BasicType('int64'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.valuelight = kwargs.get('valuelight', [])
        self.deslight = kwargs.get('deslight', [])
        self.valuetext = array.array('d', kwargs.get('valuetext', []))
        self.destext = kwargs.get('destext', [])
        self.dotcnt = kwargs.get('dotcnt', [])
        self.timestamp = kwargs.get('timestamp', int())
        self.status = kwargs.get('status', int())
        self.sensorstate = kwargs.get('sensorstate', int())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.valuelight != other.valuelight:
            return False
        if self.deslight != other.deslight:
            return False
        if self.valuetext != other.valuetext:
            return False
        if self.destext != other.destext:
            return False
        if self.dotcnt != other.dotcnt:
            return False
        if self.timestamp != other.timestamp:
            return False
        if self.status != other.status:
            return False
        if self.sensorstate != other.sensorstate:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def valuelight(self):
        """Message field 'valuelight'."""
        return self._valuelight

    @valuelight.setter
    def valuelight(self, value):
        if __debug__:
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, bytes) for v in value) and
                 True), \
                "The 'valuelight' field must be a set or sequence and each value of type 'bytes'"
        self._valuelight = value

    @builtins.property
    def deslight(self):
        """Message field 'deslight'."""
        return self._deslight

    @deslight.setter
    def deslight(self, value):
        if __debug__:
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, str) for v in value) and
                 True), \
                "The 'deslight' field must be a set or sequence and each value of type 'str'"
        self._deslight = value

    @builtins.property
    def valuetext(self):
        """Message field 'valuetext'."""
        return self._valuetext

    @valuetext.setter
    def valuetext(self, value):
        if isinstance(value, array.array):
            assert value.typecode == 'd', \
                "The 'valuetext' array.array() must have the type code of 'd'"
            self._valuetext = value
            return
        if __debug__:
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, float) for v in value) and
                 all(not (val < -1.7976931348623157e+308 or val > 1.7976931348623157e+308) or math.isinf(val) for val in value)), \
                "The 'valuetext' field must be a set or sequence and each value of type 'float' and each double in [-179769313486231570814527423731704356798070567525844996598917476803157260780028538760589558632766878171540458953514382464234321326889464182768467546703537516986049910576551282076245490090389328944075868508455133942304583236903222948165808559332123348274797826204144723168738177180919299881250404026184124858368.000000, 179769313486231570814527423731704356798070567525844996598917476803157260780028538760589558632766878171540458953514382464234321326889464182768467546703537516986049910576551282076245490090389328944075868508455133942304583236903222948165808559332123348274797826204144723168738177180919299881250404026184124858368.000000]"
        self._valuetext = array.array('d', value)

    @builtins.property
    def destext(self):
        """Message field 'destext'."""
        return self._destext

    @destext.setter
    def destext(self, value):
        if __debug__:
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, str) for v in value) and
                 True), \
                "The 'destext' field must be a set or sequence and each value of type 'str'"
        self._destext = value

    @builtins.property
    def dotcnt(self):
        """Message field 'dotcnt'."""
        return self._dotcnt

    @dotcnt.setter
    def dotcnt(self, value):
        if __debug__:
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, bytes) for v in value) and
                 True), \
                "The 'dotcnt' field must be a set or sequence and each value of type 'bytes'"
        self._dotcnt = value

    @builtins.property
    def timestamp(self):
        """Message field 'timestamp'."""
        return self._timestamp

    @timestamp.setter
    def timestamp(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'timestamp' field must be of type 'int'"
            assert value >= -9223372036854775808 and value < 9223372036854775808, \
                "The 'timestamp' field must be an integer in [-9223372036854775808, 9223372036854775807]"
        self._timestamp = value

    @builtins.property
    def status(self):
        """Message field 'status'."""
        return self._status

    @status.setter
    def status(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'status' field must be of type 'int'"
            assert value >= -9223372036854775808 and value < 9223372036854775808, \
                "The 'status' field must be an integer in [-9223372036854775808, 9223372036854775807]"
        self._status = value

    @builtins.property
    def sensorstate(self):
        """Message field 'sensorstate'."""
        return self._sensorstate

    @sensorstate.setter
    def sensorstate(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'sensorstate' field must be of type 'int'"
            assert value >= -9223372036854775808 and value < 9223372036854775808, \
                "The 'sensorstate' field must be an integer in [-9223372036854775808, 9223372036854775807]"
        self._sensorstate = value
