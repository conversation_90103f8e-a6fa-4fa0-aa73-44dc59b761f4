# generated from rosidl_generator_py/resource/_idl.py.em
# with input from common_msgs_humble:msg/ParkingActive.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_ParkingActive(type):
    """Metaclass of message 'ParkingActive'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('common_msgs_humble')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'common_msgs_humble.msg.ParkingActive')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__parking_active
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__parking_active
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__parking_active
            cls._TYPE_SUPPORT = module.type_support_msg__msg__parking_active
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__parking_active

            from common_msgs_humble.msg import Ieku
            if Ieku.__class__._TYPE_SUPPORT is None:
                Ieku.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class ParkingActive(metaclass=Metaclass_ParkingActive):
    """Message class 'ParkingActive'."""

    __slots__ = [
        '_timestamp',
        '_stage',
        '_tips',
        '_answer',
        '_iekulist',
        '_iekutargetid',
        '_driveout',
        '_stop_parking',
    ]

    _fields_and_field_types = {
        'timestamp': 'int64',
        'stage': 'uint8',
        'tips': 'uint8',
        'answer': 'uint8',
        'iekulist': 'sequence<common_msgs_humble/Ieku>',
        'iekutargetid': 'uint8',
        'driveout': 'uint8',
        'stop_parking': 'uint8',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('int64'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['common_msgs_humble', 'msg'], 'Ieku')),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.timestamp = kwargs.get('timestamp', int())
        self.stage = kwargs.get('stage', int())
        self.tips = kwargs.get('tips', int())
        self.answer = kwargs.get('answer', int())
        self.iekulist = kwargs.get('iekulist', [])
        self.iekutargetid = kwargs.get('iekutargetid', int())
        self.driveout = kwargs.get('driveout', int())
        self.stop_parking = kwargs.get('stop_parking', int())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.timestamp != other.timestamp:
            return False
        if self.stage != other.stage:
            return False
        if self.tips != other.tips:
            return False
        if self.answer != other.answer:
            return False
        if self.iekulist != other.iekulist:
            return False
        if self.iekutargetid != other.iekutargetid:
            return False
        if self.driveout != other.driveout:
            return False
        if self.stop_parking != other.stop_parking:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def timestamp(self):
        """Message field 'timestamp'."""
        return self._timestamp

    @timestamp.setter
    def timestamp(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'timestamp' field must be of type 'int'"
            assert value >= -9223372036854775808 and value < 9223372036854775808, \
                "The 'timestamp' field must be an integer in [-9223372036854775808, 9223372036854775807]"
        self._timestamp = value

    @builtins.property
    def stage(self):
        """Message field 'stage'."""
        return self._stage

    @stage.setter
    def stage(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'stage' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'stage' field must be an unsigned integer in [0, 255]"
        self._stage = value

    @builtins.property
    def tips(self):
        """Message field 'tips'."""
        return self._tips

    @tips.setter
    def tips(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'tips' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'tips' field must be an unsigned integer in [0, 255]"
        self._tips = value

    @builtins.property
    def answer(self):
        """Message field 'answer'."""
        return self._answer

    @answer.setter
    def answer(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'answer' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'answer' field must be an unsigned integer in [0, 255]"
        self._answer = value

    @builtins.property
    def iekulist(self):
        """Message field 'iekulist'."""
        return self._iekulist

    @iekulist.setter
    def iekulist(self, value):
        if __debug__:
            from common_msgs_humble.msg import Ieku
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, Ieku) for v in value) and
                 True), \
                "The 'iekulist' field must be a set or sequence and each value of type 'Ieku'"
        self._iekulist = value

    @builtins.property
    def iekutargetid(self):
        """Message field 'iekutargetid'."""
        return self._iekutargetid

    @iekutargetid.setter
    def iekutargetid(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'iekutargetid' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'iekutargetid' field must be an unsigned integer in [0, 255]"
        self._iekutargetid = value

    @builtins.property
    def driveout(self):
        """Message field 'driveout'."""
        return self._driveout

    @driveout.setter
    def driveout(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'driveout' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'driveout' field must be an unsigned integer in [0, 255]"
        self._driveout = value

    @builtins.property
    def stop_parking(self):
        """Message field 'stop_parking'."""
        return self._stop_parking

    @stop_parking.setter
    def stop_parking(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'stop_parking' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'stop_parking' field must be an unsigned integer in [0, 255]"
        self._stop_parking = value
