# generated from rosidl_generator_py/resource/_idl.py.em
# with input from common_msgs_humble:msg/Objectprediction.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_Objectprediction(type):
    """Metaclass of message 'Objectprediction'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('common_msgs_humble')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'common_msgs_humble.msg.Objectprediction')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__objectprediction
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__objectprediction
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__objectprediction
            cls._TYPE_SUPPORT = module.type_support_msg__msg__objectprediction
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__objectprediction

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class Objectprediction(metaclass=Metaclass_Objectprediction):
    """Message class 'Objectprediction'."""

    __slots__ = [
        '_timestep',
        '_x',
        '_y',
        '_z',
        '_longtitude',
        '_latitude',
        '_altitude',
        '_rollrad',
        '_pitchrad',
        '_azimuth',
        '_relavx',
        '_relavy',
        '_absvx',
        '_absvy',
        '_heading',
        '_s',
        '_l',
        '_speeds',
        '_speedl',
    ]

    _fields_and_field_types = {
        'timestep': 'float',
        'x': 'float',
        'y': 'float',
        'z': 'float',
        'longtitude': 'double',
        'latitude': 'double',
        'altitude': 'double',
        'rollrad': 'float',
        'pitchrad': 'float',
        'azimuth': 'float',
        'relavx': 'float',
        'relavy': 'float',
        'absvx': 'float',
        'absvy': 'float',
        'heading': 'float',
        's': 'float',
        'l': 'float',
        'speeds': 'float',
        'speedl': 'float',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.timestep = kwargs.get('timestep', float())
        self.x = kwargs.get('x', float())
        self.y = kwargs.get('y', float())
        self.z = kwargs.get('z', float())
        self.longtitude = kwargs.get('longtitude', float())
        self.latitude = kwargs.get('latitude', float())
        self.altitude = kwargs.get('altitude', float())
        self.rollrad = kwargs.get('rollrad', float())
        self.pitchrad = kwargs.get('pitchrad', float())
        self.azimuth = kwargs.get('azimuth', float())
        self.relavx = kwargs.get('relavx', float())
        self.relavy = kwargs.get('relavy', float())
        self.absvx = kwargs.get('absvx', float())
        self.absvy = kwargs.get('absvy', float())
        self.heading = kwargs.get('heading', float())
        self.s = kwargs.get('s', float())
        self.l = kwargs.get('l', float())
        self.speeds = kwargs.get('speeds', float())
        self.speedl = kwargs.get('speedl', float())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.timestep != other.timestep:
            return False
        if self.x != other.x:
            return False
        if self.y != other.y:
            return False
        if self.z != other.z:
            return False
        if self.longtitude != other.longtitude:
            return False
        if self.latitude != other.latitude:
            return False
        if self.altitude != other.altitude:
            return False
        if self.rollrad != other.rollrad:
            return False
        if self.pitchrad != other.pitchrad:
            return False
        if self.azimuth != other.azimuth:
            return False
        if self.relavx != other.relavx:
            return False
        if self.relavy != other.relavy:
            return False
        if self.absvx != other.absvx:
            return False
        if self.absvy != other.absvy:
            return False
        if self.heading != other.heading:
            return False
        if self.s != other.s:
            return False
        if self.l != other.l:
            return False
        if self.speeds != other.speeds:
            return False
        if self.speedl != other.speedl:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def timestep(self):
        """Message field 'timestep'."""
        return self._timestep

    @timestep.setter
    def timestep(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'timestep' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'timestep' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._timestep = value

    @builtins.property
    def x(self):
        """Message field 'x'."""
        return self._x

    @x.setter
    def x(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'x' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'x' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._x = value

    @builtins.property
    def y(self):
        """Message field 'y'."""
        return self._y

    @y.setter
    def y(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'y' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'y' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._y = value

    @builtins.property
    def z(self):
        """Message field 'z'."""
        return self._z

    @z.setter
    def z(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'z' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'z' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._z = value

    @builtins.property
    def longtitude(self):
        """Message field 'longtitude'."""
        return self._longtitude

    @longtitude.setter
    def longtitude(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'longtitude' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'longtitude' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._longtitude = value

    @builtins.property
    def latitude(self):
        """Message field 'latitude'."""
        return self._latitude

    @latitude.setter
    def latitude(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'latitude' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'latitude' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._latitude = value

    @builtins.property
    def altitude(self):
        """Message field 'altitude'."""
        return self._altitude

    @altitude.setter
    def altitude(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'altitude' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'altitude' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._altitude = value

    @builtins.property
    def rollrad(self):
        """Message field 'rollrad'."""
        return self._rollrad

    @rollrad.setter
    def rollrad(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'rollrad' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'rollrad' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._rollrad = value

    @builtins.property
    def pitchrad(self):
        """Message field 'pitchrad'."""
        return self._pitchrad

    @pitchrad.setter
    def pitchrad(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'pitchrad' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'pitchrad' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._pitchrad = value

    @builtins.property
    def azimuth(self):
        """Message field 'azimuth'."""
        return self._azimuth

    @azimuth.setter
    def azimuth(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'azimuth' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'azimuth' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._azimuth = value

    @builtins.property
    def relavx(self):
        """Message field 'relavx'."""
        return self._relavx

    @relavx.setter
    def relavx(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'relavx' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'relavx' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._relavx = value

    @builtins.property
    def relavy(self):
        """Message field 'relavy'."""
        return self._relavy

    @relavy.setter
    def relavy(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'relavy' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'relavy' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._relavy = value

    @builtins.property
    def absvx(self):
        """Message field 'absvx'."""
        return self._absvx

    @absvx.setter
    def absvx(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'absvx' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'absvx' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._absvx = value

    @builtins.property
    def absvy(self):
        """Message field 'absvy'."""
        return self._absvy

    @absvy.setter
    def absvy(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'absvy' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'absvy' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._absvy = value

    @builtins.property
    def heading(self):
        """Message field 'heading'."""
        return self._heading

    @heading.setter
    def heading(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'heading' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'heading' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._heading = value

    @builtins.property
    def s(self):
        """Message field 's'."""
        return self._s

    @s.setter
    def s(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 's' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 's' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._s = value

    @builtins.property
    def l(self):
        """Message field 'l'."""
        return self._l

    @l.setter
    def l(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'l' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'l' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._l = value

    @builtins.property
    def speeds(self):
        """Message field 'speeds'."""
        return self._speeds

    @speeds.setter
    def speeds(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'speeds' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'speeds' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._speeds = value

    @builtins.property
    def speedl(self):
        """Message field 'speedl'."""
        return self._speedl

    @speedl.setter
    def speedl(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'speedl' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'speedl' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._speedl = value
