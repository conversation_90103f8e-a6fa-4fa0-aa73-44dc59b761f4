// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from common_msgs_humble:msg/Fusiontrackingobject.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "common_msgs_humble/msg/detail/fusiontrackingobject__struct.h"
#include "common_msgs_humble/msg/detail/fusiontrackingobject__functions.h"

bool common_msgs_humble__msg__sensorobject__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * common_msgs_humble__msg__sensorobject__convert_to_py(void * raw_ros_message);
bool common_msgs_humble__msg__sensorobject__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * common_msgs_humble__msg__sensorobject__convert_to_py(void * raw_ros_message);
bool common_msgs_humble__msg__sensorobject__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * common_msgs_humble__msg__sensorobject__convert_to_py(void * raw_ros_message);
bool common_msgs_humble__msg__obupant__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * common_msgs_humble__msg__obupant__convert_to_py(void * raw_ros_message);

ROSIDL_GENERATOR_C_EXPORT
bool common_msgs_humble__msg__fusiontrackingobject__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[66];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("common_msgs_humble.msg._fusiontrackingobject.Fusiontrackingobject", full_classname_dest, 65) == 0);
  }
  common_msgs_humble__msg__Fusiontrackingobject * ros_message = _ros_message;
  {  // objectsource
    PyObject * field = PyObject_GetAttrString(_pymsg, "objectsource");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->objectsource = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // lidartimestamp
    PyObject * field = PyObject_GetAttrString(_pymsg, "lidartimestamp");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->lidartimestamp = PyLong_AsLongLong(field);
    Py_DECREF(field);
  }
  {  // lidargpstime
    PyObject * field = PyObject_GetAttrString(_pymsg, "lidargpstime");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->lidargpstime = PyLong_AsLongLong(field);
    Py_DECREF(field);
  }
  {  // lidarobject
    PyObject * field = PyObject_GetAttrString(_pymsg, "lidarobject");
    if (!field) {
      return false;
    }
    if (!common_msgs_humble__msg__sensorobject__convert_from_py(field, &ros_message->lidarobject)) {
      Py_DECREF(field);
      return false;
    }
    Py_DECREF(field);
  }
  {  // radartimestamp
    PyObject * field = PyObject_GetAttrString(_pymsg, "radartimestamp");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->radartimestamp = PyLong_AsLongLong(field);
    Py_DECREF(field);
  }
  {  // radargpstime
    PyObject * field = PyObject_GetAttrString(_pymsg, "radargpstime");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->radargpstime = PyLong_AsLongLong(field);
    Py_DECREF(field);
  }
  {  // radarobject
    PyObject * field = PyObject_GetAttrString(_pymsg, "radarobject");
    if (!field) {
      return false;
    }
    if (!common_msgs_humble__msg__sensorobject__convert_from_py(field, &ros_message->radarobject)) {
      Py_DECREF(field);
      return false;
    }
    Py_DECREF(field);
  }
  {  // obutimestamp
    PyObject * field = PyObject_GetAttrString(_pymsg, "obutimestamp");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->obutimestamp = PyLong_AsLongLong(field);
    Py_DECREF(field);
  }
  {  // obugpstime
    PyObject * field = PyObject_GetAttrString(_pymsg, "obugpstime");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->obugpstime = PyLong_AsLongLong(field);
    Py_DECREF(field);
  }
  {  // obuobject
    PyObject * field = PyObject_GetAttrString(_pymsg, "obuobject");
    if (!field) {
      return false;
    }
    if (!common_msgs_humble__msg__sensorobject__convert_from_py(field, &ros_message->obuobject)) {
      Py_DECREF(field);
      return false;
    }
    Py_DECREF(field);
  }
  {  // obupantobject
    PyObject * field = PyObject_GetAttrString(_pymsg, "obupantobject");
    if (!field) {
      return false;
    }
    if (!common_msgs_humble__msg__obupant__convert_from_py(field, &ros_message->obupantobject)) {
      Py_DECREF(field);
      return false;
    }
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * common_msgs_humble__msg__fusiontrackingobject__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of Fusiontrackingobject */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("common_msgs_humble.msg._fusiontrackingobject");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "Fusiontrackingobject");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  common_msgs_humble__msg__Fusiontrackingobject * ros_message = (common_msgs_humble__msg__Fusiontrackingobject *)raw_ros_message;
  {  // objectsource
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->objectsource);
    {
      int rc = PyObject_SetAttrString(_pymessage, "objectsource", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lidartimestamp
    PyObject * field = NULL;
    field = PyLong_FromLongLong(ros_message->lidartimestamp);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lidartimestamp", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lidargpstime
    PyObject * field = NULL;
    field = PyLong_FromLongLong(ros_message->lidargpstime);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lidargpstime", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lidarobject
    PyObject * field = NULL;
    field = common_msgs_humble__msg__sensorobject__convert_to_py(&ros_message->lidarobject);
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "lidarobject", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // radartimestamp
    PyObject * field = NULL;
    field = PyLong_FromLongLong(ros_message->radartimestamp);
    {
      int rc = PyObject_SetAttrString(_pymessage, "radartimestamp", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // radargpstime
    PyObject * field = NULL;
    field = PyLong_FromLongLong(ros_message->radargpstime);
    {
      int rc = PyObject_SetAttrString(_pymessage, "radargpstime", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // radarobject
    PyObject * field = NULL;
    field = common_msgs_humble__msg__sensorobject__convert_to_py(&ros_message->radarobject);
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "radarobject", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // obutimestamp
    PyObject * field = NULL;
    field = PyLong_FromLongLong(ros_message->obutimestamp);
    {
      int rc = PyObject_SetAttrString(_pymessage, "obutimestamp", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // obugpstime
    PyObject * field = NULL;
    field = PyLong_FromLongLong(ros_message->obugpstime);
    {
      int rc = PyObject_SetAttrString(_pymessage, "obugpstime", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // obuobject
    PyObject * field = NULL;
    field = common_msgs_humble__msg__sensorobject__convert_to_py(&ros_message->obuobject);
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "obuobject", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // obupantobject
    PyObject * field = NULL;
    field = common_msgs_humble__msg__obupant__convert_to_py(&ros_message->obupantobject);
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "obupantobject", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
