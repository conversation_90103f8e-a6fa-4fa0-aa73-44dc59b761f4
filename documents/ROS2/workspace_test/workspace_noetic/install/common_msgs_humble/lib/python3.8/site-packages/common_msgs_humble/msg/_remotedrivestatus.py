# generated from rosidl_generator_py/resource/_idl.py.em
# with input from common_msgs_humble:msg/Remotedrivestatus.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_Remotedrivestatus(type):
    """Metaclass of message 'Remotedrivestatus'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('common_msgs_humble')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'common_msgs_humble.msg.Remotedrivestatus')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__remotedrivestatus
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__remotedrivestatus
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__remotedrivestatus
            cls._TYPE_SUPPORT = module.type_support_msg__msg__remotedrivestatus
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__remotedrivestatus

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class Remotedrivestatus(metaclass=Metaclass_Remotedrivestatus):
    """Message class 'Remotedrivestatus'."""

    __slots__ = [
        '_timestamp',
        '_drivemode',
        '_remotedrivestatus',
        '_systemstatus',
    ]

    _fields_and_field_types = {
        'timestamp': 'int64',
        'drivemode': 'uint8',
        'remotedrivestatus': 'uint8',
        'systemstatus': 'uint8',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('int64'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.timestamp = kwargs.get('timestamp', int())
        self.drivemode = kwargs.get('drivemode', int())
        self.remotedrivestatus = kwargs.get('remotedrivestatus', int())
        self.systemstatus = kwargs.get('systemstatus', int())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.timestamp != other.timestamp:
            return False
        if self.drivemode != other.drivemode:
            return False
        if self.remotedrivestatus != other.remotedrivestatus:
            return False
        if self.systemstatus != other.systemstatus:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def timestamp(self):
        """Message field 'timestamp'."""
        return self._timestamp

    @timestamp.setter
    def timestamp(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'timestamp' field must be of type 'int'"
            assert value >= -9223372036854775808 and value < 9223372036854775808, \
                "The 'timestamp' field must be an integer in [-9223372036854775808, 9223372036854775807]"
        self._timestamp = value

    @builtins.property
    def drivemode(self):
        """Message field 'drivemode'."""
        return self._drivemode

    @drivemode.setter
    def drivemode(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'drivemode' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'drivemode' field must be an unsigned integer in [0, 255]"
        self._drivemode = value

    @builtins.property
    def remotedrivestatus(self):
        """Message field 'remotedrivestatus'."""
        return self._remotedrivestatus

    @remotedrivestatus.setter
    def remotedrivestatus(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'remotedrivestatus' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'remotedrivestatus' field must be an unsigned integer in [0, 255]"
        self._remotedrivestatus = value

    @builtins.property
    def systemstatus(self):
        """Message field 'systemstatus'."""
        return self._systemstatus

    @systemstatus.setter
    def systemstatus(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'systemstatus' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'systemstatus' field must be an unsigned integer in [0, 255]"
        self._systemstatus = value
