# generated from rosidl_generator_py/resource/_idl.py.em
# with input from common_msgs_humble:msg/Controllat.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_Controllat(type):
    """Metaclass of message 'Controllat'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('common_msgs_humble')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'common_msgs_humble.msg.Controllat')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__controllat
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__controllat
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__controllat
            cls._TYPE_SUPPORT = module.type_support_msg__msg__controllat
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__controllat

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class Controllat(metaclass=Metaclass_Controllat):
    """Message class 'Controllat'."""

    __slots__ = [
        '_epsmethod',
        '_epsangle',
        '_limitspeed',
        '_epstorque',
        '_lightmethod',
        '_lights',
        '_isvalid',
        '_deviation',
        '_timestamp',
        '_apavstatus',
        '_apsstate',
        '_curve',
    ]

    _fields_and_field_types = {
        'epsmethod': 'uint8',
        'epsangle': 'int16',
        'limitspeed': 'float',
        'epstorque': 'int16',
        'lightmethod': 'uint8',
        'lights': 'uint8',
        'isvalid': 'uint8',
        'deviation': 'int16',
        'timestamp': 'int64',
        'apavstatus': 'uint8',
        'apsstate': 'uint8',
        'curve': 'float',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('int16'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('int16'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('int16'),  # noqa: E501
        rosidl_parser.definition.BasicType('int64'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.epsmethod = kwargs.get('epsmethod', int())
        self.epsangle = kwargs.get('epsangle', int())
        self.limitspeed = kwargs.get('limitspeed', float())
        self.epstorque = kwargs.get('epstorque', int())
        self.lightmethod = kwargs.get('lightmethod', int())
        self.lights = kwargs.get('lights', int())
        self.isvalid = kwargs.get('isvalid', int())
        self.deviation = kwargs.get('deviation', int())
        self.timestamp = kwargs.get('timestamp', int())
        self.apavstatus = kwargs.get('apavstatus', int())
        self.apsstate = kwargs.get('apsstate', int())
        self.curve = kwargs.get('curve', float())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.epsmethod != other.epsmethod:
            return False
        if self.epsangle != other.epsangle:
            return False
        if self.limitspeed != other.limitspeed:
            return False
        if self.epstorque != other.epstorque:
            return False
        if self.lightmethod != other.lightmethod:
            return False
        if self.lights != other.lights:
            return False
        if self.isvalid != other.isvalid:
            return False
        if self.deviation != other.deviation:
            return False
        if self.timestamp != other.timestamp:
            return False
        if self.apavstatus != other.apavstatus:
            return False
        if self.apsstate != other.apsstate:
            return False
        if self.curve != other.curve:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def epsmethod(self):
        """Message field 'epsmethod'."""
        return self._epsmethod

    @epsmethod.setter
    def epsmethod(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'epsmethod' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'epsmethod' field must be an unsigned integer in [0, 255]"
        self._epsmethod = value

    @builtins.property
    def epsangle(self):
        """Message field 'epsangle'."""
        return self._epsangle

    @epsangle.setter
    def epsangle(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'epsangle' field must be of type 'int'"
            assert value >= -32768 and value < 32768, \
                "The 'epsangle' field must be an integer in [-32768, 32767]"
        self._epsangle = value

    @builtins.property
    def limitspeed(self):
        """Message field 'limitspeed'."""
        return self._limitspeed

    @limitspeed.setter
    def limitspeed(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'limitspeed' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'limitspeed' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._limitspeed = value

    @builtins.property
    def epstorque(self):
        """Message field 'epstorque'."""
        return self._epstorque

    @epstorque.setter
    def epstorque(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'epstorque' field must be of type 'int'"
            assert value >= -32768 and value < 32768, \
                "The 'epstorque' field must be an integer in [-32768, 32767]"
        self._epstorque = value

    @builtins.property
    def lightmethod(self):
        """Message field 'lightmethod'."""
        return self._lightmethod

    @lightmethod.setter
    def lightmethod(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'lightmethod' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'lightmethod' field must be an unsigned integer in [0, 255]"
        self._lightmethod = value

    @builtins.property
    def lights(self):
        """Message field 'lights'."""
        return self._lights

    @lights.setter
    def lights(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'lights' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'lights' field must be an unsigned integer in [0, 255]"
        self._lights = value

    @builtins.property
    def isvalid(self):
        """Message field 'isvalid'."""
        return self._isvalid

    @isvalid.setter
    def isvalid(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'isvalid' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'isvalid' field must be an unsigned integer in [0, 255]"
        self._isvalid = value

    @builtins.property
    def deviation(self):
        """Message field 'deviation'."""
        return self._deviation

    @deviation.setter
    def deviation(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'deviation' field must be of type 'int'"
            assert value >= -32768 and value < 32768, \
                "The 'deviation' field must be an integer in [-32768, 32767]"
        self._deviation = value

    @builtins.property
    def timestamp(self):
        """Message field 'timestamp'."""
        return self._timestamp

    @timestamp.setter
    def timestamp(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'timestamp' field must be of type 'int'"
            assert value >= -9223372036854775808 and value < 9223372036854775808, \
                "The 'timestamp' field must be an integer in [-9223372036854775808, 9223372036854775807]"
        self._timestamp = value

    @builtins.property
    def apavstatus(self):
        """Message field 'apavstatus'."""
        return self._apavstatus

    @apavstatus.setter
    def apavstatus(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'apavstatus' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'apavstatus' field must be an unsigned integer in [0, 255]"
        self._apavstatus = value

    @builtins.property
    def apsstate(self):
        """Message field 'apsstate'."""
        return self._apsstate

    @apsstate.setter
    def apsstate(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'apsstate' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'apsstate' field must be an unsigned integer in [0, 255]"
        self._apsstate = value

    @builtins.property
    def curve(self):
        """Message field 'curve'."""
        return self._curve

    @curve.setter
    def curve(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'curve' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'curve' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._curve = value
