// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from common_msgs_humble:msg/Objectprediction.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "common_msgs_humble/msg/detail/objectprediction__struct.h"
#include "common_msgs_humble/msg/detail/objectprediction__functions.h"


ROSIDL_GENERATOR_C_EXPORT
bool common_msgs_humble__msg__objectprediction__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[58];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("common_msgs_humble.msg._objectprediction.Objectprediction", full_classname_dest, 57) == 0);
  }
  common_msgs_humble__msg__Objectprediction * ros_message = _ros_message;
  {  // timestep
    PyObject * field = PyObject_GetAttrString(_pymsg, "timestep");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->timestep = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // x
    PyObject * field = PyObject_GetAttrString(_pymsg, "x");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->x = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // y
    PyObject * field = PyObject_GetAttrString(_pymsg, "y");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->y = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // z
    PyObject * field = PyObject_GetAttrString(_pymsg, "z");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->z = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // longtitude
    PyObject * field = PyObject_GetAttrString(_pymsg, "longtitude");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->longtitude = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // latitude
    PyObject * field = PyObject_GetAttrString(_pymsg, "latitude");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->latitude = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // altitude
    PyObject * field = PyObject_GetAttrString(_pymsg, "altitude");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->altitude = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // rollrad
    PyObject * field = PyObject_GetAttrString(_pymsg, "rollrad");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->rollrad = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // pitchrad
    PyObject * field = PyObject_GetAttrString(_pymsg, "pitchrad");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->pitchrad = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // azimuth
    PyObject * field = PyObject_GetAttrString(_pymsg, "azimuth");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->azimuth = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // relavx
    PyObject * field = PyObject_GetAttrString(_pymsg, "relavx");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->relavx = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // relavy
    PyObject * field = PyObject_GetAttrString(_pymsg, "relavy");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->relavy = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // absvx
    PyObject * field = PyObject_GetAttrString(_pymsg, "absvx");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->absvx = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // absvy
    PyObject * field = PyObject_GetAttrString(_pymsg, "absvy");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->absvy = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // heading
    PyObject * field = PyObject_GetAttrString(_pymsg, "heading");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->heading = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // s
    PyObject * field = PyObject_GetAttrString(_pymsg, "s");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->s = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // l
    PyObject * field = PyObject_GetAttrString(_pymsg, "l");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->l = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // speeds
    PyObject * field = PyObject_GetAttrString(_pymsg, "speeds");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->speeds = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // speedl
    PyObject * field = PyObject_GetAttrString(_pymsg, "speedl");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->speedl = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * common_msgs_humble__msg__objectprediction__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of Objectprediction */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("common_msgs_humble.msg._objectprediction");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "Objectprediction");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  common_msgs_humble__msg__Objectprediction * ros_message = (common_msgs_humble__msg__Objectprediction *)raw_ros_message;
  {  // timestep
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->timestep);
    {
      int rc = PyObject_SetAttrString(_pymessage, "timestep", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // x
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->x);
    {
      int rc = PyObject_SetAttrString(_pymessage, "x", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // y
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->y);
    {
      int rc = PyObject_SetAttrString(_pymessage, "y", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // z
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->z);
    {
      int rc = PyObject_SetAttrString(_pymessage, "z", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // longtitude
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->longtitude);
    {
      int rc = PyObject_SetAttrString(_pymessage, "longtitude", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // latitude
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->latitude);
    {
      int rc = PyObject_SetAttrString(_pymessage, "latitude", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // altitude
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->altitude);
    {
      int rc = PyObject_SetAttrString(_pymessage, "altitude", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // rollrad
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->rollrad);
    {
      int rc = PyObject_SetAttrString(_pymessage, "rollrad", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // pitchrad
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->pitchrad);
    {
      int rc = PyObject_SetAttrString(_pymessage, "pitchrad", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // azimuth
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->azimuth);
    {
      int rc = PyObject_SetAttrString(_pymessage, "azimuth", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // relavx
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->relavx);
    {
      int rc = PyObject_SetAttrString(_pymessage, "relavx", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // relavy
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->relavy);
    {
      int rc = PyObject_SetAttrString(_pymessage, "relavy", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // absvx
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->absvx);
    {
      int rc = PyObject_SetAttrString(_pymessage, "absvx", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // absvy
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->absvy);
    {
      int rc = PyObject_SetAttrString(_pymessage, "absvy", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // heading
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->heading);
    {
      int rc = PyObject_SetAttrString(_pymessage, "heading", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // s
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->s);
    {
      int rc = PyObject_SetAttrString(_pymessage, "s", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // l
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->l);
    {
      int rc = PyObject_SetAttrString(_pymessage, "l", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // speeds
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->speeds);
    {
      int rc = PyObject_SetAttrString(_pymessage, "speeds", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // speedl
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->speedl);
    {
      int rc = PyObject_SetAttrString(_pymessage, "speedl", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
