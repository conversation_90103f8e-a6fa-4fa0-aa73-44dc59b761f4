// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from common_msgs_humble:msg/Collectmap.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "common_msgs_humble/msg/detail/collectmap__struct.h"
#include "common_msgs_humble/msg/detail/collectmap__functions.h"


ROSIDL_GENERATOR_C_EXPORT
bool common_msgs_humble__msg__collectmap__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[46];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("common_msgs_humble.msg._collectmap.Collectmap", full_classname_dest, 45) == 0);
  }
  common_msgs_humble__msg__Collectmap * ros_message = _ros_message;
  {  // mapname
    PyObject * field = PyObject_GetAttrString(_pymsg, "mapname");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->mapname = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // zonename
    PyObject * field = PyObject_GetAttrString(_pymsg, "zonename");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->zonename = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // property
    PyObject * field = PyObject_GetAttrString(_pymsg, "property");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->property = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // laneattr
    PyObject * field = PyObject_GetAttrString(_pymsg, "laneattr");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->laneattr = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // speed
    PyObject * field = PyObject_GetAttrString(_pymsg, "speed");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->speed = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // sideroadwidth
    PyObject * field = PyObject_GetAttrString(_pymsg, "sideroadwidth");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->sideroadwidth = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // mergelanetype
    PyObject * field = PyObject_GetAttrString(_pymsg, "mergelanetype");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->mergelanetype = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // sensorlanetype
    PyObject * field = PyObject_GetAttrString(_pymsg, "sensorlanetype");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->sensorlanetype = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // leftsearchdis
    PyObject * field = PyObject_GetAttrString(_pymsg, "leftsearchdis");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->leftsearchdis = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // rightsearchdis
    PyObject * field = PyObject_GetAttrString(_pymsg, "rightsearchdis");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->rightsearchdis = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // timestamp
    PyObject * field = PyObject_GetAttrString(_pymsg, "timestamp");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->timestamp = PyLong_AsLongLong(field);
    Py_DECREF(field);
  }
  {  // lanewidth
    PyObject * field = PyObject_GetAttrString(_pymsg, "lanewidth");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->lanewidth = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // leftlanewidth
    PyObject * field = PyObject_GetAttrString(_pymsg, "leftlanewidth");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->leftlanewidth = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // rightlanewidth
    PyObject * field = PyObject_GetAttrString(_pymsg, "rightlanewidth");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->rightlanewidth = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // laneswitch
    PyObject * field = PyObject_GetAttrString(_pymsg, "laneswitch");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->laneswitch = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // sidepass
    PyObject * field = PyObject_GetAttrString(_pymsg, "sidepass");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->sidepass = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // lanenum
    PyObject * field = PyObject_GetAttrString(_pymsg, "lanenum");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->lanenum = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // lanesite
    PyObject * field = PyObject_GetAttrString(_pymsg, "lanesite");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->lanesite = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * common_msgs_humble__msg__collectmap__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of Collectmap */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("common_msgs_humble.msg._collectmap");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "Collectmap");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  common_msgs_humble__msg__Collectmap * ros_message = (common_msgs_humble__msg__Collectmap *)raw_ros_message;
  {  // mapname
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->mapname);
    {
      int rc = PyObject_SetAttrString(_pymessage, "mapname", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // zonename
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->zonename);
    {
      int rc = PyObject_SetAttrString(_pymessage, "zonename", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // property
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->property);
    {
      int rc = PyObject_SetAttrString(_pymessage, "property", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // laneattr
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->laneattr);
    {
      int rc = PyObject_SetAttrString(_pymessage, "laneattr", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // speed
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->speed);
    {
      int rc = PyObject_SetAttrString(_pymessage, "speed", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // sideroadwidth
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->sideroadwidth);
    {
      int rc = PyObject_SetAttrString(_pymessage, "sideroadwidth", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // mergelanetype
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->mergelanetype);
    {
      int rc = PyObject_SetAttrString(_pymessage, "mergelanetype", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // sensorlanetype
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->sensorlanetype);
    {
      int rc = PyObject_SetAttrString(_pymessage, "sensorlanetype", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // leftsearchdis
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->leftsearchdis);
    {
      int rc = PyObject_SetAttrString(_pymessage, "leftsearchdis", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // rightsearchdis
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->rightsearchdis);
    {
      int rc = PyObject_SetAttrString(_pymessage, "rightsearchdis", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // timestamp
    PyObject * field = NULL;
    field = PyLong_FromLongLong(ros_message->timestamp);
    {
      int rc = PyObject_SetAttrString(_pymessage, "timestamp", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lanewidth
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->lanewidth);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lanewidth", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // leftlanewidth
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->leftlanewidth);
    {
      int rc = PyObject_SetAttrString(_pymessage, "leftlanewidth", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // rightlanewidth
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->rightlanewidth);
    {
      int rc = PyObject_SetAttrString(_pymessage, "rightlanewidth", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // laneswitch
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->laneswitch);
    {
      int rc = PyObject_SetAttrString(_pymessage, "laneswitch", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // sidepass
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->sidepass);
    {
      int rc = PyObject_SetAttrString(_pymessage, "sidepass", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lanenum
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->lanenum);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lanenum", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lanesite
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->lanesite);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lanesite", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
