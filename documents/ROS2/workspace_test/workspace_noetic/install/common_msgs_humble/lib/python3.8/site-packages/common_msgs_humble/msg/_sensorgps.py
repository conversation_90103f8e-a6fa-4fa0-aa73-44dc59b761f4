# generated from rosidl_generator_py/resource/_idl.py.em
# with input from common_msgs_humble:msg/Sensorgps.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_Sensorgps(type):
    """Metaclass of message 'Sensorgps'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('common_msgs_humble')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'common_msgs_humble.msg.Sensorgps')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__sensorgps
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__sensorgps
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__sensorgps
            cls._TYPE_SUPPORT = module.type_support_msg__msg__sensorgps
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__sensorgps

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class Sensorgps(metaclass=Metaclass_Sensorgps):
    """Message class 'Sensorgps'."""

    __slots__ = [
        '_lon',
        '_lat',
        '_alt',
        '_roadtype',
        '_lanetype',
        '_heading',
        '_pitch',
        '_roll',
        '_pitchrate',
        '_rollrate',
        '_yawrate',
        '_accx',
        '_accy',
        '_accz',
        '_mile',
        '_velocity',
        '_status',
        '_rawstatus',
        '_satenum',
        '_gpstime',
        '_isvalid',
        '_timestamp',
        '_speed_n',
        '_speed_e',
        '_speed_d',
    ]

    _fields_and_field_types = {
        'lon': 'double',
        'lat': 'double',
        'alt': 'double',
        'roadtype': 'uint8',
        'lanetype': 'uint8',
        'heading': 'double',
        'pitch': 'double',
        'roll': 'double',
        'pitchrate': 'double',
        'rollrate': 'double',
        'yawrate': 'double',
        'accx': 'double',
        'accy': 'double',
        'accz': 'double',
        'mile': 'double',
        'velocity': 'double',
        'status': 'uint8',
        'rawstatus': 'uint8',
        'satenum': 'uint8',
        'gpstime': 'int64',
        'isvalid': 'uint8',
        'timestamp': 'int64',
        'speed_n': 'double',
        'speed_e': 'double',
        'speed_d': 'double',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('int64'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('int64'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.lon = kwargs.get('lon', float())
        self.lat = kwargs.get('lat', float())
        self.alt = kwargs.get('alt', float())
        self.roadtype = kwargs.get('roadtype', int())
        self.lanetype = kwargs.get('lanetype', int())
        self.heading = kwargs.get('heading', float())
        self.pitch = kwargs.get('pitch', float())
        self.roll = kwargs.get('roll', float())
        self.pitchrate = kwargs.get('pitchrate', float())
        self.rollrate = kwargs.get('rollrate', float())
        self.yawrate = kwargs.get('yawrate', float())
        self.accx = kwargs.get('accx', float())
        self.accy = kwargs.get('accy', float())
        self.accz = kwargs.get('accz', float())
        self.mile = kwargs.get('mile', float())
        self.velocity = kwargs.get('velocity', float())
        self.status = kwargs.get('status', int())
        self.rawstatus = kwargs.get('rawstatus', int())
        self.satenum = kwargs.get('satenum', int())
        self.gpstime = kwargs.get('gpstime', int())
        self.isvalid = kwargs.get('isvalid', int())
        self.timestamp = kwargs.get('timestamp', int())
        self.speed_n = kwargs.get('speed_n', float())
        self.speed_e = kwargs.get('speed_e', float())
        self.speed_d = kwargs.get('speed_d', float())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.lon != other.lon:
            return False
        if self.lat != other.lat:
            return False
        if self.alt != other.alt:
            return False
        if self.roadtype != other.roadtype:
            return False
        if self.lanetype != other.lanetype:
            return False
        if self.heading != other.heading:
            return False
        if self.pitch != other.pitch:
            return False
        if self.roll != other.roll:
            return False
        if self.pitchrate != other.pitchrate:
            return False
        if self.rollrate != other.rollrate:
            return False
        if self.yawrate != other.yawrate:
            return False
        if self.accx != other.accx:
            return False
        if self.accy != other.accy:
            return False
        if self.accz != other.accz:
            return False
        if self.mile != other.mile:
            return False
        if self.velocity != other.velocity:
            return False
        if self.status != other.status:
            return False
        if self.rawstatus != other.rawstatus:
            return False
        if self.satenum != other.satenum:
            return False
        if self.gpstime != other.gpstime:
            return False
        if self.isvalid != other.isvalid:
            return False
        if self.timestamp != other.timestamp:
            return False
        if self.speed_n != other.speed_n:
            return False
        if self.speed_e != other.speed_e:
            return False
        if self.speed_d != other.speed_d:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def lon(self):
        """Message field 'lon'."""
        return self._lon

    @lon.setter
    def lon(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'lon' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'lon' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._lon = value

    @builtins.property
    def lat(self):
        """Message field 'lat'."""
        return self._lat

    @lat.setter
    def lat(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'lat' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'lat' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._lat = value

    @builtins.property
    def alt(self):
        """Message field 'alt'."""
        return self._alt

    @alt.setter
    def alt(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'alt' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'alt' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._alt = value

    @builtins.property
    def roadtype(self):
        """Message field 'roadtype'."""
        return self._roadtype

    @roadtype.setter
    def roadtype(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'roadtype' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'roadtype' field must be an unsigned integer in [0, 255]"
        self._roadtype = value

    @builtins.property
    def lanetype(self):
        """Message field 'lanetype'."""
        return self._lanetype

    @lanetype.setter
    def lanetype(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'lanetype' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'lanetype' field must be an unsigned integer in [0, 255]"
        self._lanetype = value

    @builtins.property
    def heading(self):
        """Message field 'heading'."""
        return self._heading

    @heading.setter
    def heading(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'heading' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'heading' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._heading = value

    @builtins.property
    def pitch(self):
        """Message field 'pitch'."""
        return self._pitch

    @pitch.setter
    def pitch(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'pitch' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'pitch' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._pitch = value

    @builtins.property
    def roll(self):
        """Message field 'roll'."""
        return self._roll

    @roll.setter
    def roll(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'roll' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'roll' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._roll = value

    @builtins.property
    def pitchrate(self):
        """Message field 'pitchrate'."""
        return self._pitchrate

    @pitchrate.setter
    def pitchrate(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'pitchrate' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'pitchrate' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._pitchrate = value

    @builtins.property
    def rollrate(self):
        """Message field 'rollrate'."""
        return self._rollrate

    @rollrate.setter
    def rollrate(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'rollrate' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'rollrate' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._rollrate = value

    @builtins.property
    def yawrate(self):
        """Message field 'yawrate'."""
        return self._yawrate

    @yawrate.setter
    def yawrate(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'yawrate' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'yawrate' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._yawrate = value

    @builtins.property
    def accx(self):
        """Message field 'accx'."""
        return self._accx

    @accx.setter
    def accx(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'accx' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'accx' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._accx = value

    @builtins.property
    def accy(self):
        """Message field 'accy'."""
        return self._accy

    @accy.setter
    def accy(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'accy' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'accy' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._accy = value

    @builtins.property
    def accz(self):
        """Message field 'accz'."""
        return self._accz

    @accz.setter
    def accz(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'accz' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'accz' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._accz = value

    @builtins.property
    def mile(self):
        """Message field 'mile'."""
        return self._mile

    @mile.setter
    def mile(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'mile' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'mile' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._mile = value

    @builtins.property
    def velocity(self):
        """Message field 'velocity'."""
        return self._velocity

    @velocity.setter
    def velocity(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'velocity' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'velocity' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._velocity = value

    @builtins.property
    def status(self):
        """Message field 'status'."""
        return self._status

    @status.setter
    def status(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'status' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'status' field must be an unsigned integer in [0, 255]"
        self._status = value

    @builtins.property
    def rawstatus(self):
        """Message field 'rawstatus'."""
        return self._rawstatus

    @rawstatus.setter
    def rawstatus(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'rawstatus' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'rawstatus' field must be an unsigned integer in [0, 255]"
        self._rawstatus = value

    @builtins.property
    def satenum(self):
        """Message field 'satenum'."""
        return self._satenum

    @satenum.setter
    def satenum(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'satenum' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'satenum' field must be an unsigned integer in [0, 255]"
        self._satenum = value

    @builtins.property
    def gpstime(self):
        """Message field 'gpstime'."""
        return self._gpstime

    @gpstime.setter
    def gpstime(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'gpstime' field must be of type 'int'"
            assert value >= -9223372036854775808 and value < 9223372036854775808, \
                "The 'gpstime' field must be an integer in [-9223372036854775808, 9223372036854775807]"
        self._gpstime = value

    @builtins.property
    def isvalid(self):
        """Message field 'isvalid'."""
        return self._isvalid

    @isvalid.setter
    def isvalid(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'isvalid' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'isvalid' field must be an unsigned integer in [0, 255]"
        self._isvalid = value

    @builtins.property
    def timestamp(self):
        """Message field 'timestamp'."""
        return self._timestamp

    @timestamp.setter
    def timestamp(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'timestamp' field must be of type 'int'"
            assert value >= -9223372036854775808 and value < 9223372036854775808, \
                "The 'timestamp' field must be an integer in [-9223372036854775808, 9223372036854775807]"
        self._timestamp = value

    @builtins.property
    def speed_n(self):
        """Message field 'speed_n'."""
        return self._speed_n

    @speed_n.setter
    def speed_n(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'speed_n' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'speed_n' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._speed_n = value

    @builtins.property
    def speed_e(self):
        """Message field 'speed_e'."""
        return self._speed_e

    @speed_e.setter
    def speed_e(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'speed_e' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'speed_e' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._speed_e = value

    @builtins.property
    def speed_d(self):
        """Message field 'speed_d'."""
        return self._speed_d

    @speed_d.setter
    def speed_d(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'speed_d' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'speed_d' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._speed_d = value
