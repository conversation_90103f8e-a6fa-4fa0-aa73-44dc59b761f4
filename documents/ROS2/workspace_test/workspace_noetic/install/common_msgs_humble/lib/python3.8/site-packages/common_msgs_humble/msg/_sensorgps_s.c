// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from common_msgs_humble:msg/Sensorgps.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "common_msgs_humble/msg/detail/sensorgps__struct.h"
#include "common_msgs_humble/msg/detail/sensorgps__functions.h"


ROSIDL_GENERATOR_C_EXPORT
bool common_msgs_humble__msg__sensorgps__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[44];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("common_msgs_humble.msg._sensorgps.Sensorgps", full_classname_dest, 43) == 0);
  }
  common_msgs_humble__msg__Sensorgps * ros_message = _ros_message;
  {  // lon
    PyObject * field = PyObject_GetAttrString(_pymsg, "lon");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->lon = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // lat
    PyObject * field = PyObject_GetAttrString(_pymsg, "lat");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->lat = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // alt
    PyObject * field = PyObject_GetAttrString(_pymsg, "alt");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->alt = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // roadtype
    PyObject * field = PyObject_GetAttrString(_pymsg, "roadtype");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->roadtype = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // lanetype
    PyObject * field = PyObject_GetAttrString(_pymsg, "lanetype");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->lanetype = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // heading
    PyObject * field = PyObject_GetAttrString(_pymsg, "heading");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->heading = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // pitch
    PyObject * field = PyObject_GetAttrString(_pymsg, "pitch");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->pitch = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // roll
    PyObject * field = PyObject_GetAttrString(_pymsg, "roll");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->roll = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // pitchrate
    PyObject * field = PyObject_GetAttrString(_pymsg, "pitchrate");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->pitchrate = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // rollrate
    PyObject * field = PyObject_GetAttrString(_pymsg, "rollrate");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->rollrate = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // yawrate
    PyObject * field = PyObject_GetAttrString(_pymsg, "yawrate");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->yawrate = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // accx
    PyObject * field = PyObject_GetAttrString(_pymsg, "accx");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->accx = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // accy
    PyObject * field = PyObject_GetAttrString(_pymsg, "accy");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->accy = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // accz
    PyObject * field = PyObject_GetAttrString(_pymsg, "accz");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->accz = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // mile
    PyObject * field = PyObject_GetAttrString(_pymsg, "mile");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->mile = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // velocity
    PyObject * field = PyObject_GetAttrString(_pymsg, "velocity");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->velocity = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // status
    PyObject * field = PyObject_GetAttrString(_pymsg, "status");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->status = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // rawstatus
    PyObject * field = PyObject_GetAttrString(_pymsg, "rawstatus");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->rawstatus = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // satenum
    PyObject * field = PyObject_GetAttrString(_pymsg, "satenum");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->satenum = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // gpstime
    PyObject * field = PyObject_GetAttrString(_pymsg, "gpstime");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->gpstime = PyLong_AsLongLong(field);
    Py_DECREF(field);
  }
  {  // isvalid
    PyObject * field = PyObject_GetAttrString(_pymsg, "isvalid");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->isvalid = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // timestamp
    PyObject * field = PyObject_GetAttrString(_pymsg, "timestamp");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->timestamp = PyLong_AsLongLong(field);
    Py_DECREF(field);
  }
  {  // speed_n
    PyObject * field = PyObject_GetAttrString(_pymsg, "speed_n");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->speed_n = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // speed_e
    PyObject * field = PyObject_GetAttrString(_pymsg, "speed_e");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->speed_e = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // speed_d
    PyObject * field = PyObject_GetAttrString(_pymsg, "speed_d");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->speed_d = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * common_msgs_humble__msg__sensorgps__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of Sensorgps */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("common_msgs_humble.msg._sensorgps");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "Sensorgps");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  common_msgs_humble__msg__Sensorgps * ros_message = (common_msgs_humble__msg__Sensorgps *)raw_ros_message;
  {  // lon
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->lon);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lon", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lat
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->lat);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lat", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // alt
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->alt);
    {
      int rc = PyObject_SetAttrString(_pymessage, "alt", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // roadtype
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->roadtype);
    {
      int rc = PyObject_SetAttrString(_pymessage, "roadtype", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lanetype
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->lanetype);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lanetype", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // heading
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->heading);
    {
      int rc = PyObject_SetAttrString(_pymessage, "heading", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // pitch
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->pitch);
    {
      int rc = PyObject_SetAttrString(_pymessage, "pitch", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // roll
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->roll);
    {
      int rc = PyObject_SetAttrString(_pymessage, "roll", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // pitchrate
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->pitchrate);
    {
      int rc = PyObject_SetAttrString(_pymessage, "pitchrate", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // rollrate
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->rollrate);
    {
      int rc = PyObject_SetAttrString(_pymessage, "rollrate", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // yawrate
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->yawrate);
    {
      int rc = PyObject_SetAttrString(_pymessage, "yawrate", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // accx
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->accx);
    {
      int rc = PyObject_SetAttrString(_pymessage, "accx", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // accy
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->accy);
    {
      int rc = PyObject_SetAttrString(_pymessage, "accy", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // accz
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->accz);
    {
      int rc = PyObject_SetAttrString(_pymessage, "accz", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // mile
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->mile);
    {
      int rc = PyObject_SetAttrString(_pymessage, "mile", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // velocity
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->velocity);
    {
      int rc = PyObject_SetAttrString(_pymessage, "velocity", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // status
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->status);
    {
      int rc = PyObject_SetAttrString(_pymessage, "status", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // rawstatus
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->rawstatus);
    {
      int rc = PyObject_SetAttrString(_pymessage, "rawstatus", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // satenum
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->satenum);
    {
      int rc = PyObject_SetAttrString(_pymessage, "satenum", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // gpstime
    PyObject * field = NULL;
    field = PyLong_FromLongLong(ros_message->gpstime);
    {
      int rc = PyObject_SetAttrString(_pymessage, "gpstime", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // isvalid
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->isvalid);
    {
      int rc = PyObject_SetAttrString(_pymessage, "isvalid", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // timestamp
    PyObject * field = NULL;
    field = PyLong_FromLongLong(ros_message->timestamp);
    {
      int rc = PyObject_SetAttrString(_pymessage, "timestamp", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // speed_n
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->speed_n);
    {
      int rc = PyObject_SetAttrString(_pymessage, "speed_n", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // speed_e
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->speed_e);
    {
      int rc = PyObject_SetAttrString(_pymessage, "speed_e", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // speed_d
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->speed_d);
    {
      int rc = PyObject_SetAttrString(_pymessage, "speed_d", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
