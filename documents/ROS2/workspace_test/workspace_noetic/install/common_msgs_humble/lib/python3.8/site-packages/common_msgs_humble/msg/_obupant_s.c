// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from common_msgs_humble:msg/Obupant.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "common_msgs_humble/msg/detail/obupant__struct.h"
#include "common_msgs_humble/msg/detail/obupant__functions.h"

#include "rosidl_runtime_c/string.h"
#include "rosidl_runtime_c/string_functions.h"

#include "rosidl_runtime_c/primitives_sequence.h"
#include "rosidl_runtime_c/primitives_sequence_functions.h"

// Nested array functions includes
#include "common_msgs_humble/msg/detail/oburoadlist__functions.h"
// end nested array functions include
bool common_msgs_humble__msg__oburoadlist__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * common_msgs_humble__msg__oburoadlist__convert_to_py(void * raw_ros_message);

ROSIDL_GENERATOR_C_EXPORT
bool common_msgs_humble__msg__obupant__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[40];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("common_msgs_humble.msg._obupant.Obupant", full_classname_dest, 39) == 0);
  }
  common_msgs_humble__msg__Obupant * ros_message = _ros_message;
  {  // ptc_type
    PyObject * field = PyObject_GetAttrString(_pymsg, "ptc_type");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->ptc_type = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // ptc_id
    PyObject * field = PyObject_GetAttrString(_pymsg, "ptc_id");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->ptc_id = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // source
    PyObject * field = PyObject_GetAttrString(_pymsg, "source");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->source = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // source_id
    PyObject * field = PyObject_GetAttrString(_pymsg, "source_id");
    if (!field) {
      return false;
    }
    assert(PyUnicode_Check(field));
    PyObject * encoded_field = PyUnicode_AsUTF8String(field);
    if (!encoded_field) {
      Py_DECREF(field);
      return false;
    }
    rosidl_runtime_c__String__assign(&ros_message->source_id, PyBytes_AS_STRING(encoded_field));
    Py_DECREF(encoded_field);
    Py_DECREF(field);
  }
  {  // sec_mark
    PyObject * field = PyObject_GetAttrString(_pymsg, "sec_mark");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->sec_mark = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // pos_lon
    PyObject * field = PyObject_GetAttrString(_pymsg, "pos_lon");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->pos_lon = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // pos_lat
    PyObject * field = PyObject_GetAttrString(_pymsg, "pos_lat");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->pos_lat = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // pos_latitude
    PyObject * field = PyObject_GetAttrString(_pymsg, "pos_latitude");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->pos_latitude = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // speed
    PyObject * field = PyObject_GetAttrString(_pymsg, "speed");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->speed = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // heading
    PyObject * field = PyObject_GetAttrString(_pymsg, "heading");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->heading = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // accel
    PyObject * field = PyObject_GetAttrString(_pymsg, "accel");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->accel = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // accel_angle
    PyObject * field = PyObject_GetAttrString(_pymsg, "accel_angle");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->accel_angle = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // acc4way_lon
    PyObject * field = PyObject_GetAttrString(_pymsg, "acc4way_lon");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->acc4way_lon = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // acc4way_lat
    PyObject * field = PyObject_GetAttrString(_pymsg, "acc4way_lat");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->acc4way_lat = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // acc4way_vert
    PyObject * field = PyObject_GetAttrString(_pymsg, "acc4way_vert");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->acc4way_vert = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // acc4way_yaw
    PyObject * field = PyObject_GetAttrString(_pymsg, "acc4way_yaw");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->acc4way_yaw = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // width
    PyObject * field = PyObject_GetAttrString(_pymsg, "width");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->width = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // length
    PyObject * field = PyObject_GetAttrString(_pymsg, "length");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->length = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // height
    PyObject * field = PyObject_GetAttrString(_pymsg, "height");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->height = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // lon
    PyObject * field = PyObject_GetAttrString(_pymsg, "lon");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->lon = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // lat
    PyObject * field = PyObject_GetAttrString(_pymsg, "lat");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->lat = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // planlist_num
    PyObject * field = PyObject_GetAttrString(_pymsg, "planlist_num");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->planlist_num = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // roadlist
    PyObject * field = PyObject_GetAttrString(_pymsg, "roadlist");
    if (!field) {
      return false;
    }
    PyObject * seq_field = PySequence_Fast(field, "expected a sequence in 'roadlist'");
    if (!seq_field) {
      Py_DECREF(field);
      return false;
    }
    Py_ssize_t size = PySequence_Size(field);
    if (-1 == size) {
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    if (!common_msgs_humble__msg__Oburoadlist__Sequence__init(&(ros_message->roadlist), size)) {
      PyErr_SetString(PyExc_RuntimeError, "unable to create common_msgs_humble__msg__Oburoadlist__Sequence ros_message");
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    common_msgs_humble__msg__Oburoadlist * dest = ros_message->roadlist.data;
    for (Py_ssize_t i = 0; i < size; ++i) {
      if (!common_msgs_humble__msg__oburoadlist__convert_from_py(PySequence_Fast_GET_ITEM(seq_field, i), &dest[i])) {
        Py_DECREF(seq_field);
        Py_DECREF(field);
        return false;
      }
    }
    Py_DECREF(seq_field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * common_msgs_humble__msg__obupant__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of Obupant */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("common_msgs_humble.msg._obupant");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "Obupant");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  common_msgs_humble__msg__Obupant * ros_message = (common_msgs_humble__msg__Obupant *)raw_ros_message;
  {  // ptc_type
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->ptc_type);
    {
      int rc = PyObject_SetAttrString(_pymessage, "ptc_type", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // ptc_id
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->ptc_id);
    {
      int rc = PyObject_SetAttrString(_pymessage, "ptc_id", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // source
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->source);
    {
      int rc = PyObject_SetAttrString(_pymessage, "source", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // source_id
    PyObject * field = NULL;
    field = PyUnicode_DecodeUTF8(
      ros_message->source_id.data,
      strlen(ros_message->source_id.data),
      "replace");
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "source_id", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // sec_mark
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->sec_mark);
    {
      int rc = PyObject_SetAttrString(_pymessage, "sec_mark", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // pos_lon
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->pos_lon);
    {
      int rc = PyObject_SetAttrString(_pymessage, "pos_lon", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // pos_lat
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->pos_lat);
    {
      int rc = PyObject_SetAttrString(_pymessage, "pos_lat", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // pos_latitude
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->pos_latitude);
    {
      int rc = PyObject_SetAttrString(_pymessage, "pos_latitude", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // speed
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->speed);
    {
      int rc = PyObject_SetAttrString(_pymessage, "speed", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // heading
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->heading);
    {
      int rc = PyObject_SetAttrString(_pymessage, "heading", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // accel
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->accel);
    {
      int rc = PyObject_SetAttrString(_pymessage, "accel", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // accel_angle
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->accel_angle);
    {
      int rc = PyObject_SetAttrString(_pymessage, "accel_angle", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // acc4way_lon
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->acc4way_lon);
    {
      int rc = PyObject_SetAttrString(_pymessage, "acc4way_lon", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // acc4way_lat
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->acc4way_lat);
    {
      int rc = PyObject_SetAttrString(_pymessage, "acc4way_lat", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // acc4way_vert
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->acc4way_vert);
    {
      int rc = PyObject_SetAttrString(_pymessage, "acc4way_vert", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // acc4way_yaw
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->acc4way_yaw);
    {
      int rc = PyObject_SetAttrString(_pymessage, "acc4way_yaw", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // width
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->width);
    {
      int rc = PyObject_SetAttrString(_pymessage, "width", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // length
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->length);
    {
      int rc = PyObject_SetAttrString(_pymessage, "length", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // height
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->height);
    {
      int rc = PyObject_SetAttrString(_pymessage, "height", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lon
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->lon);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lon", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lat
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->lat);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lat", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // planlist_num
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->planlist_num);
    {
      int rc = PyObject_SetAttrString(_pymessage, "planlist_num", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // roadlist
    PyObject * field = NULL;
    size_t size = ros_message->roadlist.size;
    field = PyList_New(size);
    if (!field) {
      return NULL;
    }
    common_msgs_humble__msg__Oburoadlist * item;
    for (size_t i = 0; i < size; ++i) {
      item = &(ros_message->roadlist.data[i]);
      PyObject * pyitem = common_msgs_humble__msg__oburoadlist__convert_to_py(item);
      if (!pyitem) {
        Py_DECREF(field);
        return NULL;
      }
      int rc = PyList_SetItem(field, i, pyitem);
      (void)rc;
      assert(rc == 0);
    }
    assert(PySequence_Check(field));
    {
      int rc = PyObject_SetAttrString(_pymessage, "roadlist", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
