// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from common_msgs_humble:msg/Actuator.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "common_msgs_humble/msg/detail/actuator__struct.h"
#include "common_msgs_humble/msg/detail/actuator__functions.h"


ROSIDL_GENERATOR_C_EXPORT
bool common_msgs_humble__msg__actuator__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[42];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("common_msgs_humble.msg._actuator.Actuator", full_classname_dest, 41) == 0);
  }
  common_msgs_humble__msg__Actuator * ros_message = _ros_message;
  {  // epsmethod
    PyObject * field = PyObject_GetAttrString(_pymsg, "epsmethod");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->epsmethod = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // epsangle
    PyObject * field = PyObject_GetAttrString(_pymsg, "epsangle");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->epsangle = (int16_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // espmethod
    PyObject * field = PyObject_GetAttrString(_pymsg, "espmethod");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->espmethod = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // escbrakepress
    PyObject * field = PyObject_GetAttrString(_pymsg, "escbrakepress");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->escbrakepress = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // gaspedal
    PyObject * field = PyObject_GetAttrString(_pymsg, "gaspedal");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->gaspedal = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // sysstatus
    PyObject * field = PyObject_GetAttrString(_pymsg, "sysstatus");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->sysstatus = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // speed
    PyObject * field = PyObject_GetAttrString(_pymsg, "speed");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->speed = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // lights
    PyObject * field = PyObject_GetAttrString(_pymsg, "lights");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->lights = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // turnlight
    PyObject * field = PyObject_GetAttrString(_pymsg, "turnlight");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->turnlight = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // gear
    PyObject * field = PyObject_GetAttrString(_pymsg, "gear");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->gear = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // epb
    PyObject * field = PyObject_GetAttrString(_pymsg, "epb");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->epb = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // door
    PyObject * field = PyObject_GetAttrString(_pymsg, "door");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->door = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // isvalid
    PyObject * field = PyObject_GetAttrString(_pymsg, "isvalid");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->isvalid = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // timestamp
    PyObject * field = PyObject_GetAttrString(_pymsg, "timestamp");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->timestamp = PyLong_AsLongLong(field);
    Py_DECREF(field);
  }
  {  // sendsuccess
    PyObject * field = PyObject_GetAttrString(_pymsg, "sendsuccess");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->sendsuccess = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // brakepedal
    PyObject * field = PyObject_GetAttrString(_pymsg, "brakepedal");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->brakepedal = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // warning
    PyObject * field = PyObject_GetAttrString(_pymsg, "warning");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->warning = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // error
    PyObject * field = PyObject_GetAttrString(_pymsg, "error");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->error = (int16_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // battery
    PyObject * field = PyObject_GetAttrString(_pymsg, "battery");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->battery = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // controlover
    PyObject * field = PyObject_GetAttrString(_pymsg, "controlover");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->controlover = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // steerspeed
    PyObject * field = PyObject_GetAttrString(_pymsg, "steerspeed");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->steerspeed = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // accelpos
    PyObject * field = PyObject_GetAttrString(_pymsg, "accelpos");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->accelpos = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // breakflag
    PyObject * field = PyObject_GetAttrString(_pymsg, "breakflag");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->breakflag = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // breakpos
    PyObject * field = PyObject_GetAttrString(_pymsg, "breakpos");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->breakpos = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // yaw
    PyObject * field = PyObject_GetAttrString(_pymsg, "yaw");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->yaw = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // mil
    PyObject * field = PyObject_GetAttrString(_pymsg, "mil");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->mil = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // soc
    PyObject * field = PyObject_GetAttrString(_pymsg, "soc");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->soc = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // batvol
    PyObject * field = PyObject_GetAttrString(_pymsg, "batvol");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->batvol = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // acc
    PyObject * field = PyObject_GetAttrString(_pymsg, "acc");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->acc = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // oilperhour
    PyObject * field = PyObject_GetAttrString(_pymsg, "oilperhour");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->oilperhour = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // oilhundredkmconsume
    PyObject * field = PyObject_GetAttrString(_pymsg, "oilhundredkmconsume");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->oilhundredkmconsume = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // oilconsume
    PyObject * field = PyObject_GetAttrString(_pymsg, "oilconsume");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->oilconsume = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // autoctrlsig
    PyObject * field = PyObject_GetAttrString(_pymsg, "autoctrlsig");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->autoctrlsig = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // totalvoltage
    PyObject * field = PyObject_GetAttrString(_pymsg, "totalvoltage");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->totalvoltage = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // totalcurrent
    PyObject * field = PyObject_GetAttrString(_pymsg, "totalcurrent");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->totalcurrent = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // motorspeed
    PyObject * field = PyObject_GetAttrString(_pymsg, "motorspeed");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->motorspeed = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // motortorque
    PyObject * field = PyObject_GetAttrString(_pymsg, "motortorque");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->motortorque = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // wirecontrolstatus
    PyObject * field = PyObject_GetAttrString(_pymsg, "wirecontrolstatus");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->wirecontrolstatus = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // blinkerstatus
    PyObject * field = PyObject_GetAttrString(_pymsg, "blinkerstatus");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->blinkerstatus = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // accx
    PyObject * field = PyObject_GetAttrString(_pymsg, "accx");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->accx = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // gaspedalcar
    PyObject * field = PyObject_GetAttrString(_pymsg, "gaspedalcar");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->gaspedalcar = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * common_msgs_humble__msg__actuator__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of Actuator */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("common_msgs_humble.msg._actuator");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "Actuator");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  common_msgs_humble__msg__Actuator * ros_message = (common_msgs_humble__msg__Actuator *)raw_ros_message;
  {  // epsmethod
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->epsmethod);
    {
      int rc = PyObject_SetAttrString(_pymessage, "epsmethod", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // epsangle
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->epsangle);
    {
      int rc = PyObject_SetAttrString(_pymessage, "epsangle", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // espmethod
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->espmethod);
    {
      int rc = PyObject_SetAttrString(_pymessage, "espmethod", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // escbrakepress
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->escbrakepress);
    {
      int rc = PyObject_SetAttrString(_pymessage, "escbrakepress", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // gaspedal
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->gaspedal);
    {
      int rc = PyObject_SetAttrString(_pymessage, "gaspedal", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // sysstatus
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->sysstatus);
    {
      int rc = PyObject_SetAttrString(_pymessage, "sysstatus", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // speed
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->speed);
    {
      int rc = PyObject_SetAttrString(_pymessage, "speed", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lights
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->lights);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lights", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // turnlight
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->turnlight);
    {
      int rc = PyObject_SetAttrString(_pymessage, "turnlight", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // gear
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->gear);
    {
      int rc = PyObject_SetAttrString(_pymessage, "gear", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // epb
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->epb);
    {
      int rc = PyObject_SetAttrString(_pymessage, "epb", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // door
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->door);
    {
      int rc = PyObject_SetAttrString(_pymessage, "door", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // isvalid
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->isvalid);
    {
      int rc = PyObject_SetAttrString(_pymessage, "isvalid", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // timestamp
    PyObject * field = NULL;
    field = PyLong_FromLongLong(ros_message->timestamp);
    {
      int rc = PyObject_SetAttrString(_pymessage, "timestamp", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // sendsuccess
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->sendsuccess);
    {
      int rc = PyObject_SetAttrString(_pymessage, "sendsuccess", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // brakepedal
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->brakepedal);
    {
      int rc = PyObject_SetAttrString(_pymessage, "brakepedal", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // warning
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->warning);
    {
      int rc = PyObject_SetAttrString(_pymessage, "warning", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // error
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->error);
    {
      int rc = PyObject_SetAttrString(_pymessage, "error", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // battery
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->battery);
    {
      int rc = PyObject_SetAttrString(_pymessage, "battery", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // controlover
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->controlover);
    {
      int rc = PyObject_SetAttrString(_pymessage, "controlover", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // steerspeed
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->steerspeed);
    {
      int rc = PyObject_SetAttrString(_pymessage, "steerspeed", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // accelpos
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->accelpos);
    {
      int rc = PyObject_SetAttrString(_pymessage, "accelpos", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // breakflag
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->breakflag);
    {
      int rc = PyObject_SetAttrString(_pymessage, "breakflag", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // breakpos
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->breakpos);
    {
      int rc = PyObject_SetAttrString(_pymessage, "breakpos", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // yaw
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->yaw);
    {
      int rc = PyObject_SetAttrString(_pymessage, "yaw", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // mil
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->mil);
    {
      int rc = PyObject_SetAttrString(_pymessage, "mil", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // soc
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->soc);
    {
      int rc = PyObject_SetAttrString(_pymessage, "soc", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // batvol
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->batvol);
    {
      int rc = PyObject_SetAttrString(_pymessage, "batvol", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // acc
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->acc);
    {
      int rc = PyObject_SetAttrString(_pymessage, "acc", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // oilperhour
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->oilperhour);
    {
      int rc = PyObject_SetAttrString(_pymessage, "oilperhour", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // oilhundredkmconsume
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->oilhundredkmconsume);
    {
      int rc = PyObject_SetAttrString(_pymessage, "oilhundredkmconsume", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // oilconsume
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->oilconsume);
    {
      int rc = PyObject_SetAttrString(_pymessage, "oilconsume", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // autoctrlsig
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->autoctrlsig);
    {
      int rc = PyObject_SetAttrString(_pymessage, "autoctrlsig", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // totalvoltage
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->totalvoltage);
    {
      int rc = PyObject_SetAttrString(_pymessage, "totalvoltage", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // totalcurrent
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->totalcurrent);
    {
      int rc = PyObject_SetAttrString(_pymessage, "totalcurrent", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // motorspeed
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->motorspeed);
    {
      int rc = PyObject_SetAttrString(_pymessage, "motorspeed", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // motortorque
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->motortorque);
    {
      int rc = PyObject_SetAttrString(_pymessage, "motortorque", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // wirecontrolstatus
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->wirecontrolstatus);
    {
      int rc = PyObject_SetAttrString(_pymessage, "wirecontrolstatus", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // blinkerstatus
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->blinkerstatus);
    {
      int rc = PyObject_SetAttrString(_pymessage, "blinkerstatus", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // accx
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->accx);
    {
      int rc = PyObject_SetAttrString(_pymessage, "accx", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // gaspedalcar
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->gaspedalcar);
    {
      int rc = PyObject_SetAttrString(_pymessage, "gaspedalcar", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
