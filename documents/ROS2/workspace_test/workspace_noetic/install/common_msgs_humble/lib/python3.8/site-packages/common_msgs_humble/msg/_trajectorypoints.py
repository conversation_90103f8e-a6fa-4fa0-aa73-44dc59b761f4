# generated from rosidl_generator_py/resource/_idl.py.em
# with input from common_msgs_humble:msg/Trajectorypoints.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_Trajectorypoints(type):
    """Metaclass of message 'Trajectorypoints'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('common_msgs_humble')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'common_msgs_humble.msg.Trajectorypoints')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__trajectorypoints
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__trajectorypoints
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__trajectorypoints
            cls._TYPE_SUPPORT = module.type_support_msg__msg__trajectorypoints
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__trajectorypoints

            from common_msgs_humble.msg import Roadpoint
            if Roadpoint.__class__._TYPE_SUPPORT is None:
                Roadpoint.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class Trajectorypoints(metaclass=Metaclass_Trajectorypoints):
    """Message class 'Trajectorypoints'."""

    __slots__ = [
        '_points',
        '_source',
        '_isvalid',
        '_backpark',
        '_gpstime',
        '_timestamp',
    ]

    _fields_and_field_types = {
        'points': 'sequence<common_msgs_humble/Roadpoint>',
        'source': 'uint8',
        'isvalid': 'uint8',
        'backpark': 'uint8',
        'gpstime': 'int64',
        'timestamp': 'int64',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['common_msgs_humble', 'msg'], 'Roadpoint')),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('int64'),  # noqa: E501
        rosidl_parser.definition.BasicType('int64'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.points = kwargs.get('points', [])
        self.source = kwargs.get('source', int())
        self.isvalid = kwargs.get('isvalid', int())
        self.backpark = kwargs.get('backpark', int())
        self.gpstime = kwargs.get('gpstime', int())
        self.timestamp = kwargs.get('timestamp', int())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.points != other.points:
            return False
        if self.source != other.source:
            return False
        if self.isvalid != other.isvalid:
            return False
        if self.backpark != other.backpark:
            return False
        if self.gpstime != other.gpstime:
            return False
        if self.timestamp != other.timestamp:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def points(self):
        """Message field 'points'."""
        return self._points

    @points.setter
    def points(self, value):
        if __debug__:
            from common_msgs_humble.msg import Roadpoint
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, Roadpoint) for v in value) and
                 True), \
                "The 'points' field must be a set or sequence and each value of type 'Roadpoint'"
        self._points = value

    @builtins.property
    def source(self):
        """Message field 'source'."""
        return self._source

    @source.setter
    def source(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'source' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'source' field must be an unsigned integer in [0, 255]"
        self._source = value

    @builtins.property
    def isvalid(self):
        """Message field 'isvalid'."""
        return self._isvalid

    @isvalid.setter
    def isvalid(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'isvalid' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'isvalid' field must be an unsigned integer in [0, 255]"
        self._isvalid = value

    @builtins.property
    def backpark(self):
        """Message field 'backpark'."""
        return self._backpark

    @backpark.setter
    def backpark(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'backpark' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'backpark' field must be an unsigned integer in [0, 255]"
        self._backpark = value

    @builtins.property
    def gpstime(self):
        """Message field 'gpstime'."""
        return self._gpstime

    @gpstime.setter
    def gpstime(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'gpstime' field must be of type 'int'"
            assert value >= -9223372036854775808 and value < 9223372036854775808, \
                "The 'gpstime' field must be an integer in [-9223372036854775808, 9223372036854775807]"
        self._gpstime = value

    @builtins.property
    def timestamp(self):
        """Message field 'timestamp'."""
        return self._timestamp

    @timestamp.setter
    def timestamp(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'timestamp' field must be of type 'int'"
            assert value >= -9223372036854775808 and value < 9223372036854775808, \
                "The 'timestamp' field must be an integer in [-9223372036854775808, 9223372036854775807]"
        self._timestamp = value
