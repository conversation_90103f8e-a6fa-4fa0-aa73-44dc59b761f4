// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from common_msgs_humble:msg/Roadpoint.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "common_msgs_humble/msg/detail/roadpoint__struct.h"
#include "common_msgs_humble/msg/detail/roadpoint__functions.h"


ROSIDL_GENERATOR_C_EXPORT
bool common_msgs_humble__msg__roadpoint__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[44];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("common_msgs_humble.msg._roadpoint.Roadpoint", full_classname_dest, 43) == 0);
  }
  common_msgs_humble__msg__Roadpoint * ros_message = _ros_message;
  {  // x
    PyObject * field = PyObject_GetAttrString(_pymsg, "x");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->x = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // y
    PyObject * field = PyObject_GetAttrString(_pymsg, "y");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->y = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // gx
    PyObject * field = PyObject_GetAttrString(_pymsg, "gx");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->gx = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // gy
    PyObject * field = PyObject_GetAttrString(_pymsg, "gy");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->gy = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // roadtype
    PyObject * field = PyObject_GetAttrString(_pymsg, "roadtype");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->roadtype = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // speed
    PyObject * field = PyObject_GetAttrString(_pymsg, "speed");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->speed = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // a
    PyObject * field = PyObject_GetAttrString(_pymsg, "a");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->a = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // jerk
    PyObject * field = PyObject_GetAttrString(_pymsg, "jerk");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->jerk = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // lanetype
    PyObject * field = PyObject_GetAttrString(_pymsg, "lanetype");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->lanetype = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // turnlight
    PyObject * field = PyObject_GetAttrString(_pymsg, "turnlight");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->turnlight = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // mergelanetype
    PyObject * field = PyObject_GetAttrString(_pymsg, "mergelanetype");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->mergelanetype = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // sensorlanetype
    PyObject * field = PyObject_GetAttrString(_pymsg, "sensorlanetype");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->sensorlanetype = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // heading
    PyObject * field = PyObject_GetAttrString(_pymsg, "heading");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->heading = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // curvature
    PyObject * field = PyObject_GetAttrString(_pymsg, "curvature");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->curvature = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // dkappa
    PyObject * field = PyObject_GetAttrString(_pymsg, "dkappa");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->dkappa = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // ddkappa
    PyObject * field = PyObject_GetAttrString(_pymsg, "ddkappa");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->ddkappa = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // leftsearchdis
    PyObject * field = PyObject_GetAttrString(_pymsg, "leftsearchdis");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->leftsearchdis = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // rightsearchdis
    PyObject * field = PyObject_GetAttrString(_pymsg, "rightsearchdis");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->rightsearchdis = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // s
    PyObject * field = PyObject_GetAttrString(_pymsg, "s");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->s = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // sideroadwidth
    PyObject * field = PyObject_GetAttrString(_pymsg, "sideroadwidth");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->sideroadwidth = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // lanewidth
    PyObject * field = PyObject_GetAttrString(_pymsg, "lanewidth");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->lanewidth = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // leftlanewidth
    PyObject * field = PyObject_GetAttrString(_pymsg, "leftlanewidth");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->leftlanewidth = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // rightlanewidth
    PyObject * field = PyObject_GetAttrString(_pymsg, "rightlanewidth");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->rightlanewidth = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // relativetime
    PyObject * field = PyObject_GetAttrString(_pymsg, "relativetime");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->relativetime = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // laneswitch
    PyObject * field = PyObject_GetAttrString(_pymsg, "laneswitch");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->laneswitch = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // laneborrow
    PyObject * field = PyObject_GetAttrString(_pymsg, "laneborrow");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->laneborrow = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // lanenum
    PyObject * field = PyObject_GetAttrString(_pymsg, "lanenum");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->lanenum = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // lanesite
    PyObject * field = PyObject_GetAttrString(_pymsg, "lanesite");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->lanesite = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * common_msgs_humble__msg__roadpoint__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of Roadpoint */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("common_msgs_humble.msg._roadpoint");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "Roadpoint");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  common_msgs_humble__msg__Roadpoint * ros_message = (common_msgs_humble__msg__Roadpoint *)raw_ros_message;
  {  // x
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->x);
    {
      int rc = PyObject_SetAttrString(_pymessage, "x", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // y
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->y);
    {
      int rc = PyObject_SetAttrString(_pymessage, "y", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // gx
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->gx);
    {
      int rc = PyObject_SetAttrString(_pymessage, "gx", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // gy
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->gy);
    {
      int rc = PyObject_SetAttrString(_pymessage, "gy", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // roadtype
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->roadtype);
    {
      int rc = PyObject_SetAttrString(_pymessage, "roadtype", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // speed
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->speed);
    {
      int rc = PyObject_SetAttrString(_pymessage, "speed", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // a
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->a);
    {
      int rc = PyObject_SetAttrString(_pymessage, "a", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // jerk
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->jerk);
    {
      int rc = PyObject_SetAttrString(_pymessage, "jerk", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lanetype
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->lanetype);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lanetype", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // turnlight
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->turnlight);
    {
      int rc = PyObject_SetAttrString(_pymessage, "turnlight", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // mergelanetype
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->mergelanetype);
    {
      int rc = PyObject_SetAttrString(_pymessage, "mergelanetype", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // sensorlanetype
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->sensorlanetype);
    {
      int rc = PyObject_SetAttrString(_pymessage, "sensorlanetype", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // heading
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->heading);
    {
      int rc = PyObject_SetAttrString(_pymessage, "heading", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // curvature
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->curvature);
    {
      int rc = PyObject_SetAttrString(_pymessage, "curvature", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // dkappa
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->dkappa);
    {
      int rc = PyObject_SetAttrString(_pymessage, "dkappa", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // ddkappa
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->ddkappa);
    {
      int rc = PyObject_SetAttrString(_pymessage, "ddkappa", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // leftsearchdis
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->leftsearchdis);
    {
      int rc = PyObject_SetAttrString(_pymessage, "leftsearchdis", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // rightsearchdis
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->rightsearchdis);
    {
      int rc = PyObject_SetAttrString(_pymessage, "rightsearchdis", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // s
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->s);
    {
      int rc = PyObject_SetAttrString(_pymessage, "s", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // sideroadwidth
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->sideroadwidth);
    {
      int rc = PyObject_SetAttrString(_pymessage, "sideroadwidth", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lanewidth
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->lanewidth);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lanewidth", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // leftlanewidth
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->leftlanewidth);
    {
      int rc = PyObject_SetAttrString(_pymessage, "leftlanewidth", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // rightlanewidth
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->rightlanewidth);
    {
      int rc = PyObject_SetAttrString(_pymessage, "rightlanewidth", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // relativetime
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->relativetime);
    {
      int rc = PyObject_SetAttrString(_pymessage, "relativetime", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // laneswitch
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->laneswitch);
    {
      int rc = PyObject_SetAttrString(_pymessage, "laneswitch", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // laneborrow
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->laneborrow);
    {
      int rc = PyObject_SetAttrString(_pymessage, "laneborrow", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lanenum
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->lanenum);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lanenum", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lanesite
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->lanesite);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lanesite", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
