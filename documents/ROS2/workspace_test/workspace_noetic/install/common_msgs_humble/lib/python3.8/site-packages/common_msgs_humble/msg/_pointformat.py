# generated from rosidl_generator_py/resource/_idl.py.em
# with input from common_msgs_humble:msg/Pointformat.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_Pointformat(type):
    """Metaclass of message 'Pointformat'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('common_msgs_humble')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'common_msgs_humble.msg.Pointformat')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__pointformat
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__pointformat
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__pointformat
            cls._TYPE_SUPPORT = module.type_support_msg__msg__pointformat
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__pointformat

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class Pointformat(metaclass=Metaclass_Pointformat):
    """Message class 'Pointformat'."""

    __slots__ = [
        '_lon',
        '_lat',
        '_heading',
        '_index',
        '_backup1',
        '_backup2',
        '_backup3',
        '_backup4',
        '_backup5',
        '_backup6',
        '_backup7',
        '_backup8',
        '_backup9',
        '_path',
    ]

    _fields_and_field_types = {
        'lon': 'double',
        'lat': 'double',
        'heading': 'float',
        'index': 'uint8',
        'backup1': 'uint8',
        'backup2': 'uint8',
        'backup3': 'uint8',
        'backup4': 'float',
        'backup5': 'float',
        'backup6': 'float',
        'backup7': 'double',
        'backup8': 'double',
        'backup9': 'double',
        'path': 'string',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.UnboundedString(),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.lon = kwargs.get('lon', float())
        self.lat = kwargs.get('lat', float())
        self.heading = kwargs.get('heading', float())
        self.index = kwargs.get('index', int())
        self.backup1 = kwargs.get('backup1', int())
        self.backup2 = kwargs.get('backup2', int())
        self.backup3 = kwargs.get('backup3', int())
        self.backup4 = kwargs.get('backup4', float())
        self.backup5 = kwargs.get('backup5', float())
        self.backup6 = kwargs.get('backup6', float())
        self.backup7 = kwargs.get('backup7', float())
        self.backup8 = kwargs.get('backup8', float())
        self.backup9 = kwargs.get('backup9', float())
        self.path = kwargs.get('path', str())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.lon != other.lon:
            return False
        if self.lat != other.lat:
            return False
        if self.heading != other.heading:
            return False
        if self.index != other.index:
            return False
        if self.backup1 != other.backup1:
            return False
        if self.backup2 != other.backup2:
            return False
        if self.backup3 != other.backup3:
            return False
        if self.backup4 != other.backup4:
            return False
        if self.backup5 != other.backup5:
            return False
        if self.backup6 != other.backup6:
            return False
        if self.backup7 != other.backup7:
            return False
        if self.backup8 != other.backup8:
            return False
        if self.backup9 != other.backup9:
            return False
        if self.path != other.path:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def lon(self):
        """Message field 'lon'."""
        return self._lon

    @lon.setter
    def lon(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'lon' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'lon' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._lon = value

    @builtins.property
    def lat(self):
        """Message field 'lat'."""
        return self._lat

    @lat.setter
    def lat(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'lat' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'lat' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._lat = value

    @builtins.property
    def heading(self):
        """Message field 'heading'."""
        return self._heading

    @heading.setter
    def heading(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'heading' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'heading' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._heading = value

    @builtins.property
    def index(self):
        """Message field 'index'."""
        return self._index

    @index.setter
    def index(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'index' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'index' field must be an unsigned integer in [0, 255]"
        self._index = value

    @builtins.property
    def backup1(self):
        """Message field 'backup1'."""
        return self._backup1

    @backup1.setter
    def backup1(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'backup1' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'backup1' field must be an unsigned integer in [0, 255]"
        self._backup1 = value

    @builtins.property
    def backup2(self):
        """Message field 'backup2'."""
        return self._backup2

    @backup2.setter
    def backup2(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'backup2' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'backup2' field must be an unsigned integer in [0, 255]"
        self._backup2 = value

    @builtins.property
    def backup3(self):
        """Message field 'backup3'."""
        return self._backup3

    @backup3.setter
    def backup3(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'backup3' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'backup3' field must be an unsigned integer in [0, 255]"
        self._backup3 = value

    @builtins.property
    def backup4(self):
        """Message field 'backup4'."""
        return self._backup4

    @backup4.setter
    def backup4(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'backup4' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'backup4' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._backup4 = value

    @builtins.property
    def backup5(self):
        """Message field 'backup5'."""
        return self._backup5

    @backup5.setter
    def backup5(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'backup5' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'backup5' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._backup5 = value

    @builtins.property
    def backup6(self):
        """Message field 'backup6'."""
        return self._backup6

    @backup6.setter
    def backup6(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'backup6' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'backup6' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._backup6 = value

    @builtins.property
    def backup7(self):
        """Message field 'backup7'."""
        return self._backup7

    @backup7.setter
    def backup7(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'backup7' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'backup7' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._backup7 = value

    @builtins.property
    def backup8(self):
        """Message field 'backup8'."""
        return self._backup8

    @backup8.setter
    def backup8(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'backup8' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'backup8' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._backup8 = value

    @builtins.property
    def backup9(self):
        """Message field 'backup9'."""
        return self._backup9

    @backup9.setter
    def backup9(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'backup9' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'backup9' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._backup9 = value

    @builtins.property
    def path(self):
        """Message field 'path'."""
        return self._path

    @path.setter
    def path(self, value):
        if __debug__:
            assert \
                isinstance(value, str), \
                "The 'path' field must be of type 'str'"
        self._path = value
