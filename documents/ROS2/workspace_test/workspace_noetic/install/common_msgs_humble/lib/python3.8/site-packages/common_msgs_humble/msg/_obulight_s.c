// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from common_msgs_humble:msg/Obulight.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "common_msgs_humble/msg/detail/obulight__struct.h"
#include "common_msgs_humble/msg/detail/obulight__functions.h"


ROSIDL_GENERATOR_C_EXPORT
bool common_msgs_humble__msg__obulight__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[42];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("common_msgs_humble.msg._obulight.Obulight", full_classname_dest, 41) == 0);
  }
  common_msgs_humble__msg__Obulight * ros_message = _ros_message;
  {  // phase_id
    PyObject * field = PyObject_GetAttrString(_pymsg, "phase_id");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->phase_id = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // status
    PyObject * field = PyObject_GetAttrString(_pymsg, "status");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->status = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // start_time
    PyObject * field = PyObject_GetAttrString(_pymsg, "start_time");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->start_time = PyLong_AsLongLong(field);
    Py_DECREF(field);
  }
  {  // end_time
    PyObject * field = PyObject_GetAttrString(_pymsg, "end_time");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->end_time = PyLong_AsLongLong(field);
    Py_DECREF(field);
  }
  {  // next_start_time
    PyObject * field = PyObject_GetAttrString(_pymsg, "next_start_time");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->next_start_time = PyLong_AsLongLong(field);
    Py_DECREF(field);
  }
  {  // lane_speed_lower
    PyObject * field = PyObject_GetAttrString(_pymsg, "lane_speed_lower");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->lane_speed_lower = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // lane_speed_upper
    PyObject * field = PyObject_GetAttrString(_pymsg, "lane_speed_upper");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->lane_speed_upper = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * common_msgs_humble__msg__obulight__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of Obulight */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("common_msgs_humble.msg._obulight");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "Obulight");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  common_msgs_humble__msg__Obulight * ros_message = (common_msgs_humble__msg__Obulight *)raw_ros_message;
  {  // phase_id
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->phase_id);
    {
      int rc = PyObject_SetAttrString(_pymessage, "phase_id", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // status
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->status);
    {
      int rc = PyObject_SetAttrString(_pymessage, "status", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // start_time
    PyObject * field = NULL;
    field = PyLong_FromLongLong(ros_message->start_time);
    {
      int rc = PyObject_SetAttrString(_pymessage, "start_time", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // end_time
    PyObject * field = NULL;
    field = PyLong_FromLongLong(ros_message->end_time);
    {
      int rc = PyObject_SetAttrString(_pymessage, "end_time", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // next_start_time
    PyObject * field = NULL;
    field = PyLong_FromLongLong(ros_message->next_start_time);
    {
      int rc = PyObject_SetAttrString(_pymessage, "next_start_time", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lane_speed_lower
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->lane_speed_lower);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lane_speed_lower", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lane_speed_upper
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->lane_speed_upper);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lane_speed_upper", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
