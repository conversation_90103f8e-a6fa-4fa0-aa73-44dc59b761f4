// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from common_msgs_humble:msg/Controllon.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "common_msgs_humble/msg/detail/controllon__struct.h"
#include "common_msgs_humble/msg/detail/controllon__functions.h"


ROSIDL_GENERATOR_C_EXPORT
bool common_msgs_humble__msg__controllon__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[46];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("common_msgs_humble.msg._controllon.Controllon", full_classname_dest, 45) == 0);
  }
  common_msgs_humble__msg__Controllon * ros_message = _ros_message;
  {  // espmethod
    PyObject * field = PyObject_GetAttrString(_pymsg, "espmethod");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->espmethod = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // gpsdis
    PyObject * field = PyObject_GetAttrString(_pymsg, "gpsdis");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->gpsdis = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // epbmethod
    PyObject * field = PyObject_GetAttrString(_pymsg, "epbmethod");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->epbmethod = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // epb
    PyObject * field = PyObject_GetAttrString(_pymsg, "epb");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->epb = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // geermethod
    PyObject * field = PyObject_GetAttrString(_pymsg, "geermethod");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->geermethod = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // gear
    PyObject * field = PyObject_GetAttrString(_pymsg, "gear");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->gear = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // brakemethod
    PyObject * field = PyObject_GetAttrString(_pymsg, "brakemethod");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->brakemethod = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // brakepedal
    PyObject * field = PyObject_GetAttrString(_pymsg, "brakepedal");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->brakepedal = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // gaspedal
    PyObject * field = PyObject_GetAttrString(_pymsg, "gaspedal");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->gaspedal = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // station
    PyObject * field = PyObject_GetAttrString(_pymsg, "station");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->station = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // light
    PyObject * field = PyObject_GetAttrString(_pymsg, "light");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->light = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // pcmethod
    PyObject * field = PyObject_GetAttrString(_pymsg, "pcmethod");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->pcmethod = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // objdis
    PyObject * field = PyObject_GetAttrString(_pymsg, "objdis");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->objdis = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // objrel
    PyObject * field = PyObject_GetAttrString(_pymsg, "objrel");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->objrel = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // mode
    PyObject * field = PyObject_GetAttrString(_pymsg, "mode");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->mode = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // isvalid
    PyObject * field = PyObject_GetAttrString(_pymsg, "isvalid");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->isvalid = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // timestamp
    PyObject * field = PyObject_GetAttrString(_pymsg, "timestamp");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->timestamp = PyLong_AsLongLong(field);
    Py_DECREF(field);
  }
  {  // targetspeed
    PyObject * field = PyObject_GetAttrString(_pymsg, "targetspeed");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->targetspeed = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // apadis
    PyObject * field = PyObject_GetAttrString(_pymsg, "apadis");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->apadis = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // objtype
    PyObject * field = PyObject_GetAttrString(_pymsg, "objtype");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->objtype = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * common_msgs_humble__msg__controllon__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of Controllon */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("common_msgs_humble.msg._controllon");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "Controllon");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  common_msgs_humble__msg__Controllon * ros_message = (common_msgs_humble__msg__Controllon *)raw_ros_message;
  {  // espmethod
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->espmethod);
    {
      int rc = PyObject_SetAttrString(_pymessage, "espmethod", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // gpsdis
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->gpsdis);
    {
      int rc = PyObject_SetAttrString(_pymessage, "gpsdis", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // epbmethod
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->epbmethod);
    {
      int rc = PyObject_SetAttrString(_pymessage, "epbmethod", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // epb
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->epb);
    {
      int rc = PyObject_SetAttrString(_pymessage, "epb", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // geermethod
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->geermethod);
    {
      int rc = PyObject_SetAttrString(_pymessage, "geermethod", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // gear
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->gear);
    {
      int rc = PyObject_SetAttrString(_pymessage, "gear", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // brakemethod
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->brakemethod);
    {
      int rc = PyObject_SetAttrString(_pymessage, "brakemethod", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // brakepedal
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->brakepedal);
    {
      int rc = PyObject_SetAttrString(_pymessage, "brakepedal", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // gaspedal
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->gaspedal);
    {
      int rc = PyObject_SetAttrString(_pymessage, "gaspedal", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // station
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->station);
    {
      int rc = PyObject_SetAttrString(_pymessage, "station", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // light
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->light);
    {
      int rc = PyObject_SetAttrString(_pymessage, "light", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // pcmethod
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->pcmethod);
    {
      int rc = PyObject_SetAttrString(_pymessage, "pcmethod", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // objdis
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->objdis);
    {
      int rc = PyObject_SetAttrString(_pymessage, "objdis", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // objrel
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->objrel);
    {
      int rc = PyObject_SetAttrString(_pymessage, "objrel", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // mode
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->mode);
    {
      int rc = PyObject_SetAttrString(_pymessage, "mode", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // isvalid
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->isvalid);
    {
      int rc = PyObject_SetAttrString(_pymessage, "isvalid", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // timestamp
    PyObject * field = NULL;
    field = PyLong_FromLongLong(ros_message->timestamp);
    {
      int rc = PyObject_SetAttrString(_pymessage, "timestamp", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // targetspeed
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->targetspeed);
    {
      int rc = PyObject_SetAttrString(_pymessage, "targetspeed", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // apadis
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->apadis);
    {
      int rc = PyObject_SetAttrString(_pymessage, "apadis", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // objtype
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->objtype);
    {
      int rc = PyObject_SetAttrString(_pymessage, "objtype", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
