# generated from rosidl_generator_py/resource/_idl.py.em
# with input from common_msgs_humble:msg/Obupant.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_Obupant(type):
    """Metaclass of message 'Obupant'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('common_msgs_humble')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'common_msgs_humble.msg.Obupant')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__obupant
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__obupant
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__obupant
            cls._TYPE_SUPPORT = module.type_support_msg__msg__obupant
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__obupant

            from common_msgs_humble.msg import Oburoadlist
            if Oburoadlist.__class__._TYPE_SUPPORT is None:
                Oburoadlist.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class Obupant(metaclass=Metaclass_Obupant):
    """Message class 'Obupant'."""

    __slots__ = [
        '_ptc_type',
        '_ptc_id',
        '_source',
        '_source_id',
        '_sec_mark',
        '_pos_lon',
        '_pos_lat',
        '_pos_latitude',
        '_speed',
        '_heading',
        '_accel',
        '_accel_angle',
        '_acc4way_lon',
        '_acc4way_lat',
        '_acc4way_vert',
        '_acc4way_yaw',
        '_width',
        '_length',
        '_height',
        '_lon',
        '_lat',
        '_planlist_num',
        '_roadlist',
    ]

    _fields_and_field_types = {
        'ptc_type': 'int32',
        'ptc_id': 'int32',
        'source': 'int32',
        'source_id': 'string',
        'sec_mark': 'int32',
        'pos_lon': 'double',
        'pos_lat': 'double',
        'pos_latitude': 'double',
        'speed': 'float',
        'heading': 'float',
        'accel': 'float',
        'accel_angle': 'float',
        'acc4way_lon': 'float',
        'acc4way_lat': 'float',
        'acc4way_vert': 'float',
        'acc4way_yaw': 'float',
        'width': 'float',
        'length': 'float',
        'height': 'float',
        'lon': 'float',
        'lat': 'float',
        'planlist_num': 'uint8',
        'roadlist': 'sequence<common_msgs_humble/Oburoadlist>',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.UnboundedString(),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.NamespacedType(['common_msgs_humble', 'msg'], 'Oburoadlist')),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.ptc_type = kwargs.get('ptc_type', int())
        self.ptc_id = kwargs.get('ptc_id', int())
        self.source = kwargs.get('source', int())
        self.source_id = kwargs.get('source_id', str())
        self.sec_mark = kwargs.get('sec_mark', int())
        self.pos_lon = kwargs.get('pos_lon', float())
        self.pos_lat = kwargs.get('pos_lat', float())
        self.pos_latitude = kwargs.get('pos_latitude', float())
        self.speed = kwargs.get('speed', float())
        self.heading = kwargs.get('heading', float())
        self.accel = kwargs.get('accel', float())
        self.accel_angle = kwargs.get('accel_angle', float())
        self.acc4way_lon = kwargs.get('acc4way_lon', float())
        self.acc4way_lat = kwargs.get('acc4way_lat', float())
        self.acc4way_vert = kwargs.get('acc4way_vert', float())
        self.acc4way_yaw = kwargs.get('acc4way_yaw', float())
        self.width = kwargs.get('width', float())
        self.length = kwargs.get('length', float())
        self.height = kwargs.get('height', float())
        self.lon = kwargs.get('lon', float())
        self.lat = kwargs.get('lat', float())
        self.planlist_num = kwargs.get('planlist_num', int())
        self.roadlist = kwargs.get('roadlist', [])

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.ptc_type != other.ptc_type:
            return False
        if self.ptc_id != other.ptc_id:
            return False
        if self.source != other.source:
            return False
        if self.source_id != other.source_id:
            return False
        if self.sec_mark != other.sec_mark:
            return False
        if self.pos_lon != other.pos_lon:
            return False
        if self.pos_lat != other.pos_lat:
            return False
        if self.pos_latitude != other.pos_latitude:
            return False
        if self.speed != other.speed:
            return False
        if self.heading != other.heading:
            return False
        if self.accel != other.accel:
            return False
        if self.accel_angle != other.accel_angle:
            return False
        if self.acc4way_lon != other.acc4way_lon:
            return False
        if self.acc4way_lat != other.acc4way_lat:
            return False
        if self.acc4way_vert != other.acc4way_vert:
            return False
        if self.acc4way_yaw != other.acc4way_yaw:
            return False
        if self.width != other.width:
            return False
        if self.length != other.length:
            return False
        if self.height != other.height:
            return False
        if self.lon != other.lon:
            return False
        if self.lat != other.lat:
            return False
        if self.planlist_num != other.planlist_num:
            return False
        if self.roadlist != other.roadlist:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def ptc_type(self):
        """Message field 'ptc_type'."""
        return self._ptc_type

    @ptc_type.setter
    def ptc_type(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'ptc_type' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'ptc_type' field must be an integer in [-2147483648, 2147483647]"
        self._ptc_type = value

    @builtins.property
    def ptc_id(self):
        """Message field 'ptc_id'."""
        return self._ptc_id

    @ptc_id.setter
    def ptc_id(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'ptc_id' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'ptc_id' field must be an integer in [-2147483648, 2147483647]"
        self._ptc_id = value

    @builtins.property
    def source(self):
        """Message field 'source'."""
        return self._source

    @source.setter
    def source(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'source' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'source' field must be an integer in [-2147483648, 2147483647]"
        self._source = value

    @builtins.property
    def source_id(self):
        """Message field 'source_id'."""
        return self._source_id

    @source_id.setter
    def source_id(self, value):
        if __debug__:
            assert \
                isinstance(value, str), \
                "The 'source_id' field must be of type 'str'"
        self._source_id = value

    @builtins.property
    def sec_mark(self):
        """Message field 'sec_mark'."""
        return self._sec_mark

    @sec_mark.setter
    def sec_mark(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'sec_mark' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'sec_mark' field must be an integer in [-2147483648, 2147483647]"
        self._sec_mark = value

    @builtins.property
    def pos_lon(self):
        """Message field 'pos_lon'."""
        return self._pos_lon

    @pos_lon.setter
    def pos_lon(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'pos_lon' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'pos_lon' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._pos_lon = value

    @builtins.property
    def pos_lat(self):
        """Message field 'pos_lat'."""
        return self._pos_lat

    @pos_lat.setter
    def pos_lat(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'pos_lat' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'pos_lat' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._pos_lat = value

    @builtins.property
    def pos_latitude(self):
        """Message field 'pos_latitude'."""
        return self._pos_latitude

    @pos_latitude.setter
    def pos_latitude(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'pos_latitude' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'pos_latitude' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._pos_latitude = value

    @builtins.property
    def speed(self):
        """Message field 'speed'."""
        return self._speed

    @speed.setter
    def speed(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'speed' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'speed' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._speed = value

    @builtins.property
    def heading(self):
        """Message field 'heading'."""
        return self._heading

    @heading.setter
    def heading(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'heading' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'heading' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._heading = value

    @builtins.property
    def accel(self):
        """Message field 'accel'."""
        return self._accel

    @accel.setter
    def accel(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'accel' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'accel' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._accel = value

    @builtins.property
    def accel_angle(self):
        """Message field 'accel_angle'."""
        return self._accel_angle

    @accel_angle.setter
    def accel_angle(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'accel_angle' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'accel_angle' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._accel_angle = value

    @builtins.property
    def acc4way_lon(self):
        """Message field 'acc4way_lon'."""
        return self._acc4way_lon

    @acc4way_lon.setter
    def acc4way_lon(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'acc4way_lon' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'acc4way_lon' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._acc4way_lon = value

    @builtins.property
    def acc4way_lat(self):
        """Message field 'acc4way_lat'."""
        return self._acc4way_lat

    @acc4way_lat.setter
    def acc4way_lat(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'acc4way_lat' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'acc4way_lat' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._acc4way_lat = value

    @builtins.property
    def acc4way_vert(self):
        """Message field 'acc4way_vert'."""
        return self._acc4way_vert

    @acc4way_vert.setter
    def acc4way_vert(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'acc4way_vert' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'acc4way_vert' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._acc4way_vert = value

    @builtins.property
    def acc4way_yaw(self):
        """Message field 'acc4way_yaw'."""
        return self._acc4way_yaw

    @acc4way_yaw.setter
    def acc4way_yaw(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'acc4way_yaw' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'acc4way_yaw' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._acc4way_yaw = value

    @builtins.property
    def width(self):
        """Message field 'width'."""
        return self._width

    @width.setter
    def width(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'width' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'width' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._width = value

    @builtins.property
    def length(self):
        """Message field 'length'."""
        return self._length

    @length.setter
    def length(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'length' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'length' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._length = value

    @builtins.property
    def height(self):
        """Message field 'height'."""
        return self._height

    @height.setter
    def height(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'height' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'height' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._height = value

    @builtins.property
    def lon(self):
        """Message field 'lon'."""
        return self._lon

    @lon.setter
    def lon(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'lon' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'lon' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._lon = value

    @builtins.property
    def lat(self):
        """Message field 'lat'."""
        return self._lat

    @lat.setter
    def lat(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'lat' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'lat' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._lat = value

    @builtins.property
    def planlist_num(self):
        """Message field 'planlist_num'."""
        return self._planlist_num

    @planlist_num.setter
    def planlist_num(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'planlist_num' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'planlist_num' field must be an unsigned integer in [0, 255]"
        self._planlist_num = value

    @builtins.property
    def roadlist(self):
        """Message field 'roadlist'."""
        return self._roadlist

    @roadlist.setter
    def roadlist(self, value):
        if __debug__:
            from common_msgs_humble.msg import Oburoadlist
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, Oburoadlist) for v in value) and
                 True), \
                "The 'roadlist' field must be a set or sequence and each value of type 'Oburoadlist'"
        self._roadlist = value
