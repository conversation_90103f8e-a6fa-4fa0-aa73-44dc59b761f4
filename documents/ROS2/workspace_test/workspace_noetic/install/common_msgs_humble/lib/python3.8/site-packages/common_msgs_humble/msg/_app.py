# generated from rosidl_generator_py/resource/_idl.py.em
# with input from common_msgs_humble:msg/App.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_App(type):
    """Metaclass of message 'App'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('common_msgs_humble')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'common_msgs_humble.msg.App')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__app
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__app
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__app
            cls._TYPE_SUPPORT = module.type_support_msg__msg__app
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__app

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class App(metaclass=Metaclass_App):
    """Message class 'App'."""

    __slots__ = [
        '_stopgo',
        '_zonename',
        '_apsnum',
        '_estop',
        '_park',
        '_timestamp',
    ]

    _fields_and_field_types = {
        'stopgo': 'uint8',
        'zonename': 'uint8',
        'apsnum': 'uint8',
        'estop': 'uint8',
        'park': 'uint8',
        'timestamp': 'int64',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('int64'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.stopgo = kwargs.get('stopgo', int())
        self.zonename = kwargs.get('zonename', int())
        self.apsnum = kwargs.get('apsnum', int())
        self.estop = kwargs.get('estop', int())
        self.park = kwargs.get('park', int())
        self.timestamp = kwargs.get('timestamp', int())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.stopgo != other.stopgo:
            return False
        if self.zonename != other.zonename:
            return False
        if self.apsnum != other.apsnum:
            return False
        if self.estop != other.estop:
            return False
        if self.park != other.park:
            return False
        if self.timestamp != other.timestamp:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def stopgo(self):
        """Message field 'stopgo'."""
        return self._stopgo

    @stopgo.setter
    def stopgo(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'stopgo' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'stopgo' field must be an unsigned integer in [0, 255]"
        self._stopgo = value

    @builtins.property
    def zonename(self):
        """Message field 'zonename'."""
        return self._zonename

    @zonename.setter
    def zonename(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'zonename' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'zonename' field must be an unsigned integer in [0, 255]"
        self._zonename = value

    @builtins.property
    def apsnum(self):
        """Message field 'apsnum'."""
        return self._apsnum

    @apsnum.setter
    def apsnum(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'apsnum' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'apsnum' field must be an unsigned integer in [0, 255]"
        self._apsnum = value

    @builtins.property
    def estop(self):
        """Message field 'estop'."""
        return self._estop

    @estop.setter
    def estop(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'estop' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'estop' field must be an unsigned integer in [0, 255]"
        self._estop = value

    @builtins.property
    def park(self):
        """Message field 'park'."""
        return self._park

    @park.setter
    def park(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'park' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'park' field must be an unsigned integer in [0, 255]"
        self._park = value

    @builtins.property
    def timestamp(self):
        """Message field 'timestamp'."""
        return self._timestamp

    @timestamp.setter
    def timestamp(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'timestamp' field must be of type 'int'"
            assert value >= -9223372036854775808 and value < 9223372036854775808, \
                "The 'timestamp' field must be an integer in [-9223372036854775808, 9223372036854775807]"
        self._timestamp = value
