// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from common_msgs_humble:msg/Ieku.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "common_msgs_humble/msg/detail/ieku__struct.h"
#include "common_msgs_humble/msg/detail/ieku__functions.h"


ROSIDL_GENERATOR_C_EXPORT
bool common_msgs_humble__msg__ieku__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[34];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("common_msgs_humble.msg._ieku.Ieku", full_classname_dest, 33) == 0);
  }
  common_msgs_humble__msg__Ieku * ros_message = _ros_message;
  {  // id
    PyObject * field = PyObject_GetAttrString(_pymsg, "id");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->id = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // lata
    PyObject * field = PyObject_GetAttrString(_pymsg, "lata");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->lata = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // lona
    PyObject * field = PyObject_GetAttrString(_pymsg, "lona");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->lona = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // latb
    PyObject * field = PyObject_GetAttrString(_pymsg, "latb");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->latb = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // lonb
    PyObject * field = PyObject_GetAttrString(_pymsg, "lonb");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->lonb = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // width
    PyObject * field = PyObject_GetAttrString(_pymsg, "width");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->width = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * common_msgs_humble__msg__ieku__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of Ieku */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("common_msgs_humble.msg._ieku");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "Ieku");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  common_msgs_humble__msg__Ieku * ros_message = (common_msgs_humble__msg__Ieku *)raw_ros_message;
  {  // id
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->id);
    {
      int rc = PyObject_SetAttrString(_pymessage, "id", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lata
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->lata);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lata", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lona
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->lona);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lona", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // latb
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->latb);
    {
      int rc = PyObject_SetAttrString(_pymessage, "latb", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // lonb
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->lonb);
    {
      int rc = PyObject_SetAttrString(_pymessage, "lonb", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // width
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->width);
    {
      int rc = PyObject_SetAttrString(_pymessage, "width", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
