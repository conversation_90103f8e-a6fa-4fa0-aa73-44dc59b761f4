// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from common_msgs_humble:msg/Collectpoint.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "common_msgs_humble/msg/detail/collectpoint__struct.h"
#include "common_msgs_humble/msg/detail/collectpoint__functions.h"


ROSIDL_GENERATOR_C_EXPORT
bool common_msgs_humble__msg__collectpoint__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[50];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("common_msgs_humble.msg._collectpoint.Collectpoint", full_classname_dest, 49) == 0);
  }
  common_msgs_humble__msg__Collectpoint * ros_message = _ros_message;
  {  // zonename
    PyObject * field = PyObject_GetAttrString(_pymsg, "zonename");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->zonename = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // index
    PyObject * field = PyObject_GetAttrString(_pymsg, "index");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->index = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // stoptime
    PyObject * field = PyObject_GetAttrString(_pymsg, "stoptime");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->stoptime = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // property
    PyObject * field = PyObject_GetAttrString(_pymsg, "property");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->property = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // orientation
    PyObject * field = PyObject_GetAttrString(_pymsg, "orientation");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->orientation = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * common_msgs_humble__msg__collectpoint__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of Collectpoint */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("common_msgs_humble.msg._collectpoint");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "Collectpoint");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  common_msgs_humble__msg__Collectpoint * ros_message = (common_msgs_humble__msg__Collectpoint *)raw_ros_message;
  {  // zonename
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->zonename);
    {
      int rc = PyObject_SetAttrString(_pymessage, "zonename", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // index
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->index);
    {
      int rc = PyObject_SetAttrString(_pymessage, "index", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // stoptime
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->stoptime);
    {
      int rc = PyObject_SetAttrString(_pymessage, "stoptime", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // property
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->property);
    {
      int rc = PyObject_SetAttrString(_pymessage, "property", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // orientation
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->orientation);
    {
      int rc = PyObject_SetAttrString(_pymessage, "orientation", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
