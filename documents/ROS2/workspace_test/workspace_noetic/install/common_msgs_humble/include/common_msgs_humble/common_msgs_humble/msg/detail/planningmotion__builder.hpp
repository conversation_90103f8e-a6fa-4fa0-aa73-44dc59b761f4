// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Planningmotion.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__PLANNINGMOTION__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__PLANNINGMOTION__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/planningmotion__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Planningmotion_timestamp
{
public:
  explicit Init_Planningmotion_timestamp(::common_msgs_humble::msg::Planningmotion & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Planningmotion timestamp(::common_msgs_humble::msg::Planningmotion::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Planningmotion msg_;
};

class Init_Planningmotion_isvalid
{
public:
  explicit Init_Planningmotion_isvalid(::common_msgs_humble::msg::Planningmotion & msg)
  : msg_(msg)
  {}
  Init_Planningmotion_timestamp isvalid(::common_msgs_humble::msg::Planningmotion::_isvalid_type arg)
  {
    msg_.isvalid = std::move(arg);
    return Init_Planningmotion_timestamp(msg_);
  }

private:
  ::common_msgs_humble::msg::Planningmotion msg_;
};

class Init_Planningmotion_obslatdis
{
public:
  explicit Init_Planningmotion_obslatdis(::common_msgs_humble::msg::Planningmotion & msg)
  : msg_(msg)
  {}
  Init_Planningmotion_isvalid obslatdis(::common_msgs_humble::msg::Planningmotion::_obslatdis_type arg)
  {
    msg_.obslatdis = std::move(arg);
    return Init_Planningmotion_isvalid(msg_);
  }

private:
  ::common_msgs_humble::msg::Planningmotion msg_;
};

class Init_Planningmotion_obslondis
{
public:
  explicit Init_Planningmotion_obslondis(::common_msgs_humble::msg::Planningmotion & msg)
  : msg_(msg)
  {}
  Init_Planningmotion_obslatdis obslondis(::common_msgs_humble::msg::Planningmotion::_obslondis_type arg)
  {
    msg_.obslondis = std::move(arg);
    return Init_Planningmotion_obslatdis(msg_);
  }

private:
  ::common_msgs_humble::msg::Planningmotion msg_;
};

class Init_Planningmotion_changelanedis
{
public:
  explicit Init_Planningmotion_changelanedis(::common_msgs_humble::msg::Planningmotion & msg)
  : msg_(msg)
  {}
  Init_Planningmotion_obslondis changelanedis(::common_msgs_humble::msg::Planningmotion::_changelanedis_type arg)
  {
    msg_.changelanedis = std::move(arg);
    return Init_Planningmotion_obslondis(msg_);
  }

private:
  ::common_msgs_humble::msg::Planningmotion msg_;
};

class Init_Planningmotion_guideangle
{
public:
  explicit Init_Planningmotion_guideangle(::common_msgs_humble::msg::Planningmotion & msg)
  : msg_(msg)
  {}
  Init_Planningmotion_changelanedis guideangle(::common_msgs_humble::msg::Planningmotion::_guideangle_type arg)
  {
    msg_.guideangle = std::move(arg);
    return Init_Planningmotion_changelanedis(msg_);
  }

private:
  ::common_msgs_humble::msg::Planningmotion msg_;
};

class Init_Planningmotion_guidespeed
{
public:
  explicit Init_Planningmotion_guidespeed(::common_msgs_humble::msg::Planningmotion & msg)
  : msg_(msg)
  {}
  Init_Planningmotion_guideangle guidespeed(::common_msgs_humble::msg::Planningmotion::_guidespeed_type arg)
  {
    msg_.guidespeed = std::move(arg);
    return Init_Planningmotion_guideangle(msg_);
  }

private:
  ::common_msgs_humble::msg::Planningmotion msg_;
};

class Init_Planningmotion_points
{
public:
  Init_Planningmotion_points()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Planningmotion_guidespeed points(::common_msgs_humble::msg::Planningmotion::_points_type arg)
  {
    msg_.points = std::move(arg);
    return Init_Planningmotion_guidespeed(msg_);
  }

private:
  ::common_msgs_humble::msg::Planningmotion msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Planningmotion>()
{
  return common_msgs_humble::msg::builder::Init_Planningmotion_points();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__PLANNINGMOTION__BUILDER_HPP_
