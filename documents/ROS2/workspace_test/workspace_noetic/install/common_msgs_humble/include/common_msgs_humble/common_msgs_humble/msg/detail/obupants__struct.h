// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Obupants.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUPANTS__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUPANTS__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'pants'
#include "common_msgs_humble/msg/detail/obupant__struct.h"

/// Struct defined in msg/Obupants in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Obupants
{
  common_msgs_humble__msg__Obupant__Sequence pants;
  uint8_t isvalid;
  int64_t timestamp;
  int32_t msg_cnt;
} common_msgs_humble__msg__Obupants;

// Struct for a sequence of common_msgs_humble__msg__Obupants.
typedef struct common_msgs_humble__msg__Obupants__Sequence
{
  common_msgs_humble__msg__Obupants * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Obupants__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUPANTS__STRUCT_H_
