// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from common_msgs_humble:msg/Error.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__ERROR__TRAITS_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__ERROR__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "common_msgs_humble/msg/detail/error__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

namespace common_msgs_humble
{

namespace msg
{

inline void to_flow_style_yaml(
  const Error & msg,
  std::ostream & out)
{
  out << "{";
  // member: timestamp
  {
    out << "timestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.timestamp, out);
    out << ", ";
  }

  // member: maincode
  {
    out << "maincode: ";
    rosidl_generator_traits::value_to_yaml(msg.maincode, out);
    out << ", ";
  }

  // member: subcode
  {
    out << "subcode: ";
    rosidl_generator_traits::value_to_yaml(msg.subcode, out);
    out << ", ";
  }

  // member: describe
  {
    out << "describe: ";
    rosidl_generator_traits::value_to_yaml(msg.describe, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const Error & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: timestamp
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "timestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.timestamp, out);
    out << "\n";
  }

  // member: maincode
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "maincode: ";
    rosidl_generator_traits::value_to_yaml(msg.maincode, out);
    out << "\n";
  }

  // member: subcode
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "subcode: ";
    rosidl_generator_traits::value_to_yaml(msg.subcode, out);
    out << "\n";
  }

  // member: describe
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "describe: ";
    rosidl_generator_traits::value_to_yaml(msg.describe, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const Error & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace common_msgs_humble

namespace rosidl_generator_traits
{

[[deprecated("use common_msgs_humble::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const common_msgs_humble::msg::Error & msg,
  std::ostream & out, size_t indentation = 0)
{
  common_msgs_humble::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use common_msgs_humble::msg::to_yaml() instead")]]
inline std::string to_yaml(const common_msgs_humble::msg::Error & msg)
{
  return common_msgs_humble::msg::to_yaml(msg);
}

template<>
inline const char * data_type<common_msgs_humble::msg::Error>()
{
  return "common_msgs_humble::msg::Error";
}

template<>
inline const char * name<common_msgs_humble::msg::Error>()
{
  return "common_msgs_humble/msg/Error";
}

template<>
struct has_fixed_size<common_msgs_humble::msg::Error>
  : std::integral_constant<bool, false> {};

template<>
struct has_bounded_size<common_msgs_humble::msg::Error>
  : std::integral_constant<bool, false> {};

template<>
struct is_message<common_msgs_humble::msg::Error>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__ERROR__TRAITS_HPP_
