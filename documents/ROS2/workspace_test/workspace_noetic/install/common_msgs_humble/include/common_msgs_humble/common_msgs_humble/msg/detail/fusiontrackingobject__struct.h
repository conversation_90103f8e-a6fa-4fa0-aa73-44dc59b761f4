﻿// NOLINT: This file starts with a BOM since it contain non-ASCII characters
// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Fusiontrackingobject.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__FUSIONTRACKINGOBJECT__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__FUSIONTRACKINGOBJECT__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'lidarobject'
// Member 'radarobject'
// Member 'obuobject'
#include "common_msgs_humble/msg/detail/sensorobject__struct.h"
// Member 'obupantobject'
#include "common_msgs_humble/msg/detail/obupant__struct.h"

/// Struct defined in msg/Fusiontrackingobject in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Fusiontrackingobject
{
  /// 目标来源 1:ldiar,2:radar,3:camera,4:gps,5:OBU,6:cloudplatform,7:fusion,8:tracking
  uint8_t objectsource;
  int64_t lidartimestamp;
  int64_t lidargpstime;
  common_msgs_humble__msg__Sensorobject lidarobject;
  int64_t radartimestamp;
  int64_t radargpstime;
  /// 匹配的radar目标
  common_msgs_humble__msg__Sensorobject radarobject;
  int64_t obutimestamp;
  int64_t obugpstime;
  /// 匹配的obu目标
  common_msgs_humble__msg__Sensorobject obuobject;
  /// 匹配的obu目标
  common_msgs_humble__msg__Obupant obupantobject;
} common_msgs_humble__msg__Fusiontrackingobject;

// Struct for a sequence of common_msgs_humble__msg__Fusiontrackingobject.
typedef struct common_msgs_humble__msg__Fusiontrackingobject__Sequence
{
  common_msgs_humble__msg__Fusiontrackingobject * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Fusiontrackingobject__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__FUSIONTRACKINGOBJECT__STRUCT_H_
