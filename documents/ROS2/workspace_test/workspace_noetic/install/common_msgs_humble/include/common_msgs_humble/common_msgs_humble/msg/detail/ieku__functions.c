// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from common_msgs_humble:msg/Ieku.idl
// generated code does not contain a copyright notice
#include "common_msgs_humble/msg/detail/ieku__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


bool
common_msgs_humble__msg__Ieku__init(common_msgs_humble__msg__Ieku * msg)
{
  if (!msg) {
    return false;
  }
  // id
  // lata
  // lona
  // latb
  // lonb
  // width
  return true;
}

void
common_msgs_humble__msg__Ieku__fini(common_msgs_humble__msg__Ieku * msg)
{
  if (!msg) {
    return;
  }
  // id
  // lata
  // lona
  // latb
  // lonb
  // width
}

bool
common_msgs_humble__msg__Ieku__are_equal(const common_msgs_humble__msg__Ieku * lhs, const common_msgs_humble__msg__Ieku * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // id
  if (lhs->id != rhs->id) {
    return false;
  }
  // lata
  if (lhs->lata != rhs->lata) {
    return false;
  }
  // lona
  if (lhs->lona != rhs->lona) {
    return false;
  }
  // latb
  if (lhs->latb != rhs->latb) {
    return false;
  }
  // lonb
  if (lhs->lonb != rhs->lonb) {
    return false;
  }
  // width
  if (lhs->width != rhs->width) {
    return false;
  }
  return true;
}

bool
common_msgs_humble__msg__Ieku__copy(
  const common_msgs_humble__msg__Ieku * input,
  common_msgs_humble__msg__Ieku * output)
{
  if (!input || !output) {
    return false;
  }
  // id
  output->id = input->id;
  // lata
  output->lata = input->lata;
  // lona
  output->lona = input->lona;
  // latb
  output->latb = input->latb;
  // lonb
  output->lonb = input->lonb;
  // width
  output->width = input->width;
  return true;
}

common_msgs_humble__msg__Ieku *
common_msgs_humble__msg__Ieku__create()
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Ieku * msg = (common_msgs_humble__msg__Ieku *)allocator.allocate(sizeof(common_msgs_humble__msg__Ieku), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(common_msgs_humble__msg__Ieku));
  bool success = common_msgs_humble__msg__Ieku__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
common_msgs_humble__msg__Ieku__destroy(common_msgs_humble__msg__Ieku * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    common_msgs_humble__msg__Ieku__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
common_msgs_humble__msg__Ieku__Sequence__init(common_msgs_humble__msg__Ieku__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Ieku * data = NULL;

  if (size) {
    data = (common_msgs_humble__msg__Ieku *)allocator.zero_allocate(size, sizeof(common_msgs_humble__msg__Ieku), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = common_msgs_humble__msg__Ieku__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        common_msgs_humble__msg__Ieku__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
common_msgs_humble__msg__Ieku__Sequence__fini(common_msgs_humble__msg__Ieku__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      common_msgs_humble__msg__Ieku__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

common_msgs_humble__msg__Ieku__Sequence *
common_msgs_humble__msg__Ieku__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Ieku__Sequence * array = (common_msgs_humble__msg__Ieku__Sequence *)allocator.allocate(sizeof(common_msgs_humble__msg__Ieku__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = common_msgs_humble__msg__Ieku__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
common_msgs_humble__msg__Ieku__Sequence__destroy(common_msgs_humble__msg__Ieku__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    common_msgs_humble__msg__Ieku__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
common_msgs_humble__msg__Ieku__Sequence__are_equal(const common_msgs_humble__msg__Ieku__Sequence * lhs, const common_msgs_humble__msg__Ieku__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!common_msgs_humble__msg__Ieku__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
common_msgs_humble__msg__Ieku__Sequence__copy(
  const common_msgs_humble__msg__Ieku__Sequence * input,
  common_msgs_humble__msg__Ieku__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(common_msgs_humble__msg__Ieku);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    common_msgs_humble__msg__Ieku * data =
      (common_msgs_humble__msg__Ieku *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!common_msgs_humble__msg__Ieku__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          common_msgs_humble__msg__Ieku__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!common_msgs_humble__msg__Ieku__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
