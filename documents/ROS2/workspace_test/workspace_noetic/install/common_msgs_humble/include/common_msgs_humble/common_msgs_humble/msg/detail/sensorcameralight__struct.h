// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Sensorcameralight.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSORCAMERALIGHT__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSORCAMERALIGHT__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

/// Struct defined in msg/Sensorcameralight in the package common_msgs_humble.
/**
  * cameralight[] light
 */
typedef struct common_msgs_humble__msg__Sensorcameralight
{
  uint8_t start;
  uint8_t isvalid;
  int64_t timestamp;
} common_msgs_humble__msg__Sensorcameralight;

// Struct for a sequence of common_msgs_humble__msg__Sensorcameralight.
typedef struct common_msgs_humble__msg__Sensorcameralight__Sequence
{
  common_msgs_humble__msg__Sensorcameralight * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Sensorcameralight__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSORCAMERALIGHT__STRUCT_H_
