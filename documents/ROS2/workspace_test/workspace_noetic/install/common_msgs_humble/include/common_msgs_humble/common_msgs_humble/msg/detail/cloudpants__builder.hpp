// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Cloudpants.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__CLOUDPANTS__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__CLOUDPANTS__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/cloudpants__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Cloudpants_pants
{
public:
  explicit Init_Cloudpants_pants(::common_msgs_humble::msg::Cloudpants & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Cloudpants pants(::common_msgs_humble::msg::Cloudpants::_pants_type arg)
  {
    msg_.pants = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Cloudpants msg_;
};

class Init_Cloudpants_count
{
public:
  explicit Init_Cloudpants_count(::common_msgs_humble::msg::Cloudpants & msg)
  : msg_(msg)
  {}
  Init_Cloudpants_pants count(::common_msgs_humble::msg::Cloudpants::_count_type arg)
  {
    msg_.count = std::move(arg);
    return Init_Cloudpants_pants(msg_);
  }

private:
  ::common_msgs_humble::msg::Cloudpants msg_;
};

class Init_Cloudpants_frameid
{
public:
  explicit Init_Cloudpants_frameid(::common_msgs_humble::msg::Cloudpants & msg)
  : msg_(msg)
  {}
  Init_Cloudpants_count frameid(::common_msgs_humble::msg::Cloudpants::_frameid_type arg)
  {
    msg_.frameid = std::move(arg);
    return Init_Cloudpants_count(msg_);
  }

private:
  ::common_msgs_humble::msg::Cloudpants msg_;
};

class Init_Cloudpants_timestamp
{
public:
  Init_Cloudpants_timestamp()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Cloudpants_frameid timestamp(::common_msgs_humble::msg::Cloudpants::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return Init_Cloudpants_frameid(msg_);
  }

private:
  ::common_msgs_humble::msg::Cloudpants msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Cloudpants>()
{
  return common_msgs_humble::msg::builder::Init_Cloudpants_timestamp();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__CLOUDPANTS__BUILDER_HPP_
