// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from common_msgs_humble:msg/Decisionbehavior.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__DECISIONBEHAVIOR__TRAITS_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__DECISIONBEHAVIOR__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "common_msgs_humble/msg/detail/decisionbehavior__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

// Include directives for member types
// Member 'obs'
#include "common_msgs_humble/msg/detail/sensorobject__traits.hpp"

namespace common_msgs_humble
{

namespace msg
{

inline void to_flow_style_yaml(
  const Decisionbehavior & msg,
  std::ostream & out)
{
  out << "{";
  // member: drivebehavior
  {
    out << "drivebehavior: ";
    rosidl_generator_traits::value_to_yaml(msg.drivebehavior, out);
    out << ", ";
  }

  // member: obs
  {
    if (msg.obs.size() == 0) {
      out << "obs: []";
    } else {
      out << "obs: [";
      size_t pending_items = msg.obs.size();
      for (auto item : msg.obs) {
        to_flow_style_yaml(item, out);
        if (--pending_items > 0) {
          out << ", ";
        }
      }
      out << "]";
    }
    out << ", ";
  }

  // member: isvalid
  {
    out << "isvalid: ";
    rosidl_generator_traits::value_to_yaml(msg.isvalid, out);
    out << ", ";
  }

  // member: turnlights
  {
    out << "turnlights: ";
    rosidl_generator_traits::value_to_yaml(msg.turnlights, out);
    out << ", ";
  }

  // member: laneblock
  {
    out << "laneblock: ";
    rosidl_generator_traits::value_to_yaml(msg.laneblock, out);
    out << ", ";
  }

  // member: door
  {
    out << "door: ";
    rosidl_generator_traits::value_to_yaml(msg.door, out);
    out << ", ";
  }

  // member: timestamp
  {
    out << "timestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.timestamp, out);
    out << ", ";
  }

  // member: mergetrigger
  {
    out << "mergetrigger: ";
    rosidl_generator_traits::value_to_yaml(msg.mergetrigger, out);
    out << ", ";
  }

  // member: guidespeed
  {
    out << "guidespeed: ";
    rosidl_generator_traits::value_to_yaml(msg.guidespeed, out);
    out << ", ";
  }

  // member: avoidsituation
  {
    out << "avoidsituation: ";
    rosidl_generator_traits::value_to_yaml(msg.avoidsituation, out);
    out << ", ";
  }

  // member: alert
  {
    out << "alert: ";
    rosidl_generator_traits::value_to_yaml(msg.alert, out);
    out << ", ";
  }

  // member: deviation
  {
    out << "deviation: ";
    rosidl_generator_traits::value_to_yaml(msg.deviation, out);
    out << ", ";
  }

  // member: starttime
  {
    out << "starttime: ";
    rosidl_generator_traits::value_to_yaml(msg.starttime, out);
    out << ", ";
  }

  // member: endtime
  {
    out << "endtime: ";
    rosidl_generator_traits::value_to_yaml(msg.endtime, out);
    out << ", ";
  }

  // member: carworkstatus
  {
    out << "carworkstatus: ";
    rosidl_generator_traits::value_to_yaml(msg.carworkstatus, out);
    out << ", ";
  }

  // member: stationblock
  {
    out << "stationblock: ";
    rosidl_generator_traits::value_to_yaml(msg.stationblock, out);
    out << ", ";
  }

  // member: needreplan
  {
    out << "needreplan: ";
    rosidl_generator_traits::value_to_yaml(msg.needreplan, out);
    out << ", ";
  }

  // member: virtualpointtype
  {
    out << "virtualpointtype: ";
    rosidl_generator_traits::value_to_yaml(msg.virtualpointtype, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const Decisionbehavior & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: drivebehavior
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "drivebehavior: ";
    rosidl_generator_traits::value_to_yaml(msg.drivebehavior, out);
    out << "\n";
  }

  // member: obs
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    if (msg.obs.size() == 0) {
      out << "obs: []\n";
    } else {
      out << "obs:\n";
      for (auto item : msg.obs) {
        if (indentation > 0) {
          out << std::string(indentation, ' ');
        }
        out << "-\n";
        to_block_style_yaml(item, out, indentation + 2);
      }
    }
  }

  // member: isvalid
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "isvalid: ";
    rosidl_generator_traits::value_to_yaml(msg.isvalid, out);
    out << "\n";
  }

  // member: turnlights
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "turnlights: ";
    rosidl_generator_traits::value_to_yaml(msg.turnlights, out);
    out << "\n";
  }

  // member: laneblock
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "laneblock: ";
    rosidl_generator_traits::value_to_yaml(msg.laneblock, out);
    out << "\n";
  }

  // member: door
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "door: ";
    rosidl_generator_traits::value_to_yaml(msg.door, out);
    out << "\n";
  }

  // member: timestamp
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "timestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.timestamp, out);
    out << "\n";
  }

  // member: mergetrigger
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "mergetrigger: ";
    rosidl_generator_traits::value_to_yaml(msg.mergetrigger, out);
    out << "\n";
  }

  // member: guidespeed
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "guidespeed: ";
    rosidl_generator_traits::value_to_yaml(msg.guidespeed, out);
    out << "\n";
  }

  // member: avoidsituation
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "avoidsituation: ";
    rosidl_generator_traits::value_to_yaml(msg.avoidsituation, out);
    out << "\n";
  }

  // member: alert
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "alert: ";
    rosidl_generator_traits::value_to_yaml(msg.alert, out);
    out << "\n";
  }

  // member: deviation
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "deviation: ";
    rosidl_generator_traits::value_to_yaml(msg.deviation, out);
    out << "\n";
  }

  // member: starttime
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "starttime: ";
    rosidl_generator_traits::value_to_yaml(msg.starttime, out);
    out << "\n";
  }

  // member: endtime
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "endtime: ";
    rosidl_generator_traits::value_to_yaml(msg.endtime, out);
    out << "\n";
  }

  // member: carworkstatus
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "carworkstatus: ";
    rosidl_generator_traits::value_to_yaml(msg.carworkstatus, out);
    out << "\n";
  }

  // member: stationblock
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "stationblock: ";
    rosidl_generator_traits::value_to_yaml(msg.stationblock, out);
    out << "\n";
  }

  // member: needreplan
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "needreplan: ";
    rosidl_generator_traits::value_to_yaml(msg.needreplan, out);
    out << "\n";
  }

  // member: virtualpointtype
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "virtualpointtype: ";
    rosidl_generator_traits::value_to_yaml(msg.virtualpointtype, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const Decisionbehavior & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace common_msgs_humble

namespace rosidl_generator_traits
{

[[deprecated("use common_msgs_humble::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const common_msgs_humble::msg::Decisionbehavior & msg,
  std::ostream & out, size_t indentation = 0)
{
  common_msgs_humble::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use common_msgs_humble::msg::to_yaml() instead")]]
inline std::string to_yaml(const common_msgs_humble::msg::Decisionbehavior & msg)
{
  return common_msgs_humble::msg::to_yaml(msg);
}

template<>
inline const char * data_type<common_msgs_humble::msg::Decisionbehavior>()
{
  return "common_msgs_humble::msg::Decisionbehavior";
}

template<>
inline const char * name<common_msgs_humble::msg::Decisionbehavior>()
{
  return "common_msgs_humble/msg/Decisionbehavior";
}

template<>
struct has_fixed_size<common_msgs_humble::msg::Decisionbehavior>
  : std::integral_constant<bool, false> {};

template<>
struct has_bounded_size<common_msgs_humble::msg::Decisionbehavior>
  : std::integral_constant<bool, false> {};

template<>
struct is_message<common_msgs_humble::msg::Decisionbehavior>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__DECISIONBEHAVIOR__TRAITS_HPP_
