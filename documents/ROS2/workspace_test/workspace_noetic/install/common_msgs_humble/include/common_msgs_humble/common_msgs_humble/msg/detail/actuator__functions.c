// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from common_msgs_humble:msg/Actuator.idl
// generated code does not contain a copyright notice
#include "common_msgs_humble/msg/detail/actuator__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


bool
common_msgs_humble__msg__Actuator__init(common_msgs_humble__msg__Actuator * msg)
{
  if (!msg) {
    return false;
  }
  // epsmethod
  // epsangle
  // espmethod
  // escbrakepress
  // gaspedal
  // sysstatus
  // speed
  // lights
  // turnlight
  // gear
  // epb
  // door
  // isvalid
  // timestamp
  // sendsuccess
  // brakepedal
  // warning
  // error
  // battery
  // controlover
  // steerspeed
  // accelpos
  // breakflag
  // breakpos
  // yaw
  // mil
  // soc
  // batvol
  // acc
  // oilperhour
  // oilhundredkmconsume
  // oilconsume
  // autoctrlsig
  // totalvoltage
  // totalcurrent
  // motorspeed
  // motortorque
  // wirecontrolstatus
  // blinkerstatus
  // accx
  // gaspedalcar
  return true;
}

void
common_msgs_humble__msg__Actuator__fini(common_msgs_humble__msg__Actuator * msg)
{
  if (!msg) {
    return;
  }
  // epsmethod
  // epsangle
  // espmethod
  // escbrakepress
  // gaspedal
  // sysstatus
  // speed
  // lights
  // turnlight
  // gear
  // epb
  // door
  // isvalid
  // timestamp
  // sendsuccess
  // brakepedal
  // warning
  // error
  // battery
  // controlover
  // steerspeed
  // accelpos
  // breakflag
  // breakpos
  // yaw
  // mil
  // soc
  // batvol
  // acc
  // oilperhour
  // oilhundredkmconsume
  // oilconsume
  // autoctrlsig
  // totalvoltage
  // totalcurrent
  // motorspeed
  // motortorque
  // wirecontrolstatus
  // blinkerstatus
  // accx
  // gaspedalcar
}

bool
common_msgs_humble__msg__Actuator__are_equal(const common_msgs_humble__msg__Actuator * lhs, const common_msgs_humble__msg__Actuator * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // epsmethod
  if (lhs->epsmethod != rhs->epsmethod) {
    return false;
  }
  // epsangle
  if (lhs->epsangle != rhs->epsangle) {
    return false;
  }
  // espmethod
  if (lhs->espmethod != rhs->espmethod) {
    return false;
  }
  // escbrakepress
  if (lhs->escbrakepress != rhs->escbrakepress) {
    return false;
  }
  // gaspedal
  if (lhs->gaspedal != rhs->gaspedal) {
    return false;
  }
  // sysstatus
  if (lhs->sysstatus != rhs->sysstatus) {
    return false;
  }
  // speed
  if (lhs->speed != rhs->speed) {
    return false;
  }
  // lights
  if (lhs->lights != rhs->lights) {
    return false;
  }
  // turnlight
  if (lhs->turnlight != rhs->turnlight) {
    return false;
  }
  // gear
  if (lhs->gear != rhs->gear) {
    return false;
  }
  // epb
  if (lhs->epb != rhs->epb) {
    return false;
  }
  // door
  if (lhs->door != rhs->door) {
    return false;
  }
  // isvalid
  if (lhs->isvalid != rhs->isvalid) {
    return false;
  }
  // timestamp
  if (lhs->timestamp != rhs->timestamp) {
    return false;
  }
  // sendsuccess
  if (lhs->sendsuccess != rhs->sendsuccess) {
    return false;
  }
  // brakepedal
  if (lhs->brakepedal != rhs->brakepedal) {
    return false;
  }
  // warning
  if (lhs->warning != rhs->warning) {
    return false;
  }
  // error
  if (lhs->error != rhs->error) {
    return false;
  }
  // battery
  if (lhs->battery != rhs->battery) {
    return false;
  }
  // controlover
  if (lhs->controlover != rhs->controlover) {
    return false;
  }
  // steerspeed
  if (lhs->steerspeed != rhs->steerspeed) {
    return false;
  }
  // accelpos
  if (lhs->accelpos != rhs->accelpos) {
    return false;
  }
  // breakflag
  if (lhs->breakflag != rhs->breakflag) {
    return false;
  }
  // breakpos
  if (lhs->breakpos != rhs->breakpos) {
    return false;
  }
  // yaw
  if (lhs->yaw != rhs->yaw) {
    return false;
  }
  // mil
  if (lhs->mil != rhs->mil) {
    return false;
  }
  // soc
  if (lhs->soc != rhs->soc) {
    return false;
  }
  // batvol
  if (lhs->batvol != rhs->batvol) {
    return false;
  }
  // acc
  if (lhs->acc != rhs->acc) {
    return false;
  }
  // oilperhour
  if (lhs->oilperhour != rhs->oilperhour) {
    return false;
  }
  // oilhundredkmconsume
  if (lhs->oilhundredkmconsume != rhs->oilhundredkmconsume) {
    return false;
  }
  // oilconsume
  if (lhs->oilconsume != rhs->oilconsume) {
    return false;
  }
  // autoctrlsig
  if (lhs->autoctrlsig != rhs->autoctrlsig) {
    return false;
  }
  // totalvoltage
  if (lhs->totalvoltage != rhs->totalvoltage) {
    return false;
  }
  // totalcurrent
  if (lhs->totalcurrent != rhs->totalcurrent) {
    return false;
  }
  // motorspeed
  if (lhs->motorspeed != rhs->motorspeed) {
    return false;
  }
  // motortorque
  if (lhs->motortorque != rhs->motortorque) {
    return false;
  }
  // wirecontrolstatus
  if (lhs->wirecontrolstatus != rhs->wirecontrolstatus) {
    return false;
  }
  // blinkerstatus
  if (lhs->blinkerstatus != rhs->blinkerstatus) {
    return false;
  }
  // accx
  if (lhs->accx != rhs->accx) {
    return false;
  }
  // gaspedalcar
  if (lhs->gaspedalcar != rhs->gaspedalcar) {
    return false;
  }
  return true;
}

bool
common_msgs_humble__msg__Actuator__copy(
  const common_msgs_humble__msg__Actuator * input,
  common_msgs_humble__msg__Actuator * output)
{
  if (!input || !output) {
    return false;
  }
  // epsmethod
  output->epsmethod = input->epsmethod;
  // epsangle
  output->epsangle = input->epsangle;
  // espmethod
  output->espmethod = input->espmethod;
  // escbrakepress
  output->escbrakepress = input->escbrakepress;
  // gaspedal
  output->gaspedal = input->gaspedal;
  // sysstatus
  output->sysstatus = input->sysstatus;
  // speed
  output->speed = input->speed;
  // lights
  output->lights = input->lights;
  // turnlight
  output->turnlight = input->turnlight;
  // gear
  output->gear = input->gear;
  // epb
  output->epb = input->epb;
  // door
  output->door = input->door;
  // isvalid
  output->isvalid = input->isvalid;
  // timestamp
  output->timestamp = input->timestamp;
  // sendsuccess
  output->sendsuccess = input->sendsuccess;
  // brakepedal
  output->brakepedal = input->brakepedal;
  // warning
  output->warning = input->warning;
  // error
  output->error = input->error;
  // battery
  output->battery = input->battery;
  // controlover
  output->controlover = input->controlover;
  // steerspeed
  output->steerspeed = input->steerspeed;
  // accelpos
  output->accelpos = input->accelpos;
  // breakflag
  output->breakflag = input->breakflag;
  // breakpos
  output->breakpos = input->breakpos;
  // yaw
  output->yaw = input->yaw;
  // mil
  output->mil = input->mil;
  // soc
  output->soc = input->soc;
  // batvol
  output->batvol = input->batvol;
  // acc
  output->acc = input->acc;
  // oilperhour
  output->oilperhour = input->oilperhour;
  // oilhundredkmconsume
  output->oilhundredkmconsume = input->oilhundredkmconsume;
  // oilconsume
  output->oilconsume = input->oilconsume;
  // autoctrlsig
  output->autoctrlsig = input->autoctrlsig;
  // totalvoltage
  output->totalvoltage = input->totalvoltage;
  // totalcurrent
  output->totalcurrent = input->totalcurrent;
  // motorspeed
  output->motorspeed = input->motorspeed;
  // motortorque
  output->motortorque = input->motortorque;
  // wirecontrolstatus
  output->wirecontrolstatus = input->wirecontrolstatus;
  // blinkerstatus
  output->blinkerstatus = input->blinkerstatus;
  // accx
  output->accx = input->accx;
  // gaspedalcar
  output->gaspedalcar = input->gaspedalcar;
  return true;
}

common_msgs_humble__msg__Actuator *
common_msgs_humble__msg__Actuator__create()
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Actuator * msg = (common_msgs_humble__msg__Actuator *)allocator.allocate(sizeof(common_msgs_humble__msg__Actuator), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(common_msgs_humble__msg__Actuator));
  bool success = common_msgs_humble__msg__Actuator__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
common_msgs_humble__msg__Actuator__destroy(common_msgs_humble__msg__Actuator * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    common_msgs_humble__msg__Actuator__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
common_msgs_humble__msg__Actuator__Sequence__init(common_msgs_humble__msg__Actuator__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Actuator * data = NULL;

  if (size) {
    data = (common_msgs_humble__msg__Actuator *)allocator.zero_allocate(size, sizeof(common_msgs_humble__msg__Actuator), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = common_msgs_humble__msg__Actuator__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        common_msgs_humble__msg__Actuator__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
common_msgs_humble__msg__Actuator__Sequence__fini(common_msgs_humble__msg__Actuator__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      common_msgs_humble__msg__Actuator__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

common_msgs_humble__msg__Actuator__Sequence *
common_msgs_humble__msg__Actuator__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Actuator__Sequence * array = (common_msgs_humble__msg__Actuator__Sequence *)allocator.allocate(sizeof(common_msgs_humble__msg__Actuator__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = common_msgs_humble__msg__Actuator__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
common_msgs_humble__msg__Actuator__Sequence__destroy(common_msgs_humble__msg__Actuator__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    common_msgs_humble__msg__Actuator__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
common_msgs_humble__msg__Actuator__Sequence__are_equal(const common_msgs_humble__msg__Actuator__Sequence * lhs, const common_msgs_humble__msg__Actuator__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!common_msgs_humble__msg__Actuator__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
common_msgs_humble__msg__Actuator__Sequence__copy(
  const common_msgs_humble__msg__Actuator__Sequence * input,
  common_msgs_humble__msg__Actuator__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(common_msgs_humble__msg__Actuator);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    common_msgs_humble__msg__Actuator * data =
      (common_msgs_humble__msg__Actuator *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!common_msgs_humble__msg__Actuator__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          common_msgs_humble__msg__Actuator__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!common_msgs_humble__msg__Actuator__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
