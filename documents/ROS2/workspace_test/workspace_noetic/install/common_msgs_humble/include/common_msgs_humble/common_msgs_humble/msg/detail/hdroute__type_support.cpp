// generated from rosidl_typesupport_introspection_cpp/resource/idl__type_support.cpp.em
// with input from common_msgs_humble:msg/Hdroute.idl
// generated code does not contain a copyright notice

#include "array"
#include "cstddef"
#include "string"
#include "vector"
#include "rosidl_runtime_c/message_type_support_struct.h"
#include "rosidl_typesupport_cpp/message_type_support.hpp"
#include "rosidl_typesupport_interface/macros.h"
#include "common_msgs_humble/msg/detail/hdroute__struct.hpp"
#include "rosidl_typesupport_introspection_cpp/field_types.hpp"
#include "rosidl_typesupport_introspection_cpp/identifier.hpp"
#include "rosidl_typesupport_introspection_cpp/message_introspection.hpp"
#include "rosidl_typesupport_introspection_cpp/message_type_support_decl.hpp"
#include "rosidl_typesupport_introspection_cpp/visibility_control.h"

namespace common_msgs_humble
{

namespace msg
{

namespace rosidl_typesupport_introspection_cpp
{

void Hdroute_init_function(
  void * message_memory, rosidl_runtime_cpp::MessageInitialization _init)
{
  new (message_memory) common_msgs_humble::msg::Hdroute(_init);
}

void Hdroute_fini_function(void * message_memory)
{
  auto typed_message = static_cast<common_msgs_humble::msg::Hdroute *>(message_memory);
  typed_message->~Hdroute();
}

size_t size_function__Hdroute__map(const void * untyped_member)
{
  const auto * member = reinterpret_cast<const std::vector<common_msgs_humble::msg::Hdmap> *>(untyped_member);
  return member->size();
}

const void * get_const_function__Hdroute__map(const void * untyped_member, size_t index)
{
  const auto & member =
    *reinterpret_cast<const std::vector<common_msgs_humble::msg::Hdmap> *>(untyped_member);
  return &member[index];
}

void * get_function__Hdroute__map(void * untyped_member, size_t index)
{
  auto & member =
    *reinterpret_cast<std::vector<common_msgs_humble::msg::Hdmap> *>(untyped_member);
  return &member[index];
}

void fetch_function__Hdroute__map(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const auto & item = *reinterpret_cast<const common_msgs_humble::msg::Hdmap *>(
    get_const_function__Hdroute__map(untyped_member, index));
  auto & value = *reinterpret_cast<common_msgs_humble::msg::Hdmap *>(untyped_value);
  value = item;
}

void assign_function__Hdroute__map(
  void * untyped_member, size_t index, const void * untyped_value)
{
  auto & item = *reinterpret_cast<common_msgs_humble::msg::Hdmap *>(
    get_function__Hdroute__map(untyped_member, index));
  const auto & value = *reinterpret_cast<const common_msgs_humble::msg::Hdmap *>(untyped_value);
  item = value;
}

void resize_function__Hdroute__map(void * untyped_member, size_t size)
{
  auto * member =
    reinterpret_cast<std::vector<common_msgs_humble::msg::Hdmap> *>(untyped_member);
  member->resize(size);
}

static const ::rosidl_typesupport_introspection_cpp::MessageMember Hdroute_message_member_array[4] = {
  {
    "map",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    ::rosidl_typesupport_introspection_cpp::get_message_type_support_handle<common_msgs_humble::msg::Hdmap>(),  // members of sub message
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble::msg::Hdroute, map),  // bytes offset in struct
    nullptr,  // default value
    size_function__Hdroute__map,  // size() function pointer
    get_const_function__Hdroute__map,  // get_const(index) function pointer
    get_function__Hdroute__map,  // get(index) function pointer
    fetch_function__Hdroute__map,  // fetch(index, &value) function pointer
    assign_function__Hdroute__map,  // assign(index, value) function pointer
    resize_function__Hdroute__map  // resize(index) function pointer
  },
  {
    "isvalid",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT8,  // type
    0,  // upper bound of string
    nullptr,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble::msg::Hdroute, isvalid),  // bytes offset in struct
    nullptr,  // default value
    nullptr,  // size() function pointer
    nullptr,  // get_const(index) function pointer
    nullptr,  // get(index) function pointer
    nullptr,  // fetch(index, &value) function pointer
    nullptr,  // assign(index, value) function pointer
    nullptr  // resize(index) function pointer
  },
  {
    "timestamp",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_INT64,  // type
    0,  // upper bound of string
    nullptr,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble::msg::Hdroute, timestamp),  // bytes offset in struct
    nullptr,  // default value
    nullptr,  // size() function pointer
    nullptr,  // get_const(index) function pointer
    nullptr,  // get(index) function pointer
    nullptr,  // fetch(index, &value) function pointer
    nullptr,  // assign(index, value) function pointer
    nullptr  // resize(index) function pointer
  },
  {
    "index",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_INT64,  // type
    0,  // upper bound of string
    nullptr,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble::msg::Hdroute, index),  // bytes offset in struct
    nullptr,  // default value
    nullptr,  // size() function pointer
    nullptr,  // get_const(index) function pointer
    nullptr,  // get(index) function pointer
    nullptr,  // fetch(index, &value) function pointer
    nullptr,  // assign(index, value) function pointer
    nullptr  // resize(index) function pointer
  }
};

static const ::rosidl_typesupport_introspection_cpp::MessageMembers Hdroute_message_members = {
  "common_msgs_humble::msg",  // message namespace
  "Hdroute",  // message name
  4,  // number of fields
  sizeof(common_msgs_humble::msg::Hdroute),
  Hdroute_message_member_array,  // message members
  Hdroute_init_function,  // function to initialize message memory (memory has to be allocated)
  Hdroute_fini_function  // function to terminate message instance (will not free memory)
};

static const rosidl_message_type_support_t Hdroute_message_type_support_handle = {
  ::rosidl_typesupport_introspection_cpp::typesupport_identifier,
  &Hdroute_message_members,
  get_message_typesupport_handle_function,
};

}  // namespace rosidl_typesupport_introspection_cpp

}  // namespace msg

}  // namespace common_msgs_humble


namespace rosidl_typesupport_introspection_cpp
{

template<>
ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_PUBLIC
const rosidl_message_type_support_t *
get_message_type_support_handle<common_msgs_humble::msg::Hdroute>()
{
  return &::common_msgs_humble::msg::rosidl_typesupport_introspection_cpp::Hdroute_message_type_support_handle;
}

}  // namespace rosidl_typesupport_introspection_cpp

#ifdef __cplusplus
extern "C"
{
#endif

ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_PUBLIC
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_cpp, common_msgs_humble, msg, Hdroute)() {
  return &::common_msgs_humble::msg::rosidl_typesupport_introspection_cpp::Hdroute_message_type_support_handle;
}

#ifdef __cplusplus
}
#endif
