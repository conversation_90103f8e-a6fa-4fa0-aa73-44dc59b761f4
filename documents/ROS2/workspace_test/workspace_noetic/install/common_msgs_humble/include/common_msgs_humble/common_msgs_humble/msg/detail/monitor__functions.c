// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from common_msgs_humble:msg/Monitor.idl
// generated code does not contain a copyright notice
#include "common_msgs_humble/msg/detail/monitor__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


// Include directives for member types
// Member `valuelight`
// Member `valuetext`
// Member `dotcnt`
#include "rosidl_runtime_c/primitives_sequence_functions.h"
// Member `deslight`
// Member `destext`
#include "rosidl_runtime_c/string_functions.h"

bool
common_msgs_humble__msg__Monitor__init(common_msgs_humble__msg__Monitor * msg)
{
  if (!msg) {
    return false;
  }
  // valuelight
  if (!rosidl_runtime_c__octet__Sequence__init(&msg->valuelight, 0)) {
    common_msgs_humble__msg__Monitor__fini(msg);
    return false;
  }
  // deslight
  if (!rosidl_runtime_c__String__Sequence__init(&msg->deslight, 0)) {
    common_msgs_humble__msg__Monitor__fini(msg);
    return false;
  }
  // valuetext
  if (!rosidl_runtime_c__double__Sequence__init(&msg->valuetext, 0)) {
    common_msgs_humble__msg__Monitor__fini(msg);
    return false;
  }
  // destext
  if (!rosidl_runtime_c__String__Sequence__init(&msg->destext, 0)) {
    common_msgs_humble__msg__Monitor__fini(msg);
    return false;
  }
  // dotcnt
  if (!rosidl_runtime_c__octet__Sequence__init(&msg->dotcnt, 0)) {
    common_msgs_humble__msg__Monitor__fini(msg);
    return false;
  }
  // timestamp
  // status
  // sensorstate
  return true;
}

void
common_msgs_humble__msg__Monitor__fini(common_msgs_humble__msg__Monitor * msg)
{
  if (!msg) {
    return;
  }
  // valuelight
  rosidl_runtime_c__octet__Sequence__fini(&msg->valuelight);
  // deslight
  rosidl_runtime_c__String__Sequence__fini(&msg->deslight);
  // valuetext
  rosidl_runtime_c__double__Sequence__fini(&msg->valuetext);
  // destext
  rosidl_runtime_c__String__Sequence__fini(&msg->destext);
  // dotcnt
  rosidl_runtime_c__octet__Sequence__fini(&msg->dotcnt);
  // timestamp
  // status
  // sensorstate
}

bool
common_msgs_humble__msg__Monitor__are_equal(const common_msgs_humble__msg__Monitor * lhs, const common_msgs_humble__msg__Monitor * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // valuelight
  if (!rosidl_runtime_c__octet__Sequence__are_equal(
      &(lhs->valuelight), &(rhs->valuelight)))
  {
    return false;
  }
  // deslight
  if (!rosidl_runtime_c__String__Sequence__are_equal(
      &(lhs->deslight), &(rhs->deslight)))
  {
    return false;
  }
  // valuetext
  if (!rosidl_runtime_c__double__Sequence__are_equal(
      &(lhs->valuetext), &(rhs->valuetext)))
  {
    return false;
  }
  // destext
  if (!rosidl_runtime_c__String__Sequence__are_equal(
      &(lhs->destext), &(rhs->destext)))
  {
    return false;
  }
  // dotcnt
  if (!rosidl_runtime_c__octet__Sequence__are_equal(
      &(lhs->dotcnt), &(rhs->dotcnt)))
  {
    return false;
  }
  // timestamp
  if (lhs->timestamp != rhs->timestamp) {
    return false;
  }
  // status
  if (lhs->status != rhs->status) {
    return false;
  }
  // sensorstate
  if (lhs->sensorstate != rhs->sensorstate) {
    return false;
  }
  return true;
}

bool
common_msgs_humble__msg__Monitor__copy(
  const common_msgs_humble__msg__Monitor * input,
  common_msgs_humble__msg__Monitor * output)
{
  if (!input || !output) {
    return false;
  }
  // valuelight
  if (!rosidl_runtime_c__octet__Sequence__copy(
      &(input->valuelight), &(output->valuelight)))
  {
    return false;
  }
  // deslight
  if (!rosidl_runtime_c__String__Sequence__copy(
      &(input->deslight), &(output->deslight)))
  {
    return false;
  }
  // valuetext
  if (!rosidl_runtime_c__double__Sequence__copy(
      &(input->valuetext), &(output->valuetext)))
  {
    return false;
  }
  // destext
  if (!rosidl_runtime_c__String__Sequence__copy(
      &(input->destext), &(output->destext)))
  {
    return false;
  }
  // dotcnt
  if (!rosidl_runtime_c__octet__Sequence__copy(
      &(input->dotcnt), &(output->dotcnt)))
  {
    return false;
  }
  // timestamp
  output->timestamp = input->timestamp;
  // status
  output->status = input->status;
  // sensorstate
  output->sensorstate = input->sensorstate;
  return true;
}

common_msgs_humble__msg__Monitor *
common_msgs_humble__msg__Monitor__create()
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Monitor * msg = (common_msgs_humble__msg__Monitor *)allocator.allocate(sizeof(common_msgs_humble__msg__Monitor), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(common_msgs_humble__msg__Monitor));
  bool success = common_msgs_humble__msg__Monitor__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
common_msgs_humble__msg__Monitor__destroy(common_msgs_humble__msg__Monitor * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    common_msgs_humble__msg__Monitor__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
common_msgs_humble__msg__Monitor__Sequence__init(common_msgs_humble__msg__Monitor__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Monitor * data = NULL;

  if (size) {
    data = (common_msgs_humble__msg__Monitor *)allocator.zero_allocate(size, sizeof(common_msgs_humble__msg__Monitor), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = common_msgs_humble__msg__Monitor__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        common_msgs_humble__msg__Monitor__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
common_msgs_humble__msg__Monitor__Sequence__fini(common_msgs_humble__msg__Monitor__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      common_msgs_humble__msg__Monitor__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

common_msgs_humble__msg__Monitor__Sequence *
common_msgs_humble__msg__Monitor__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Monitor__Sequence * array = (common_msgs_humble__msg__Monitor__Sequence *)allocator.allocate(sizeof(common_msgs_humble__msg__Monitor__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = common_msgs_humble__msg__Monitor__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
common_msgs_humble__msg__Monitor__Sequence__destroy(common_msgs_humble__msg__Monitor__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    common_msgs_humble__msg__Monitor__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
common_msgs_humble__msg__Monitor__Sequence__are_equal(const common_msgs_humble__msg__Monitor__Sequence * lhs, const common_msgs_humble__msg__Monitor__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!common_msgs_humble__msg__Monitor__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
common_msgs_humble__msg__Monitor__Sequence__copy(
  const common_msgs_humble__msg__Monitor__Sequence * input,
  common_msgs_humble__msg__Monitor__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(common_msgs_humble__msg__Monitor);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    common_msgs_humble__msg__Monitor * data =
      (common_msgs_humble__msg__Monitor *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!common_msgs_humble__msg__Monitor__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          common_msgs_humble__msg__Monitor__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!common_msgs_humble__msg__Monitor__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
