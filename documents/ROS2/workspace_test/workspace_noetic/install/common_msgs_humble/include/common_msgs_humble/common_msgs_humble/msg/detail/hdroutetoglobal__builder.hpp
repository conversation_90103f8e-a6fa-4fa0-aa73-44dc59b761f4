// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Hdroutetoglobal.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__HDROUTETOGLOBAL__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__HDROUTETOGLOBAL__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/hdroutetoglobal__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Hdroutetoglobal_timestamp
{
public:
  explicit Init_Hdroutetoglobal_timestamp(::common_msgs_humble::msg::Hdroutetoglobal & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Hdroutetoglobal timestamp(::common_msgs_humble::msg::Hdroutetoglobal::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Hdroutetoglobal msg_;
};

class Init_Hdroutetoglobal_isvalid
{
public:
  explicit Init_Hdroutetoglobal_isvalid(::common_msgs_humble::msg::Hdroutetoglobal & msg)
  : msg_(msg)
  {}
  Init_Hdroutetoglobal_timestamp isvalid(::common_msgs_humble::msg::Hdroutetoglobal::_isvalid_type arg)
  {
    msg_.isvalid = std::move(arg);
    return Init_Hdroutetoglobal_timestamp(msg_);
  }

private:
  ::common_msgs_humble::msg::Hdroutetoglobal msg_;
};

class Init_Hdroutetoglobal_map
{
public:
  Init_Hdroutetoglobal_map()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Hdroutetoglobal_isvalid map(::common_msgs_humble::msg::Hdroutetoglobal::_map_type arg)
  {
    msg_.map = std::move(arg);
    return Init_Hdroutetoglobal_isvalid(msg_);
  }

private:
  ::common_msgs_humble::msg::Hdroutetoglobal msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Hdroutetoglobal>()
{
  return common_msgs_humble::msg::builder::Init_Hdroutetoglobal_map();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__HDROUTETOGLOBAL__BUILDER_HPP_
