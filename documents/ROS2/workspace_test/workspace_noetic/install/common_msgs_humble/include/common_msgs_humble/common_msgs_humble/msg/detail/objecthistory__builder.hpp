// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Objecthistory.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__OBJECTHISTORY__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__OBJECTHISTORY__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/objecthistory__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Objecthistory_speedl
{
public:
  explicit Init_Objecthistory_speedl(::common_msgs_humble::msg::Objecthistory & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Objecthistory speedl(::common_msgs_humble::msg::Objecthistory::_speedl_type arg)
  {
    msg_.speedl = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Objecthistory msg_;
};

class Init_Objecthistory_speeds
{
public:
  explicit Init_Objecthistory_speeds(::common_msgs_humble::msg::Objecthistory & msg)
  : msg_(msg)
  {}
  Init_Objecthistory_speedl speeds(::common_msgs_humble::msg::Objecthistory::_speeds_type arg)
  {
    msg_.speeds = std::move(arg);
    return Init_Objecthistory_speedl(msg_);
  }

private:
  ::common_msgs_humble::msg::Objecthistory msg_;
};

class Init_Objecthistory_l
{
public:
  explicit Init_Objecthistory_l(::common_msgs_humble::msg::Objecthistory & msg)
  : msg_(msg)
  {}
  Init_Objecthistory_speeds l(::common_msgs_humble::msg::Objecthistory::_l_type arg)
  {
    msg_.l = std::move(arg);
    return Init_Objecthistory_speeds(msg_);
  }

private:
  ::common_msgs_humble::msg::Objecthistory msg_;
};

class Init_Objecthistory_s
{
public:
  explicit Init_Objecthistory_s(::common_msgs_humble::msg::Objecthistory & msg)
  : msg_(msg)
  {}
  Init_Objecthistory_l s(::common_msgs_humble::msg::Objecthistory::_s_type arg)
  {
    msg_.s = std::move(arg);
    return Init_Objecthistory_l(msg_);
  }

private:
  ::common_msgs_humble::msg::Objecthistory msg_;
};

class Init_Objecthistory_absvy
{
public:
  explicit Init_Objecthistory_absvy(::common_msgs_humble::msg::Objecthistory & msg)
  : msg_(msg)
  {}
  Init_Objecthistory_s absvy(::common_msgs_humble::msg::Objecthistory::_absvy_type arg)
  {
    msg_.absvy = std::move(arg);
    return Init_Objecthistory_s(msg_);
  }

private:
  ::common_msgs_humble::msg::Objecthistory msg_;
};

class Init_Objecthistory_absvx
{
public:
  explicit Init_Objecthistory_absvx(::common_msgs_humble::msg::Objecthistory & msg)
  : msg_(msg)
  {}
  Init_Objecthistory_absvy absvx(::common_msgs_humble::msg::Objecthistory::_absvx_type arg)
  {
    msg_.absvx = std::move(arg);
    return Init_Objecthistory_absvy(msg_);
  }

private:
  ::common_msgs_humble::msg::Objecthistory msg_;
};

class Init_Objecthistory_relavy
{
public:
  explicit Init_Objecthistory_relavy(::common_msgs_humble::msg::Objecthistory & msg)
  : msg_(msg)
  {}
  Init_Objecthistory_absvx relavy(::common_msgs_humble::msg::Objecthistory::_relavy_type arg)
  {
    msg_.relavy = std::move(arg);
    return Init_Objecthistory_absvx(msg_);
  }

private:
  ::common_msgs_humble::msg::Objecthistory msg_;
};

class Init_Objecthistory_relavx
{
public:
  explicit Init_Objecthistory_relavx(::common_msgs_humble::msg::Objecthistory & msg)
  : msg_(msg)
  {}
  Init_Objecthistory_relavy relavx(::common_msgs_humble::msg::Objecthistory::_relavx_type arg)
  {
    msg_.relavx = std::move(arg);
    return Init_Objecthistory_relavy(msg_);
  }

private:
  ::common_msgs_humble::msg::Objecthistory msg_;
};

class Init_Objecthistory_heading
{
public:
  explicit Init_Objecthistory_heading(::common_msgs_humble::msg::Objecthistory & msg)
  : msg_(msg)
  {}
  Init_Objecthistory_relavx heading(::common_msgs_humble::msg::Objecthistory::_heading_type arg)
  {
    msg_.heading = std::move(arg);
    return Init_Objecthistory_relavx(msg_);
  }

private:
  ::common_msgs_humble::msg::Objecthistory msg_;
};

class Init_Objecthistory_pitch
{
public:
  explicit Init_Objecthistory_pitch(::common_msgs_humble::msg::Objecthistory & msg)
  : msg_(msg)
  {}
  Init_Objecthistory_heading pitch(::common_msgs_humble::msg::Objecthistory::_pitch_type arg)
  {
    msg_.pitch = std::move(arg);
    return Init_Objecthistory_heading(msg_);
  }

private:
  ::common_msgs_humble::msg::Objecthistory msg_;
};

class Init_Objecthistory_roll
{
public:
  explicit Init_Objecthistory_roll(::common_msgs_humble::msg::Objecthistory & msg)
  : msg_(msg)
  {}
  Init_Objecthistory_pitch roll(::common_msgs_humble::msg::Objecthistory::_roll_type arg)
  {
    msg_.roll = std::move(arg);
    return Init_Objecthistory_pitch(msg_);
  }

private:
  ::common_msgs_humble::msg::Objecthistory msg_;
};

class Init_Objecthistory_alt
{
public:
  explicit Init_Objecthistory_alt(::common_msgs_humble::msg::Objecthistory & msg)
  : msg_(msg)
  {}
  Init_Objecthistory_roll alt(::common_msgs_humble::msg::Objecthistory::_alt_type arg)
  {
    msg_.alt = std::move(arg);
    return Init_Objecthistory_roll(msg_);
  }

private:
  ::common_msgs_humble::msg::Objecthistory msg_;
};

class Init_Objecthistory_lat
{
public:
  explicit Init_Objecthistory_lat(::common_msgs_humble::msg::Objecthistory & msg)
  : msg_(msg)
  {}
  Init_Objecthistory_alt lat(::common_msgs_humble::msg::Objecthistory::_lat_type arg)
  {
    msg_.lat = std::move(arg);
    return Init_Objecthistory_alt(msg_);
  }

private:
  ::common_msgs_humble::msg::Objecthistory msg_;
};

class Init_Objecthistory_lon
{
public:
  explicit Init_Objecthistory_lon(::common_msgs_humble::msg::Objecthistory & msg)
  : msg_(msg)
  {}
  Init_Objecthistory_lat lon(::common_msgs_humble::msg::Objecthistory::_lon_type arg)
  {
    msg_.lon = std::move(arg);
    return Init_Objecthistory_lat(msg_);
  }

private:
  ::common_msgs_humble::msg::Objecthistory msg_;
};

class Init_Objecthistory_trajectorypoint
{
public:
  explicit Init_Objecthistory_trajectorypoint(::common_msgs_humble::msg::Objecthistory & msg)
  : msg_(msg)
  {}
  Init_Objecthistory_lon trajectorypoint(::common_msgs_humble::msg::Objecthistory::_trajectorypoint_type arg)
  {
    msg_.trajectorypoint = std::move(arg);
    return Init_Objecthistory_lon(msg_);
  }

private:
  ::common_msgs_humble::msg::Objecthistory msg_;
};

class Init_Objecthistory_timestamp
{
public:
  Init_Objecthistory_timestamp()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Objecthistory_trajectorypoint timestamp(::common_msgs_humble::msg::Objecthistory::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return Init_Objecthistory_trajectorypoint(msg_);
  }

private:
  ::common_msgs_humble::msg::Objecthistory msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Objecthistory>()
{
  return common_msgs_humble::msg::builder::Init_Objecthistory_timestamp();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__OBJECTHISTORY__BUILDER_HPP_
