// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Obutrafficlights.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUTRAFFICLIGHTS__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUTRAFFICLIGHTS__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'light'
#include "common_msgs_humble/msg/detail/obulight__struct.h"

/// Struct defined in msg/Obutrafficlights in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Obutrafficlights
{
  int64_t timestamp;
  uint32_t region_id;
  uint32_t node_id;
  uint8_t light_status;
  uint8_t phase_cnt;
  common_msgs_humble__msg__Obulight__Sequence light;
} common_msgs_humble__msg__Obutrafficlights;

// Struct for a sequence of common_msgs_humble__msg__Obutrafficlights.
typedef struct common_msgs_humble__msg__Obutrafficlights__Sequence
{
  common_msgs_humble__msg__Obutrafficlights * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Obutrafficlights__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUTRAFFICLIGHTS__STRUCT_H_
