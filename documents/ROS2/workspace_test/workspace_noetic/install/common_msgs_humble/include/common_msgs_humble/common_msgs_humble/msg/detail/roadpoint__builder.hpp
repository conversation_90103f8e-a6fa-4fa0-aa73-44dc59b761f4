// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Roadpoint.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__ROADPOINT__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__ROADPOINT__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/roadpoint__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Roadpoint_lanesite
{
public:
  explicit Init_Roadpoint_lanesite(::common_msgs_humble::msg::Roadpoint & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Roadpoint lanesite(::common_msgs_humble::msg::Roadpoint::_lanesite_type arg)
  {
    msg_.lanesite = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Roadpoint msg_;
};

class Init_Roadpoint_lanenum
{
public:
  explicit Init_Roadpoint_lanenum(::common_msgs_humble::msg::Roadpoint & msg)
  : msg_(msg)
  {}
  Init_Roadpoint_lanesite lanenum(::common_msgs_humble::msg::Roadpoint::_lanenum_type arg)
  {
    msg_.lanenum = std::move(arg);
    return Init_Roadpoint_lanesite(msg_);
  }

private:
  ::common_msgs_humble::msg::Roadpoint msg_;
};

class Init_Roadpoint_laneborrow
{
public:
  explicit Init_Roadpoint_laneborrow(::common_msgs_humble::msg::Roadpoint & msg)
  : msg_(msg)
  {}
  Init_Roadpoint_lanenum laneborrow(::common_msgs_humble::msg::Roadpoint::_laneborrow_type arg)
  {
    msg_.laneborrow = std::move(arg);
    return Init_Roadpoint_lanenum(msg_);
  }

private:
  ::common_msgs_humble::msg::Roadpoint msg_;
};

class Init_Roadpoint_laneswitch
{
public:
  explicit Init_Roadpoint_laneswitch(::common_msgs_humble::msg::Roadpoint & msg)
  : msg_(msg)
  {}
  Init_Roadpoint_laneborrow laneswitch(::common_msgs_humble::msg::Roadpoint::_laneswitch_type arg)
  {
    msg_.laneswitch = std::move(arg);
    return Init_Roadpoint_laneborrow(msg_);
  }

private:
  ::common_msgs_humble::msg::Roadpoint msg_;
};

class Init_Roadpoint_relativetime
{
public:
  explicit Init_Roadpoint_relativetime(::common_msgs_humble::msg::Roadpoint & msg)
  : msg_(msg)
  {}
  Init_Roadpoint_laneswitch relativetime(::common_msgs_humble::msg::Roadpoint::_relativetime_type arg)
  {
    msg_.relativetime = std::move(arg);
    return Init_Roadpoint_laneswitch(msg_);
  }

private:
  ::common_msgs_humble::msg::Roadpoint msg_;
};

class Init_Roadpoint_rightlanewidth
{
public:
  explicit Init_Roadpoint_rightlanewidth(::common_msgs_humble::msg::Roadpoint & msg)
  : msg_(msg)
  {}
  Init_Roadpoint_relativetime rightlanewidth(::common_msgs_humble::msg::Roadpoint::_rightlanewidth_type arg)
  {
    msg_.rightlanewidth = std::move(arg);
    return Init_Roadpoint_relativetime(msg_);
  }

private:
  ::common_msgs_humble::msg::Roadpoint msg_;
};

class Init_Roadpoint_leftlanewidth
{
public:
  explicit Init_Roadpoint_leftlanewidth(::common_msgs_humble::msg::Roadpoint & msg)
  : msg_(msg)
  {}
  Init_Roadpoint_rightlanewidth leftlanewidth(::common_msgs_humble::msg::Roadpoint::_leftlanewidth_type arg)
  {
    msg_.leftlanewidth = std::move(arg);
    return Init_Roadpoint_rightlanewidth(msg_);
  }

private:
  ::common_msgs_humble::msg::Roadpoint msg_;
};

class Init_Roadpoint_lanewidth
{
public:
  explicit Init_Roadpoint_lanewidth(::common_msgs_humble::msg::Roadpoint & msg)
  : msg_(msg)
  {}
  Init_Roadpoint_leftlanewidth lanewidth(::common_msgs_humble::msg::Roadpoint::_lanewidth_type arg)
  {
    msg_.lanewidth = std::move(arg);
    return Init_Roadpoint_leftlanewidth(msg_);
  }

private:
  ::common_msgs_humble::msg::Roadpoint msg_;
};

class Init_Roadpoint_sideroadwidth
{
public:
  explicit Init_Roadpoint_sideroadwidth(::common_msgs_humble::msg::Roadpoint & msg)
  : msg_(msg)
  {}
  Init_Roadpoint_lanewidth sideroadwidth(::common_msgs_humble::msg::Roadpoint::_sideroadwidth_type arg)
  {
    msg_.sideroadwidth = std::move(arg);
    return Init_Roadpoint_lanewidth(msg_);
  }

private:
  ::common_msgs_humble::msg::Roadpoint msg_;
};

class Init_Roadpoint_s
{
public:
  explicit Init_Roadpoint_s(::common_msgs_humble::msg::Roadpoint & msg)
  : msg_(msg)
  {}
  Init_Roadpoint_sideroadwidth s(::common_msgs_humble::msg::Roadpoint::_s_type arg)
  {
    msg_.s = std::move(arg);
    return Init_Roadpoint_sideroadwidth(msg_);
  }

private:
  ::common_msgs_humble::msg::Roadpoint msg_;
};

class Init_Roadpoint_rightsearchdis
{
public:
  explicit Init_Roadpoint_rightsearchdis(::common_msgs_humble::msg::Roadpoint & msg)
  : msg_(msg)
  {}
  Init_Roadpoint_s rightsearchdis(::common_msgs_humble::msg::Roadpoint::_rightsearchdis_type arg)
  {
    msg_.rightsearchdis = std::move(arg);
    return Init_Roadpoint_s(msg_);
  }

private:
  ::common_msgs_humble::msg::Roadpoint msg_;
};

class Init_Roadpoint_leftsearchdis
{
public:
  explicit Init_Roadpoint_leftsearchdis(::common_msgs_humble::msg::Roadpoint & msg)
  : msg_(msg)
  {}
  Init_Roadpoint_rightsearchdis leftsearchdis(::common_msgs_humble::msg::Roadpoint::_leftsearchdis_type arg)
  {
    msg_.leftsearchdis = std::move(arg);
    return Init_Roadpoint_rightsearchdis(msg_);
  }

private:
  ::common_msgs_humble::msg::Roadpoint msg_;
};

class Init_Roadpoint_ddkappa
{
public:
  explicit Init_Roadpoint_ddkappa(::common_msgs_humble::msg::Roadpoint & msg)
  : msg_(msg)
  {}
  Init_Roadpoint_leftsearchdis ddkappa(::common_msgs_humble::msg::Roadpoint::_ddkappa_type arg)
  {
    msg_.ddkappa = std::move(arg);
    return Init_Roadpoint_leftsearchdis(msg_);
  }

private:
  ::common_msgs_humble::msg::Roadpoint msg_;
};

class Init_Roadpoint_dkappa
{
public:
  explicit Init_Roadpoint_dkappa(::common_msgs_humble::msg::Roadpoint & msg)
  : msg_(msg)
  {}
  Init_Roadpoint_ddkappa dkappa(::common_msgs_humble::msg::Roadpoint::_dkappa_type arg)
  {
    msg_.dkappa = std::move(arg);
    return Init_Roadpoint_ddkappa(msg_);
  }

private:
  ::common_msgs_humble::msg::Roadpoint msg_;
};

class Init_Roadpoint_curvature
{
public:
  explicit Init_Roadpoint_curvature(::common_msgs_humble::msg::Roadpoint & msg)
  : msg_(msg)
  {}
  Init_Roadpoint_dkappa curvature(::common_msgs_humble::msg::Roadpoint::_curvature_type arg)
  {
    msg_.curvature = std::move(arg);
    return Init_Roadpoint_dkappa(msg_);
  }

private:
  ::common_msgs_humble::msg::Roadpoint msg_;
};

class Init_Roadpoint_heading
{
public:
  explicit Init_Roadpoint_heading(::common_msgs_humble::msg::Roadpoint & msg)
  : msg_(msg)
  {}
  Init_Roadpoint_curvature heading(::common_msgs_humble::msg::Roadpoint::_heading_type arg)
  {
    msg_.heading = std::move(arg);
    return Init_Roadpoint_curvature(msg_);
  }

private:
  ::common_msgs_humble::msg::Roadpoint msg_;
};

class Init_Roadpoint_sensorlanetype
{
public:
  explicit Init_Roadpoint_sensorlanetype(::common_msgs_humble::msg::Roadpoint & msg)
  : msg_(msg)
  {}
  Init_Roadpoint_heading sensorlanetype(::common_msgs_humble::msg::Roadpoint::_sensorlanetype_type arg)
  {
    msg_.sensorlanetype = std::move(arg);
    return Init_Roadpoint_heading(msg_);
  }

private:
  ::common_msgs_humble::msg::Roadpoint msg_;
};

class Init_Roadpoint_mergelanetype
{
public:
  explicit Init_Roadpoint_mergelanetype(::common_msgs_humble::msg::Roadpoint & msg)
  : msg_(msg)
  {}
  Init_Roadpoint_sensorlanetype mergelanetype(::common_msgs_humble::msg::Roadpoint::_mergelanetype_type arg)
  {
    msg_.mergelanetype = std::move(arg);
    return Init_Roadpoint_sensorlanetype(msg_);
  }

private:
  ::common_msgs_humble::msg::Roadpoint msg_;
};

class Init_Roadpoint_turnlight
{
public:
  explicit Init_Roadpoint_turnlight(::common_msgs_humble::msg::Roadpoint & msg)
  : msg_(msg)
  {}
  Init_Roadpoint_mergelanetype turnlight(::common_msgs_humble::msg::Roadpoint::_turnlight_type arg)
  {
    msg_.turnlight = std::move(arg);
    return Init_Roadpoint_mergelanetype(msg_);
  }

private:
  ::common_msgs_humble::msg::Roadpoint msg_;
};

class Init_Roadpoint_lanetype
{
public:
  explicit Init_Roadpoint_lanetype(::common_msgs_humble::msg::Roadpoint & msg)
  : msg_(msg)
  {}
  Init_Roadpoint_turnlight lanetype(::common_msgs_humble::msg::Roadpoint::_lanetype_type arg)
  {
    msg_.lanetype = std::move(arg);
    return Init_Roadpoint_turnlight(msg_);
  }

private:
  ::common_msgs_humble::msg::Roadpoint msg_;
};

class Init_Roadpoint_jerk
{
public:
  explicit Init_Roadpoint_jerk(::common_msgs_humble::msg::Roadpoint & msg)
  : msg_(msg)
  {}
  Init_Roadpoint_lanetype jerk(::common_msgs_humble::msg::Roadpoint::_jerk_type arg)
  {
    msg_.jerk = std::move(arg);
    return Init_Roadpoint_lanetype(msg_);
  }

private:
  ::common_msgs_humble::msg::Roadpoint msg_;
};

class Init_Roadpoint_a
{
public:
  explicit Init_Roadpoint_a(::common_msgs_humble::msg::Roadpoint & msg)
  : msg_(msg)
  {}
  Init_Roadpoint_jerk a(::common_msgs_humble::msg::Roadpoint::_a_type arg)
  {
    msg_.a = std::move(arg);
    return Init_Roadpoint_jerk(msg_);
  }

private:
  ::common_msgs_humble::msg::Roadpoint msg_;
};

class Init_Roadpoint_speed
{
public:
  explicit Init_Roadpoint_speed(::common_msgs_humble::msg::Roadpoint & msg)
  : msg_(msg)
  {}
  Init_Roadpoint_a speed(::common_msgs_humble::msg::Roadpoint::_speed_type arg)
  {
    msg_.speed = std::move(arg);
    return Init_Roadpoint_a(msg_);
  }

private:
  ::common_msgs_humble::msg::Roadpoint msg_;
};

class Init_Roadpoint_roadtype
{
public:
  explicit Init_Roadpoint_roadtype(::common_msgs_humble::msg::Roadpoint & msg)
  : msg_(msg)
  {}
  Init_Roadpoint_speed roadtype(::common_msgs_humble::msg::Roadpoint::_roadtype_type arg)
  {
    msg_.roadtype = std::move(arg);
    return Init_Roadpoint_speed(msg_);
  }

private:
  ::common_msgs_humble::msg::Roadpoint msg_;
};

class Init_Roadpoint_gy
{
public:
  explicit Init_Roadpoint_gy(::common_msgs_humble::msg::Roadpoint & msg)
  : msg_(msg)
  {}
  Init_Roadpoint_roadtype gy(::common_msgs_humble::msg::Roadpoint::_gy_type arg)
  {
    msg_.gy = std::move(arg);
    return Init_Roadpoint_roadtype(msg_);
  }

private:
  ::common_msgs_humble::msg::Roadpoint msg_;
};

class Init_Roadpoint_gx
{
public:
  explicit Init_Roadpoint_gx(::common_msgs_humble::msg::Roadpoint & msg)
  : msg_(msg)
  {}
  Init_Roadpoint_gy gx(::common_msgs_humble::msg::Roadpoint::_gx_type arg)
  {
    msg_.gx = std::move(arg);
    return Init_Roadpoint_gy(msg_);
  }

private:
  ::common_msgs_humble::msg::Roadpoint msg_;
};

class Init_Roadpoint_y
{
public:
  explicit Init_Roadpoint_y(::common_msgs_humble::msg::Roadpoint & msg)
  : msg_(msg)
  {}
  Init_Roadpoint_gx y(::common_msgs_humble::msg::Roadpoint::_y_type arg)
  {
    msg_.y = std::move(arg);
    return Init_Roadpoint_gx(msg_);
  }

private:
  ::common_msgs_humble::msg::Roadpoint msg_;
};

class Init_Roadpoint_x
{
public:
  Init_Roadpoint_x()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Roadpoint_y x(::common_msgs_humble::msg::Roadpoint::_x_type arg)
  {
    msg_.x = std::move(arg);
    return Init_Roadpoint_y(msg_);
  }

private:
  ::common_msgs_humble::msg::Roadpoint msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Roadpoint>()
{
  return common_msgs_humble::msg::builder::Init_Roadpoint_x();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__ROADPOINT__BUILDER_HPP_
