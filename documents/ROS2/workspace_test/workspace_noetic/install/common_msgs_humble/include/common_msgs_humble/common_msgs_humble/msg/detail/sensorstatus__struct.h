﻿// NOLINT: This file starts with a BOM since it contain non-ASCII characters
// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Sensorstatus.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSORSTATUS__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSORSTATUS__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

/// Struct defined in msg/Sensorstatus in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Sensorstatus
{
  uint8_t state;
  /// 时间戳
  int64_t timestamp;
} common_msgs_humble__msg__Sensorstatus;

// Struct for a sequence of common_msgs_humble__msg__Sensorstatus.
typedef struct common_msgs_humble__msg__Sensorstatus__Sequence
{
  common_msgs_humble__msg__Sensorstatus * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Sensorstatus__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSORSTATUS__STRUCT_H_
