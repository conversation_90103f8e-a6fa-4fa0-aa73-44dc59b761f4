// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Hdstoppointstoglobal.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__HDSTOPPOINTSTOGLOBAL__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__HDSTOPPOINTSTOGLOBAL__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'points'
#include "common_msgs_humble/msg/detail/pointformat__struct.h"

/// Struct defined in msg/Hdstoppointstoglobal in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Hdstoppointstoglobal
{
  common_msgs_humble__msg__Pointformat__Sequence points;
  uint8_t isvalid;
  int64_t timestamp;
} common_msgs_humble__msg__Hdstoppointstoglobal;

// Struct for a sequence of common_msgs_humble__msg__Hdstoppointstoglobal.
typedef struct common_msgs_humble__msg__Hdstoppointstoglobal__Sequence
{
  common_msgs_humble__msg__Hdstoppointstoglobal * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Hdstoppointstoglobal__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__HDSTOPPOINTSTOGLOBAL__STRUCT_H_
