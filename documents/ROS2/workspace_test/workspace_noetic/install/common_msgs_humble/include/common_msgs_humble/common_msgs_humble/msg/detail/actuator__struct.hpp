// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Actuator.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__ACTUATOR__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__ACTUATOR__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Actuator __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Actuator __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Actuator_
{
  using Type = Actuator_<ContainerAllocator>;

  explicit Actuator_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->epsmethod = 0;
      this->epsangle = 0;
      this->espmethod = 0;
      this->escbrakepress = 0.0f;
      this->gaspedal = 0;
      this->sysstatus = 0;
      this->speed = 0.0f;
      this->lights = 0;
      this->turnlight = 0;
      this->gear = 0;
      this->epb = 0;
      this->door = 0;
      this->isvalid = 0;
      this->timestamp = 0ll;
      this->sendsuccess = 0;
      this->brakepedal = 0;
      this->warning = 0;
      this->error = 0;
      this->battery = 0;
      this->controlover = 0;
      this->steerspeed = 0l;
      this->accelpos = 0l;
      this->breakflag = 0l;
      this->breakpos = 0l;
      this->yaw = 0l;
      this->mil = 0l;
      this->soc = 0.0f;
      this->batvol = 0.0f;
      this->acc = 0l;
      this->oilperhour = 0l;
      this->oilhundredkmconsume = 0l;
      this->oilconsume = 0l;
      this->autoctrlsig = 0l;
      this->totalvoltage = 0.0f;
      this->totalcurrent = 0.0f;
      this->motorspeed = 0l;
      this->motortorque = 0l;
      this->wirecontrolstatus = 0;
      this->blinkerstatus = 0;
      this->accx = 0.0f;
      this->gaspedalcar = 0.0f;
    }
  }

  explicit Actuator_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->epsmethod = 0;
      this->epsangle = 0;
      this->espmethod = 0;
      this->escbrakepress = 0.0f;
      this->gaspedal = 0;
      this->sysstatus = 0;
      this->speed = 0.0f;
      this->lights = 0;
      this->turnlight = 0;
      this->gear = 0;
      this->epb = 0;
      this->door = 0;
      this->isvalid = 0;
      this->timestamp = 0ll;
      this->sendsuccess = 0;
      this->brakepedal = 0;
      this->warning = 0;
      this->error = 0;
      this->battery = 0;
      this->controlover = 0;
      this->steerspeed = 0l;
      this->accelpos = 0l;
      this->breakflag = 0l;
      this->breakpos = 0l;
      this->yaw = 0l;
      this->mil = 0l;
      this->soc = 0.0f;
      this->batvol = 0.0f;
      this->acc = 0l;
      this->oilperhour = 0l;
      this->oilhundredkmconsume = 0l;
      this->oilconsume = 0l;
      this->autoctrlsig = 0l;
      this->totalvoltage = 0.0f;
      this->totalcurrent = 0.0f;
      this->motorspeed = 0l;
      this->motortorque = 0l;
      this->wirecontrolstatus = 0;
      this->blinkerstatus = 0;
      this->accx = 0.0f;
      this->gaspedalcar = 0.0f;
    }
  }

  // field types and members
  using _epsmethod_type =
    uint8_t;
  _epsmethod_type epsmethod;
  using _epsangle_type =
    int16_t;
  _epsangle_type epsangle;
  using _espmethod_type =
    uint8_t;
  _espmethod_type espmethod;
  using _escbrakepress_type =
    float;
  _escbrakepress_type escbrakepress;
  using _gaspedal_type =
    uint8_t;
  _gaspedal_type gaspedal;
  using _sysstatus_type =
    uint8_t;
  _sysstatus_type sysstatus;
  using _speed_type =
    float;
  _speed_type speed;
  using _lights_type =
    uint8_t;
  _lights_type lights;
  using _turnlight_type =
    uint8_t;
  _turnlight_type turnlight;
  using _gear_type =
    uint8_t;
  _gear_type gear;
  using _epb_type =
    uint8_t;
  _epb_type epb;
  using _door_type =
    uint8_t;
  _door_type door;
  using _isvalid_type =
    uint8_t;
  _isvalid_type isvalid;
  using _timestamp_type =
    int64_t;
  _timestamp_type timestamp;
  using _sendsuccess_type =
    uint8_t;
  _sendsuccess_type sendsuccess;
  using _brakepedal_type =
    uint8_t;
  _brakepedal_type brakepedal;
  using _warning_type =
    uint8_t;
  _warning_type warning;
  using _error_type =
    int16_t;
  _error_type error;
  using _battery_type =
    uint8_t;
  _battery_type battery;
  using _controlover_type =
    uint8_t;
  _controlover_type controlover;
  using _steerspeed_type =
    int32_t;
  _steerspeed_type steerspeed;
  using _accelpos_type =
    int32_t;
  _accelpos_type accelpos;
  using _breakflag_type =
    int32_t;
  _breakflag_type breakflag;
  using _breakpos_type =
    int32_t;
  _breakpos_type breakpos;
  using _yaw_type =
    int32_t;
  _yaw_type yaw;
  using _mil_type =
    int32_t;
  _mil_type mil;
  using _soc_type =
    float;
  _soc_type soc;
  using _batvol_type =
    float;
  _batvol_type batvol;
  using _acc_type =
    int32_t;
  _acc_type acc;
  using _oilperhour_type =
    int32_t;
  _oilperhour_type oilperhour;
  using _oilhundredkmconsume_type =
    int32_t;
  _oilhundredkmconsume_type oilhundredkmconsume;
  using _oilconsume_type =
    int32_t;
  _oilconsume_type oilconsume;
  using _autoctrlsig_type =
    int32_t;
  _autoctrlsig_type autoctrlsig;
  using _totalvoltage_type =
    float;
  _totalvoltage_type totalvoltage;
  using _totalcurrent_type =
    float;
  _totalcurrent_type totalcurrent;
  using _motorspeed_type =
    int32_t;
  _motorspeed_type motorspeed;
  using _motortorque_type =
    int32_t;
  _motortorque_type motortorque;
  using _wirecontrolstatus_type =
    uint8_t;
  _wirecontrolstatus_type wirecontrolstatus;
  using _blinkerstatus_type =
    uint8_t;
  _blinkerstatus_type blinkerstatus;
  using _accx_type =
    float;
  _accx_type accx;
  using _gaspedalcar_type =
    float;
  _gaspedalcar_type gaspedalcar;

  // setters for named parameter idiom
  Type & set__epsmethod(
    const uint8_t & _arg)
  {
    this->epsmethod = _arg;
    return *this;
  }
  Type & set__epsangle(
    const int16_t & _arg)
  {
    this->epsangle = _arg;
    return *this;
  }
  Type & set__espmethod(
    const uint8_t & _arg)
  {
    this->espmethod = _arg;
    return *this;
  }
  Type & set__escbrakepress(
    const float & _arg)
  {
    this->escbrakepress = _arg;
    return *this;
  }
  Type & set__gaspedal(
    const uint8_t & _arg)
  {
    this->gaspedal = _arg;
    return *this;
  }
  Type & set__sysstatus(
    const uint8_t & _arg)
  {
    this->sysstatus = _arg;
    return *this;
  }
  Type & set__speed(
    const float & _arg)
  {
    this->speed = _arg;
    return *this;
  }
  Type & set__lights(
    const uint8_t & _arg)
  {
    this->lights = _arg;
    return *this;
  }
  Type & set__turnlight(
    const uint8_t & _arg)
  {
    this->turnlight = _arg;
    return *this;
  }
  Type & set__gear(
    const uint8_t & _arg)
  {
    this->gear = _arg;
    return *this;
  }
  Type & set__epb(
    const uint8_t & _arg)
  {
    this->epb = _arg;
    return *this;
  }
  Type & set__door(
    const uint8_t & _arg)
  {
    this->door = _arg;
    return *this;
  }
  Type & set__isvalid(
    const uint8_t & _arg)
  {
    this->isvalid = _arg;
    return *this;
  }
  Type & set__timestamp(
    const int64_t & _arg)
  {
    this->timestamp = _arg;
    return *this;
  }
  Type & set__sendsuccess(
    const uint8_t & _arg)
  {
    this->sendsuccess = _arg;
    return *this;
  }
  Type & set__brakepedal(
    const uint8_t & _arg)
  {
    this->brakepedal = _arg;
    return *this;
  }
  Type & set__warning(
    const uint8_t & _arg)
  {
    this->warning = _arg;
    return *this;
  }
  Type & set__error(
    const int16_t & _arg)
  {
    this->error = _arg;
    return *this;
  }
  Type & set__battery(
    const uint8_t & _arg)
  {
    this->battery = _arg;
    return *this;
  }
  Type & set__controlover(
    const uint8_t & _arg)
  {
    this->controlover = _arg;
    return *this;
  }
  Type & set__steerspeed(
    const int32_t & _arg)
  {
    this->steerspeed = _arg;
    return *this;
  }
  Type & set__accelpos(
    const int32_t & _arg)
  {
    this->accelpos = _arg;
    return *this;
  }
  Type & set__breakflag(
    const int32_t & _arg)
  {
    this->breakflag = _arg;
    return *this;
  }
  Type & set__breakpos(
    const int32_t & _arg)
  {
    this->breakpos = _arg;
    return *this;
  }
  Type & set__yaw(
    const int32_t & _arg)
  {
    this->yaw = _arg;
    return *this;
  }
  Type & set__mil(
    const int32_t & _arg)
  {
    this->mil = _arg;
    return *this;
  }
  Type & set__soc(
    const float & _arg)
  {
    this->soc = _arg;
    return *this;
  }
  Type & set__batvol(
    const float & _arg)
  {
    this->batvol = _arg;
    return *this;
  }
  Type & set__acc(
    const int32_t & _arg)
  {
    this->acc = _arg;
    return *this;
  }
  Type & set__oilperhour(
    const int32_t & _arg)
  {
    this->oilperhour = _arg;
    return *this;
  }
  Type & set__oilhundredkmconsume(
    const int32_t & _arg)
  {
    this->oilhundredkmconsume = _arg;
    return *this;
  }
  Type & set__oilconsume(
    const int32_t & _arg)
  {
    this->oilconsume = _arg;
    return *this;
  }
  Type & set__autoctrlsig(
    const int32_t & _arg)
  {
    this->autoctrlsig = _arg;
    return *this;
  }
  Type & set__totalvoltage(
    const float & _arg)
  {
    this->totalvoltage = _arg;
    return *this;
  }
  Type & set__totalcurrent(
    const float & _arg)
  {
    this->totalcurrent = _arg;
    return *this;
  }
  Type & set__motorspeed(
    const int32_t & _arg)
  {
    this->motorspeed = _arg;
    return *this;
  }
  Type & set__motortorque(
    const int32_t & _arg)
  {
    this->motortorque = _arg;
    return *this;
  }
  Type & set__wirecontrolstatus(
    const uint8_t & _arg)
  {
    this->wirecontrolstatus = _arg;
    return *this;
  }
  Type & set__blinkerstatus(
    const uint8_t & _arg)
  {
    this->blinkerstatus = _arg;
    return *this;
  }
  Type & set__accx(
    const float & _arg)
  {
    this->accx = _arg;
    return *this;
  }
  Type & set__gaspedalcar(
    const float & _arg)
  {
    this->gaspedalcar = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Actuator_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Actuator_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Actuator_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Actuator_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Actuator_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Actuator_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Actuator_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Actuator_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Actuator_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Actuator_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Actuator
    std::shared_ptr<common_msgs_humble::msg::Actuator_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Actuator
    std::shared_ptr<common_msgs_humble::msg::Actuator_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Actuator_ & other) const
  {
    if (this->epsmethod != other.epsmethod) {
      return false;
    }
    if (this->epsangle != other.epsangle) {
      return false;
    }
    if (this->espmethod != other.espmethod) {
      return false;
    }
    if (this->escbrakepress != other.escbrakepress) {
      return false;
    }
    if (this->gaspedal != other.gaspedal) {
      return false;
    }
    if (this->sysstatus != other.sysstatus) {
      return false;
    }
    if (this->speed != other.speed) {
      return false;
    }
    if (this->lights != other.lights) {
      return false;
    }
    if (this->turnlight != other.turnlight) {
      return false;
    }
    if (this->gear != other.gear) {
      return false;
    }
    if (this->epb != other.epb) {
      return false;
    }
    if (this->door != other.door) {
      return false;
    }
    if (this->isvalid != other.isvalid) {
      return false;
    }
    if (this->timestamp != other.timestamp) {
      return false;
    }
    if (this->sendsuccess != other.sendsuccess) {
      return false;
    }
    if (this->brakepedal != other.brakepedal) {
      return false;
    }
    if (this->warning != other.warning) {
      return false;
    }
    if (this->error != other.error) {
      return false;
    }
    if (this->battery != other.battery) {
      return false;
    }
    if (this->controlover != other.controlover) {
      return false;
    }
    if (this->steerspeed != other.steerspeed) {
      return false;
    }
    if (this->accelpos != other.accelpos) {
      return false;
    }
    if (this->breakflag != other.breakflag) {
      return false;
    }
    if (this->breakpos != other.breakpos) {
      return false;
    }
    if (this->yaw != other.yaw) {
      return false;
    }
    if (this->mil != other.mil) {
      return false;
    }
    if (this->soc != other.soc) {
      return false;
    }
    if (this->batvol != other.batvol) {
      return false;
    }
    if (this->acc != other.acc) {
      return false;
    }
    if (this->oilperhour != other.oilperhour) {
      return false;
    }
    if (this->oilhundredkmconsume != other.oilhundredkmconsume) {
      return false;
    }
    if (this->oilconsume != other.oilconsume) {
      return false;
    }
    if (this->autoctrlsig != other.autoctrlsig) {
      return false;
    }
    if (this->totalvoltage != other.totalvoltage) {
      return false;
    }
    if (this->totalcurrent != other.totalcurrent) {
      return false;
    }
    if (this->motorspeed != other.motorspeed) {
      return false;
    }
    if (this->motortorque != other.motortorque) {
      return false;
    }
    if (this->wirecontrolstatus != other.wirecontrolstatus) {
      return false;
    }
    if (this->blinkerstatus != other.blinkerstatus) {
      return false;
    }
    if (this->accx != other.accx) {
      return false;
    }
    if (this->gaspedalcar != other.gaspedalcar) {
      return false;
    }
    return true;
  }
  bool operator!=(const Actuator_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Actuator_

// alias to use template instance with default allocator
using Actuator =
  common_msgs_humble::msg::Actuator_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__ACTUATOR__STRUCT_HPP_
