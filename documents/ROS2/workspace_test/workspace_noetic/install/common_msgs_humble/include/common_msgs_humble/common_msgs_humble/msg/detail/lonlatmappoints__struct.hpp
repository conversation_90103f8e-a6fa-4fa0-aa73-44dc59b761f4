// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Lonlatmappoints.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__LONLATMAPPOINTS__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__LONLATMAPPOINTS__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'points'
#include "common_msgs_humble/msg/detail/lonlat__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Lonlatmappoints __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Lonlatmappoints __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Lonlatmappoints_
{
  using Type = Lonlatmappoints_<ContainerAllocator>;

  explicit Lonlatmappoints_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->mapname = "";
      this->zonename = "";
      this->timestamp = 0ll;
    }
  }

  explicit Lonlatmappoints_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : mapname(_alloc),
    zonename(_alloc)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->mapname = "";
      this->zonename = "";
      this->timestamp = 0ll;
    }
  }

  // field types and members
  using _mapname_type =
    std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>;
  _mapname_type mapname;
  using _zonename_type =
    std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>;
  _zonename_type zonename;
  using _points_type =
    std::vector<common_msgs_humble::msg::Lonlat_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<common_msgs_humble::msg::Lonlat_<ContainerAllocator>>>;
  _points_type points;
  using _timestamp_type =
    int64_t;
  _timestamp_type timestamp;

  // setters for named parameter idiom
  Type & set__mapname(
    const std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> & _arg)
  {
    this->mapname = _arg;
    return *this;
  }
  Type & set__zonename(
    const std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> & _arg)
  {
    this->zonename = _arg;
    return *this;
  }
  Type & set__points(
    const std::vector<common_msgs_humble::msg::Lonlat_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<common_msgs_humble::msg::Lonlat_<ContainerAllocator>>> & _arg)
  {
    this->points = _arg;
    return *this;
  }
  Type & set__timestamp(
    const int64_t & _arg)
  {
    this->timestamp = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Lonlatmappoints_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Lonlatmappoints_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Lonlatmappoints_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Lonlatmappoints_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Lonlatmappoints_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Lonlatmappoints_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Lonlatmappoints_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Lonlatmappoints_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Lonlatmappoints_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Lonlatmappoints_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Lonlatmappoints
    std::shared_ptr<common_msgs_humble::msg::Lonlatmappoints_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Lonlatmappoints
    std::shared_ptr<common_msgs_humble::msg::Lonlatmappoints_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Lonlatmappoints_ & other) const
  {
    if (this->mapname != other.mapname) {
      return false;
    }
    if (this->zonename != other.zonename) {
      return false;
    }
    if (this->points != other.points) {
      return false;
    }
    if (this->timestamp != other.timestamp) {
      return false;
    }
    return true;
  }
  bool operator!=(const Lonlatmappoints_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Lonlatmappoints_

// alias to use template instance with default allocator
using Lonlatmappoints =
  common_msgs_humble::msg::Lonlatmappoints_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__LONLATMAPPOINTS__STRUCT_HPP_
