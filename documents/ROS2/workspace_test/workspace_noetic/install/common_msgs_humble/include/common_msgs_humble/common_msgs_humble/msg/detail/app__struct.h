﻿// NOLINT: This file starts with a BOM since it contain non-ASCII characters
// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/App.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__APP__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__APP__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

/// Struct defined in msg/App in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__App
{
  uint8_t stopgo;
  uint8_t zonename;
  uint8_t apsnum;
  uint8_t estop;
  uint8_t park;
  /// 时间戳  123
  int64_t timestamp;
} common_msgs_humble__msg__App;

// Struct for a sequence of common_msgs_humble__msg__App.
typedef struct common_msgs_humble__msg__App__Sequence
{
  common_msgs_humble__msg__App * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__App__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__APP__STRUCT_H_
