// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Hdmap.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__HDMAP__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__HDMAP__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/hdmap__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Hdmap_index
{
public:
  explicit Init_Hdmap_index(::common_msgs_humble::msg::Hdmap & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Hdmap index(::common_msgs_humble::msg::Hdmap::_index_type arg)
  {
    msg_.index = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Hdmap msg_;
};

class Init_Hdmap_timestamp
{
public:
  explicit Init_Hdmap_timestamp(::common_msgs_humble::msg::Hdmap & msg)
  : msg_(msg)
  {}
  Init_Hdmap_index timestamp(::common_msgs_humble::msg::Hdmap::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return Init_Hdmap_index(msg_);
  }

private:
  ::common_msgs_humble::msg::Hdmap msg_;
};

class Init_Hdmap_isvalid
{
public:
  explicit Init_Hdmap_isvalid(::common_msgs_humble::msg::Hdmap & msg)
  : msg_(msg)
  {}
  Init_Hdmap_timestamp isvalid(::common_msgs_humble::msg::Hdmap::_isvalid_type arg)
  {
    msg_.isvalid = std::move(arg);
    return Init_Hdmap_timestamp(msg_);
  }

private:
  ::common_msgs_humble::msg::Hdmap msg_;
};

class Init_Hdmap_point
{
public:
  Init_Hdmap_point()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Hdmap_isvalid point(::common_msgs_humble::msg::Hdmap::_point_type arg)
  {
    msg_.point = std::move(arg);
    return Init_Hdmap_isvalid(msg_);
  }

private:
  ::common_msgs_humble::msg::Hdmap msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Hdmap>()
{
  return common_msgs_humble::msg::builder::Init_Hdmap_point();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__HDMAP__BUILDER_HPP_
