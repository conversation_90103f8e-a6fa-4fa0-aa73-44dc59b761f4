// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from common_msgs_humble:msg/Controllon.idl
// generated code does not contain a copyright notice
#include "common_msgs_humble/msg/detail/controllon__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


bool
common_msgs_humble__msg__Controllon__init(common_msgs_humble__msg__Controllon * msg)
{
  if (!msg) {
    return false;
  }
  // espmethod
  // gpsdis
  // epbmethod
  // epb
  // geermethod
  // gear
  // brakemethod
  // brakepedal
  // gaspedal
  // station
  // light
  // pcmethod
  // objdis
  // objrel
  // mode
  // isvalid
  // timestamp
  // targetspeed
  // apadis
  // objtype
  return true;
}

void
common_msgs_humble__msg__Controllon__fini(common_msgs_humble__msg__Controllon * msg)
{
  if (!msg) {
    return;
  }
  // espmethod
  // gpsdis
  // epbmethod
  // epb
  // geermethod
  // gear
  // brakemethod
  // brakepedal
  // gaspedal
  // station
  // light
  // pcmethod
  // objdis
  // objrel
  // mode
  // isvalid
  // timestamp
  // targetspeed
  // apadis
  // objtype
}

bool
common_msgs_humble__msg__Controllon__are_equal(const common_msgs_humble__msg__Controllon * lhs, const common_msgs_humble__msg__Controllon * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // espmethod
  if (lhs->espmethod != rhs->espmethod) {
    return false;
  }
  // gpsdis
  if (lhs->gpsdis != rhs->gpsdis) {
    return false;
  }
  // epbmethod
  if (lhs->epbmethod != rhs->epbmethod) {
    return false;
  }
  // epb
  if (lhs->epb != rhs->epb) {
    return false;
  }
  // geermethod
  if (lhs->geermethod != rhs->geermethod) {
    return false;
  }
  // gear
  if (lhs->gear != rhs->gear) {
    return false;
  }
  // brakemethod
  if (lhs->brakemethod != rhs->brakemethod) {
    return false;
  }
  // brakepedal
  if (lhs->brakepedal != rhs->brakepedal) {
    return false;
  }
  // gaspedal
  if (lhs->gaspedal != rhs->gaspedal) {
    return false;
  }
  // station
  if (lhs->station != rhs->station) {
    return false;
  }
  // light
  if (lhs->light != rhs->light) {
    return false;
  }
  // pcmethod
  if (lhs->pcmethod != rhs->pcmethod) {
    return false;
  }
  // objdis
  if (lhs->objdis != rhs->objdis) {
    return false;
  }
  // objrel
  if (lhs->objrel != rhs->objrel) {
    return false;
  }
  // mode
  if (lhs->mode != rhs->mode) {
    return false;
  }
  // isvalid
  if (lhs->isvalid != rhs->isvalid) {
    return false;
  }
  // timestamp
  if (lhs->timestamp != rhs->timestamp) {
    return false;
  }
  // targetspeed
  if (lhs->targetspeed != rhs->targetspeed) {
    return false;
  }
  // apadis
  if (lhs->apadis != rhs->apadis) {
    return false;
  }
  // objtype
  if (lhs->objtype != rhs->objtype) {
    return false;
  }
  return true;
}

bool
common_msgs_humble__msg__Controllon__copy(
  const common_msgs_humble__msg__Controllon * input,
  common_msgs_humble__msg__Controllon * output)
{
  if (!input || !output) {
    return false;
  }
  // espmethod
  output->espmethod = input->espmethod;
  // gpsdis
  output->gpsdis = input->gpsdis;
  // epbmethod
  output->epbmethod = input->epbmethod;
  // epb
  output->epb = input->epb;
  // geermethod
  output->geermethod = input->geermethod;
  // gear
  output->gear = input->gear;
  // brakemethod
  output->brakemethod = input->brakemethod;
  // brakepedal
  output->brakepedal = input->brakepedal;
  // gaspedal
  output->gaspedal = input->gaspedal;
  // station
  output->station = input->station;
  // light
  output->light = input->light;
  // pcmethod
  output->pcmethod = input->pcmethod;
  // objdis
  output->objdis = input->objdis;
  // objrel
  output->objrel = input->objrel;
  // mode
  output->mode = input->mode;
  // isvalid
  output->isvalid = input->isvalid;
  // timestamp
  output->timestamp = input->timestamp;
  // targetspeed
  output->targetspeed = input->targetspeed;
  // apadis
  output->apadis = input->apadis;
  // objtype
  output->objtype = input->objtype;
  return true;
}

common_msgs_humble__msg__Controllon *
common_msgs_humble__msg__Controllon__create()
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Controllon * msg = (common_msgs_humble__msg__Controllon *)allocator.allocate(sizeof(common_msgs_humble__msg__Controllon), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(common_msgs_humble__msg__Controllon));
  bool success = common_msgs_humble__msg__Controllon__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
common_msgs_humble__msg__Controllon__destroy(common_msgs_humble__msg__Controllon * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    common_msgs_humble__msg__Controllon__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
common_msgs_humble__msg__Controllon__Sequence__init(common_msgs_humble__msg__Controllon__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Controllon * data = NULL;

  if (size) {
    data = (common_msgs_humble__msg__Controllon *)allocator.zero_allocate(size, sizeof(common_msgs_humble__msg__Controllon), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = common_msgs_humble__msg__Controllon__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        common_msgs_humble__msg__Controllon__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
common_msgs_humble__msg__Controllon__Sequence__fini(common_msgs_humble__msg__Controllon__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      common_msgs_humble__msg__Controllon__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

common_msgs_humble__msg__Controllon__Sequence *
common_msgs_humble__msg__Controllon__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Controllon__Sequence * array = (common_msgs_humble__msg__Controllon__Sequence *)allocator.allocate(sizeof(common_msgs_humble__msg__Controllon__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = common_msgs_humble__msg__Controllon__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
common_msgs_humble__msg__Controllon__Sequence__destroy(common_msgs_humble__msg__Controllon__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    common_msgs_humble__msg__Controllon__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
common_msgs_humble__msg__Controllon__Sequence__are_equal(const common_msgs_humble__msg__Controllon__Sequence * lhs, const common_msgs_humble__msg__Controllon__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!common_msgs_humble__msg__Controllon__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
common_msgs_humble__msg__Controllon__Sequence__copy(
  const common_msgs_humble__msg__Controllon__Sequence * input,
  common_msgs_humble__msg__Controllon__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(common_msgs_humble__msg__Controllon);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    common_msgs_humble__msg__Controllon * data =
      (common_msgs_humble__msg__Controllon *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!common_msgs_humble__msg__Controllon__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          common_msgs_humble__msg__Controllon__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!common_msgs_humble__msg__Controllon__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
