﻿// NOLINT: This file starts with a BOM since it contain non-ASCII characters
// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Oburoadpoint.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUROADPOINT__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUROADPOINT__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

/// Struct defined in msg/Oburoadpoint in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Oburoadpoint
{
  /// 经度
  double lon;
  /// 纬度
  double lat;
  /// 速度 m/s
  double speed;
  /// 车辆加速度
  double accel;
  /// 航向角
  double heading;
  /// 有效性
  int32_t availability;
} common_msgs_humble__msg__Oburoadpoint;

// Struct for a sequence of common_msgs_humble__msg__Oburoadpoint.
typedef struct common_msgs_humble__msg__Oburoadpoint__Sequence
{
  common_msgs_humble__msg__Oburoadpoint * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Oburoadpoint__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUROADPOINT__STRUCT_H_
