﻿// NOLINT: This file starts with a BOM since it contain non-ASCII characters
// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Cloudpants.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__CLOUDPANTS__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__CLOUDPANTS__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'pants'
#include "common_msgs_humble/msg/detail/cloudpant__struct.h"

/// Struct defined in msg/Cloudpants in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Cloudpants
{
  /// 当前时间戳 ms 级
  int64_t timestamp;
  /// 仿真结果帧序号
  int32_t frameid;
  /// 参与者数量
  int32_t count;
  /// 参与者集合
  common_msgs_humble__msg__Cloudpant__Sequence pants;
} common_msgs_humble__msg__Cloudpants;

// Struct for a sequence of common_msgs_humble__msg__Cloudpants.
typedef struct common_msgs_humble__msg__Cloudpants__Sequence
{
  common_msgs_humble__msg__Cloudpants * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Cloudpants__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__CLOUDPANTS__STRUCT_H_
