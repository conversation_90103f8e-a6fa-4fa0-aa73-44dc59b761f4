// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Pullover.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__PULLOVER__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__PULLOVER__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Pullover __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Pullover __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Pullover_
{
  using Type = Pullover_<ContainerAllocator>;

  explicit Pullover_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->timestamp = 0ll;
      this->po_decision = 0;
      this->po_button = 0;
      this->doorstatus = 0;
      this->reserve = 0l;
    }
  }

  explicit Pullover_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->timestamp = 0ll;
      this->po_decision = 0;
      this->po_button = 0;
      this->doorstatus = 0;
      this->reserve = 0l;
    }
  }

  // field types and members
  using _timestamp_type =
    int64_t;
  _timestamp_type timestamp;
  using _po_decision_type =
    uint8_t;
  _po_decision_type po_decision;
  using _po_button_type =
    uint8_t;
  _po_button_type po_button;
  using _doorstatus_type =
    uint8_t;
  _doorstatus_type doorstatus;
  using _reserve_type =
    int32_t;
  _reserve_type reserve;

  // setters for named parameter idiom
  Type & set__timestamp(
    const int64_t & _arg)
  {
    this->timestamp = _arg;
    return *this;
  }
  Type & set__po_decision(
    const uint8_t & _arg)
  {
    this->po_decision = _arg;
    return *this;
  }
  Type & set__po_button(
    const uint8_t & _arg)
  {
    this->po_button = _arg;
    return *this;
  }
  Type & set__doorstatus(
    const uint8_t & _arg)
  {
    this->doorstatus = _arg;
    return *this;
  }
  Type & set__reserve(
    const int32_t & _arg)
  {
    this->reserve = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Pullover_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Pullover_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Pullover_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Pullover_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Pullover_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Pullover_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Pullover_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Pullover_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Pullover_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Pullover_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Pullover
    std::shared_ptr<common_msgs_humble::msg::Pullover_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Pullover
    std::shared_ptr<common_msgs_humble::msg::Pullover_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Pullover_ & other) const
  {
    if (this->timestamp != other.timestamp) {
      return false;
    }
    if (this->po_decision != other.po_decision) {
      return false;
    }
    if (this->po_button != other.po_button) {
      return false;
    }
    if (this->doorstatus != other.doorstatus) {
      return false;
    }
    if (this->reserve != other.reserve) {
      return false;
    }
    return true;
  }
  bool operator!=(const Pullover_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Pullover_

// alias to use template instance with default allocator
using Pullover =
  common_msgs_humble::msg::Pullover_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__PULLOVER__STRUCT_HPP_
