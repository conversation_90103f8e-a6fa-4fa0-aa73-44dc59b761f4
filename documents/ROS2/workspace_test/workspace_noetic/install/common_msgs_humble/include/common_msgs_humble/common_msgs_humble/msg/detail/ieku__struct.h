// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Ieku.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__IEKU__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__IEKU__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

/// Struct defined in msg/Ieku in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Ieku
{
  uint8_t id;
  double lata;
  double lona;
  double latb;
  double lonb;
  float width;
} common_msgs_humble__msg__Ieku;

// Struct for a sequence of common_msgs_humble__msg__Ieku.
typedef struct common_msgs_humble__msg__Ieku__Sequence
{
  common_msgs_humble__msg__Ieku * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Ieku__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__IEKU__STRUCT_H_
