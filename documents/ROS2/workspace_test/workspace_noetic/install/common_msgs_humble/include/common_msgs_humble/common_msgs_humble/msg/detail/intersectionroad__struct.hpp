// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Intersectionroad.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__INTERSECTIONROAD__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__INTERSECTIONROAD__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'lane'
#include "common_msgs_humble/msg/detail/roadpoint__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Intersectionroad __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Intersectionroad __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Intersectionroad_
{
  using Type = Intersectionroad_<ContainerAllocator>;

  explicit Intersectionroad_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_init;
  }

  explicit Intersectionroad_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_init;
    (void)_alloc;
  }

  // field types and members
  using _lane_type =
    std::vector<common_msgs_humble::msg::Roadpoint_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<common_msgs_humble::msg::Roadpoint_<ContainerAllocator>>>;
  _lane_type lane;

  // setters for named parameter idiom
  Type & set__lane(
    const std::vector<common_msgs_humble::msg::Roadpoint_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<common_msgs_humble::msg::Roadpoint_<ContainerAllocator>>> & _arg)
  {
    this->lane = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Intersectionroad_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Intersectionroad_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Intersectionroad_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Intersectionroad_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Intersectionroad_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Intersectionroad_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Intersectionroad_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Intersectionroad_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Intersectionroad_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Intersectionroad_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Intersectionroad
    std::shared_ptr<common_msgs_humble::msg::Intersectionroad_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Intersectionroad
    std::shared_ptr<common_msgs_humble::msg::Intersectionroad_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Intersectionroad_ & other) const
  {
    if (this->lane != other.lane) {
      return false;
    }
    return true;
  }
  bool operator!=(const Intersectionroad_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Intersectionroad_

// alias to use template instance with default allocator
using Intersectionroad =
  common_msgs_humble::msg::Intersectionroad_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__INTERSECTIONROAD__STRUCT_HPP_
