// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Intersectionroads.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__INTERSECTIONROADS__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__INTERSECTIONROADS__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'intersection'
#include "common_msgs_humble/msg/detail/intersectionroad__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Intersectionroads __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Intersectionroads __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Intersectionroads_
{
  using Type = Intersectionroads_<ContainerAllocator>;

  explicit Intersectionroads_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->timestamp = 0ll;
    }
  }

  explicit Intersectionroads_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->timestamp = 0ll;
    }
  }

  // field types and members
  using _intersection_type =
    std::vector<common_msgs_humble::msg::Intersectionroad_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<common_msgs_humble::msg::Intersectionroad_<ContainerAllocator>>>;
  _intersection_type intersection;
  using _timestamp_type =
    int64_t;
  _timestamp_type timestamp;

  // setters for named parameter idiom
  Type & set__intersection(
    const std::vector<common_msgs_humble::msg::Intersectionroad_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<common_msgs_humble::msg::Intersectionroad_<ContainerAllocator>>> & _arg)
  {
    this->intersection = _arg;
    return *this;
  }
  Type & set__timestamp(
    const int64_t & _arg)
  {
    this->timestamp = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Intersectionroads_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Intersectionroads_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Intersectionroads_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Intersectionroads_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Intersectionroads_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Intersectionroads_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Intersectionroads_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Intersectionroads_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Intersectionroads_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Intersectionroads_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Intersectionroads
    std::shared_ptr<common_msgs_humble::msg::Intersectionroads_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Intersectionroads
    std::shared_ptr<common_msgs_humble::msg::Intersectionroads_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Intersectionroads_ & other) const
  {
    if (this->intersection != other.intersection) {
      return false;
    }
    if (this->timestamp != other.timestamp) {
      return false;
    }
    return true;
  }
  bool operator!=(const Intersectionroads_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Intersectionroads_

// alias to use template instance with default allocator
using Intersectionroads =
  common_msgs_humble::msg::Intersectionroads_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__INTERSECTIONROADS__STRUCT_HPP_
