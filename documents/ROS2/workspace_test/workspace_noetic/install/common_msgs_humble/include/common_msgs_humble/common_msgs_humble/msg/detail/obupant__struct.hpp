// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Obupant.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUPANT__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUPANT__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'roadlist'
#include "common_msgs_humble/msg/detail/oburoadlist__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Obupant __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Obupant __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Obupant_
{
  using Type = Obupant_<ContainerAllocator>;

  explicit Obupant_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->ptc_type = 0l;
      this->ptc_id = 0l;
      this->source = 0l;
      this->source_id = "";
      this->sec_mark = 0l;
      this->pos_lon = 0.0;
      this->pos_lat = 0.0;
      this->pos_latitude = 0.0;
      this->speed = 0.0f;
      this->heading = 0.0f;
      this->accel = 0.0f;
      this->accel_angle = 0.0f;
      this->acc4way_lon = 0.0f;
      this->acc4way_lat = 0.0f;
      this->acc4way_vert = 0.0f;
      this->acc4way_yaw = 0.0f;
      this->width = 0.0f;
      this->length = 0.0f;
      this->height = 0.0f;
      this->lon = 0.0f;
      this->lat = 0.0f;
      this->planlist_num = 0;
    }
  }

  explicit Obupant_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : source_id(_alloc)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->ptc_type = 0l;
      this->ptc_id = 0l;
      this->source = 0l;
      this->source_id = "";
      this->sec_mark = 0l;
      this->pos_lon = 0.0;
      this->pos_lat = 0.0;
      this->pos_latitude = 0.0;
      this->speed = 0.0f;
      this->heading = 0.0f;
      this->accel = 0.0f;
      this->accel_angle = 0.0f;
      this->acc4way_lon = 0.0f;
      this->acc4way_lat = 0.0f;
      this->acc4way_vert = 0.0f;
      this->acc4way_yaw = 0.0f;
      this->width = 0.0f;
      this->length = 0.0f;
      this->height = 0.0f;
      this->lon = 0.0f;
      this->lat = 0.0f;
      this->planlist_num = 0;
    }
  }

  // field types and members
  using _ptc_type_type =
    int32_t;
  _ptc_type_type ptc_type;
  using _ptc_id_type =
    int32_t;
  _ptc_id_type ptc_id;
  using _source_type =
    int32_t;
  _source_type source;
  using _source_id_type =
    std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>;
  _source_id_type source_id;
  using _sec_mark_type =
    int32_t;
  _sec_mark_type sec_mark;
  using _pos_lon_type =
    double;
  _pos_lon_type pos_lon;
  using _pos_lat_type =
    double;
  _pos_lat_type pos_lat;
  using _pos_latitude_type =
    double;
  _pos_latitude_type pos_latitude;
  using _speed_type =
    float;
  _speed_type speed;
  using _heading_type =
    float;
  _heading_type heading;
  using _accel_type =
    float;
  _accel_type accel;
  using _accel_angle_type =
    float;
  _accel_angle_type accel_angle;
  using _acc4way_lon_type =
    float;
  _acc4way_lon_type acc4way_lon;
  using _acc4way_lat_type =
    float;
  _acc4way_lat_type acc4way_lat;
  using _acc4way_vert_type =
    float;
  _acc4way_vert_type acc4way_vert;
  using _acc4way_yaw_type =
    float;
  _acc4way_yaw_type acc4way_yaw;
  using _width_type =
    float;
  _width_type width;
  using _length_type =
    float;
  _length_type length;
  using _height_type =
    float;
  _height_type height;
  using _lon_type =
    float;
  _lon_type lon;
  using _lat_type =
    float;
  _lat_type lat;
  using _planlist_num_type =
    uint8_t;
  _planlist_num_type planlist_num;
  using _roadlist_type =
    std::vector<common_msgs_humble::msg::Oburoadlist_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<common_msgs_humble::msg::Oburoadlist_<ContainerAllocator>>>;
  _roadlist_type roadlist;

  // setters for named parameter idiom
  Type & set__ptc_type(
    const int32_t & _arg)
  {
    this->ptc_type = _arg;
    return *this;
  }
  Type & set__ptc_id(
    const int32_t & _arg)
  {
    this->ptc_id = _arg;
    return *this;
  }
  Type & set__source(
    const int32_t & _arg)
  {
    this->source = _arg;
    return *this;
  }
  Type & set__source_id(
    const std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> & _arg)
  {
    this->source_id = _arg;
    return *this;
  }
  Type & set__sec_mark(
    const int32_t & _arg)
  {
    this->sec_mark = _arg;
    return *this;
  }
  Type & set__pos_lon(
    const double & _arg)
  {
    this->pos_lon = _arg;
    return *this;
  }
  Type & set__pos_lat(
    const double & _arg)
  {
    this->pos_lat = _arg;
    return *this;
  }
  Type & set__pos_latitude(
    const double & _arg)
  {
    this->pos_latitude = _arg;
    return *this;
  }
  Type & set__speed(
    const float & _arg)
  {
    this->speed = _arg;
    return *this;
  }
  Type & set__heading(
    const float & _arg)
  {
    this->heading = _arg;
    return *this;
  }
  Type & set__accel(
    const float & _arg)
  {
    this->accel = _arg;
    return *this;
  }
  Type & set__accel_angle(
    const float & _arg)
  {
    this->accel_angle = _arg;
    return *this;
  }
  Type & set__acc4way_lon(
    const float & _arg)
  {
    this->acc4way_lon = _arg;
    return *this;
  }
  Type & set__acc4way_lat(
    const float & _arg)
  {
    this->acc4way_lat = _arg;
    return *this;
  }
  Type & set__acc4way_vert(
    const float & _arg)
  {
    this->acc4way_vert = _arg;
    return *this;
  }
  Type & set__acc4way_yaw(
    const float & _arg)
  {
    this->acc4way_yaw = _arg;
    return *this;
  }
  Type & set__width(
    const float & _arg)
  {
    this->width = _arg;
    return *this;
  }
  Type & set__length(
    const float & _arg)
  {
    this->length = _arg;
    return *this;
  }
  Type & set__height(
    const float & _arg)
  {
    this->height = _arg;
    return *this;
  }
  Type & set__lon(
    const float & _arg)
  {
    this->lon = _arg;
    return *this;
  }
  Type & set__lat(
    const float & _arg)
  {
    this->lat = _arg;
    return *this;
  }
  Type & set__planlist_num(
    const uint8_t & _arg)
  {
    this->planlist_num = _arg;
    return *this;
  }
  Type & set__roadlist(
    const std::vector<common_msgs_humble::msg::Oburoadlist_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<common_msgs_humble::msg::Oburoadlist_<ContainerAllocator>>> & _arg)
  {
    this->roadlist = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Obupant_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Obupant_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Obupant_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Obupant_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Obupant_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Obupant_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Obupant_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Obupant_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Obupant_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Obupant_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Obupant
    std::shared_ptr<common_msgs_humble::msg::Obupant_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Obupant
    std::shared_ptr<common_msgs_humble::msg::Obupant_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Obupant_ & other) const
  {
    if (this->ptc_type != other.ptc_type) {
      return false;
    }
    if (this->ptc_id != other.ptc_id) {
      return false;
    }
    if (this->source != other.source) {
      return false;
    }
    if (this->source_id != other.source_id) {
      return false;
    }
    if (this->sec_mark != other.sec_mark) {
      return false;
    }
    if (this->pos_lon != other.pos_lon) {
      return false;
    }
    if (this->pos_lat != other.pos_lat) {
      return false;
    }
    if (this->pos_latitude != other.pos_latitude) {
      return false;
    }
    if (this->speed != other.speed) {
      return false;
    }
    if (this->heading != other.heading) {
      return false;
    }
    if (this->accel != other.accel) {
      return false;
    }
    if (this->accel_angle != other.accel_angle) {
      return false;
    }
    if (this->acc4way_lon != other.acc4way_lon) {
      return false;
    }
    if (this->acc4way_lat != other.acc4way_lat) {
      return false;
    }
    if (this->acc4way_vert != other.acc4way_vert) {
      return false;
    }
    if (this->acc4way_yaw != other.acc4way_yaw) {
      return false;
    }
    if (this->width != other.width) {
      return false;
    }
    if (this->length != other.length) {
      return false;
    }
    if (this->height != other.height) {
      return false;
    }
    if (this->lon != other.lon) {
      return false;
    }
    if (this->lat != other.lat) {
      return false;
    }
    if (this->planlist_num != other.planlist_num) {
      return false;
    }
    if (this->roadlist != other.roadlist) {
      return false;
    }
    return true;
  }
  bool operator!=(const Obupant_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Obupant_

// alias to use template instance with default allocator
using Obupant =
  common_msgs_humble::msg::Obupant_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUPANT__STRUCT_HPP_
