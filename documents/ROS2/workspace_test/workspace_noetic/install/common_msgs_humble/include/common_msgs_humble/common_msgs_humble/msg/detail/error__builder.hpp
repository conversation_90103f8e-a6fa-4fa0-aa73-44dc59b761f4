// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Error.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__ERROR__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__ERROR__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/error__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Error_describe
{
public:
  explicit Init_Error_describe(::common_msgs_humble::msg::Error & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Error describe(::common_msgs_humble::msg::Error::_describe_type arg)
  {
    msg_.describe = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Error msg_;
};

class Init_Error_subcode
{
public:
  explicit Init_Error_subcode(::common_msgs_humble::msg::Error & msg)
  : msg_(msg)
  {}
  Init_Error_describe subcode(::common_msgs_humble::msg::Error::_subcode_type arg)
  {
    msg_.subcode = std::move(arg);
    return Init_Error_describe(msg_);
  }

private:
  ::common_msgs_humble::msg::Error msg_;
};

class Init_Error_maincode
{
public:
  explicit Init_Error_maincode(::common_msgs_humble::msg::Error & msg)
  : msg_(msg)
  {}
  Init_Error_subcode maincode(::common_msgs_humble::msg::Error::_maincode_type arg)
  {
    msg_.maincode = std::move(arg);
    return Init_Error_subcode(msg_);
  }

private:
  ::common_msgs_humble::msg::Error msg_;
};

class Init_Error_timestamp
{
public:
  Init_Error_timestamp()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Error_maincode timestamp(::common_msgs_humble::msg::Error::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return Init_Error_maincode(msg_);
  }

private:
  ::common_msgs_humble::msg::Error msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Error>()
{
  return common_msgs_humble::msg::builder::Init_Error_timestamp();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__ERROR__BUILDER_HPP_
