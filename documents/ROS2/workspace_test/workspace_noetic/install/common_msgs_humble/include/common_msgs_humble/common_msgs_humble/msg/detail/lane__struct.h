﻿// NOLINT: This file starts with a BOM since it contain non-ASCII characters
// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Lane.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__LANE__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__LANE__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'points'
#include "common_msgs_humble/msg/detail/point3d__struct.h"

/// Struct defined in msg/Lane in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Lane
{
  /// 车道ID
  uint8_t id;
  /// 类别
  uint8_t classification;
  /// 置信度
  uint8_t value;
  /// 置信度
  float confidence;
  /// 轮廓点数据
  common_msgs_humble__msg__Point3d__Sequence points;
} common_msgs_humble__msg__Lane;

// Struct for a sequence of common_msgs_humble__msg__Lane.
typedef struct common_msgs_humble__msg__Lane__Sequence
{
  common_msgs_humble__msg__Lane * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Lane__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__LANE__STRUCT_H_
