// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/App.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__APP__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__APP__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/app__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_App_timestamp
{
public:
  explicit Init_App_timestamp(::common_msgs_humble::msg::App & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::App timestamp(::common_msgs_humble::msg::App::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::App msg_;
};

class Init_App_park
{
public:
  explicit Init_App_park(::common_msgs_humble::msg::App & msg)
  : msg_(msg)
  {}
  Init_App_timestamp park(::common_msgs_humble::msg::App::_park_type arg)
  {
    msg_.park = std::move(arg);
    return Init_App_timestamp(msg_);
  }

private:
  ::common_msgs_humble::msg::App msg_;
};

class Init_App_estop
{
public:
  explicit Init_App_estop(::common_msgs_humble::msg::App & msg)
  : msg_(msg)
  {}
  Init_App_park estop(::common_msgs_humble::msg::App::_estop_type arg)
  {
    msg_.estop = std::move(arg);
    return Init_App_park(msg_);
  }

private:
  ::common_msgs_humble::msg::App msg_;
};

class Init_App_apsnum
{
public:
  explicit Init_App_apsnum(::common_msgs_humble::msg::App & msg)
  : msg_(msg)
  {}
  Init_App_estop apsnum(::common_msgs_humble::msg::App::_apsnum_type arg)
  {
    msg_.apsnum = std::move(arg);
    return Init_App_estop(msg_);
  }

private:
  ::common_msgs_humble::msg::App msg_;
};

class Init_App_zonename
{
public:
  explicit Init_App_zonename(::common_msgs_humble::msg::App & msg)
  : msg_(msg)
  {}
  Init_App_apsnum zonename(::common_msgs_humble::msg::App::_zonename_type arg)
  {
    msg_.zonename = std::move(arg);
    return Init_App_apsnum(msg_);
  }

private:
  ::common_msgs_humble::msg::App msg_;
};

class Init_App_stopgo
{
public:
  Init_App_stopgo()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_App_zonename stopgo(::common_msgs_humble::msg::App::_stopgo_type arg)
  {
    msg_.stopgo = std::move(arg);
    return Init_App_zonename(msg_);
  }

private:
  ::common_msgs_humble::msg::App msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::App>()
{
  return common_msgs_humble::msg::builder::Init_App_stopgo();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__APP__BUILDER_HPP_
