// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Pointformat.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__POINTFORMAT__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__POINTFORMAT__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/pointformat__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Pointformat_path
{
public:
  explicit Init_Pointformat_path(::common_msgs_humble::msg::Pointformat & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Pointformat path(::common_msgs_humble::msg::Pointformat::_path_type arg)
  {
    msg_.path = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Pointformat msg_;
};

class Init_Pointformat_backup9
{
public:
  explicit Init_Pointformat_backup9(::common_msgs_humble::msg::Pointformat & msg)
  : msg_(msg)
  {}
  Init_Pointformat_path backup9(::common_msgs_humble::msg::Pointformat::_backup9_type arg)
  {
    msg_.backup9 = std::move(arg);
    return Init_Pointformat_path(msg_);
  }

private:
  ::common_msgs_humble::msg::Pointformat msg_;
};

class Init_Pointformat_backup8
{
public:
  explicit Init_Pointformat_backup8(::common_msgs_humble::msg::Pointformat & msg)
  : msg_(msg)
  {}
  Init_Pointformat_backup9 backup8(::common_msgs_humble::msg::Pointformat::_backup8_type arg)
  {
    msg_.backup8 = std::move(arg);
    return Init_Pointformat_backup9(msg_);
  }

private:
  ::common_msgs_humble::msg::Pointformat msg_;
};

class Init_Pointformat_backup7
{
public:
  explicit Init_Pointformat_backup7(::common_msgs_humble::msg::Pointformat & msg)
  : msg_(msg)
  {}
  Init_Pointformat_backup8 backup7(::common_msgs_humble::msg::Pointformat::_backup7_type arg)
  {
    msg_.backup7 = std::move(arg);
    return Init_Pointformat_backup8(msg_);
  }

private:
  ::common_msgs_humble::msg::Pointformat msg_;
};

class Init_Pointformat_backup6
{
public:
  explicit Init_Pointformat_backup6(::common_msgs_humble::msg::Pointformat & msg)
  : msg_(msg)
  {}
  Init_Pointformat_backup7 backup6(::common_msgs_humble::msg::Pointformat::_backup6_type arg)
  {
    msg_.backup6 = std::move(arg);
    return Init_Pointformat_backup7(msg_);
  }

private:
  ::common_msgs_humble::msg::Pointformat msg_;
};

class Init_Pointformat_backup5
{
public:
  explicit Init_Pointformat_backup5(::common_msgs_humble::msg::Pointformat & msg)
  : msg_(msg)
  {}
  Init_Pointformat_backup6 backup5(::common_msgs_humble::msg::Pointformat::_backup5_type arg)
  {
    msg_.backup5 = std::move(arg);
    return Init_Pointformat_backup6(msg_);
  }

private:
  ::common_msgs_humble::msg::Pointformat msg_;
};

class Init_Pointformat_backup4
{
public:
  explicit Init_Pointformat_backup4(::common_msgs_humble::msg::Pointformat & msg)
  : msg_(msg)
  {}
  Init_Pointformat_backup5 backup4(::common_msgs_humble::msg::Pointformat::_backup4_type arg)
  {
    msg_.backup4 = std::move(arg);
    return Init_Pointformat_backup5(msg_);
  }

private:
  ::common_msgs_humble::msg::Pointformat msg_;
};

class Init_Pointformat_backup3
{
public:
  explicit Init_Pointformat_backup3(::common_msgs_humble::msg::Pointformat & msg)
  : msg_(msg)
  {}
  Init_Pointformat_backup4 backup3(::common_msgs_humble::msg::Pointformat::_backup3_type arg)
  {
    msg_.backup3 = std::move(arg);
    return Init_Pointformat_backup4(msg_);
  }

private:
  ::common_msgs_humble::msg::Pointformat msg_;
};

class Init_Pointformat_backup2
{
public:
  explicit Init_Pointformat_backup2(::common_msgs_humble::msg::Pointformat & msg)
  : msg_(msg)
  {}
  Init_Pointformat_backup3 backup2(::common_msgs_humble::msg::Pointformat::_backup2_type arg)
  {
    msg_.backup2 = std::move(arg);
    return Init_Pointformat_backup3(msg_);
  }

private:
  ::common_msgs_humble::msg::Pointformat msg_;
};

class Init_Pointformat_backup1
{
public:
  explicit Init_Pointformat_backup1(::common_msgs_humble::msg::Pointformat & msg)
  : msg_(msg)
  {}
  Init_Pointformat_backup2 backup1(::common_msgs_humble::msg::Pointformat::_backup1_type arg)
  {
    msg_.backup1 = std::move(arg);
    return Init_Pointformat_backup2(msg_);
  }

private:
  ::common_msgs_humble::msg::Pointformat msg_;
};

class Init_Pointformat_index
{
public:
  explicit Init_Pointformat_index(::common_msgs_humble::msg::Pointformat & msg)
  : msg_(msg)
  {}
  Init_Pointformat_backup1 index(::common_msgs_humble::msg::Pointformat::_index_type arg)
  {
    msg_.index = std::move(arg);
    return Init_Pointformat_backup1(msg_);
  }

private:
  ::common_msgs_humble::msg::Pointformat msg_;
};

class Init_Pointformat_heading
{
public:
  explicit Init_Pointformat_heading(::common_msgs_humble::msg::Pointformat & msg)
  : msg_(msg)
  {}
  Init_Pointformat_index heading(::common_msgs_humble::msg::Pointformat::_heading_type arg)
  {
    msg_.heading = std::move(arg);
    return Init_Pointformat_index(msg_);
  }

private:
  ::common_msgs_humble::msg::Pointformat msg_;
};

class Init_Pointformat_lat
{
public:
  explicit Init_Pointformat_lat(::common_msgs_humble::msg::Pointformat & msg)
  : msg_(msg)
  {}
  Init_Pointformat_heading lat(::common_msgs_humble::msg::Pointformat::_lat_type arg)
  {
    msg_.lat = std::move(arg);
    return Init_Pointformat_heading(msg_);
  }

private:
  ::common_msgs_humble::msg::Pointformat msg_;
};

class Init_Pointformat_lon
{
public:
  Init_Pointformat_lon()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Pointformat_lat lon(::common_msgs_humble::msg::Pointformat::_lon_type arg)
  {
    msg_.lon = std::move(arg);
    return Init_Pointformat_lat(msg_);
  }

private:
  ::common_msgs_humble::msg::Pointformat msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Pointformat>()
{
  return common_msgs_humble::msg::builder::Init_Pointformat_lon();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__POINTFORMAT__BUILDER_HPP_
