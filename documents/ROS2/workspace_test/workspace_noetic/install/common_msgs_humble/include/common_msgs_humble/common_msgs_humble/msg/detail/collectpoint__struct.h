// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Collectpoint.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__COLLECTPOINT__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__COLLECTPOINT__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

/// Struct defined in msg/Collectpoint in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Collectpoint
{
  uint8_t zonename;
  double index;
  int32_t stoptime;
  uint8_t property;
  uint8_t orientation;
} common_msgs_humble__msg__Collectpoint;

// Struct for a sequence of common_msgs_humble__msg__Collectpoint.
typedef struct common_msgs_humble__msg__Collectpoint__Sequence
{
  common_msgs_humble__msg__Collectpoint * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Collectpoint__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__COLLECTPOINT__STRUCT_H_
