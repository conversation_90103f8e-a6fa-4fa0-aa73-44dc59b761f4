// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Pullover.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__PULLOVER__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__PULLOVER__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/pullover__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Pullover_reserve
{
public:
  explicit Init_Pullover_reserve(::common_msgs_humble::msg::Pullover & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Pullover reserve(::common_msgs_humble::msg::Pullover::_reserve_type arg)
  {
    msg_.reserve = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Pullover msg_;
};

class Init_Pullover_doorstatus
{
public:
  explicit Init_Pullover_doorstatus(::common_msgs_humble::msg::Pullover & msg)
  : msg_(msg)
  {}
  Init_Pullover_reserve doorstatus(::common_msgs_humble::msg::Pullover::_doorstatus_type arg)
  {
    msg_.doorstatus = std::move(arg);
    return Init_Pullover_reserve(msg_);
  }

private:
  ::common_msgs_humble::msg::Pullover msg_;
};

class Init_Pullover_po_button
{
public:
  explicit Init_Pullover_po_button(::common_msgs_humble::msg::Pullover & msg)
  : msg_(msg)
  {}
  Init_Pullover_doorstatus po_button(::common_msgs_humble::msg::Pullover::_po_button_type arg)
  {
    msg_.po_button = std::move(arg);
    return Init_Pullover_doorstatus(msg_);
  }

private:
  ::common_msgs_humble::msg::Pullover msg_;
};

class Init_Pullover_po_decision
{
public:
  explicit Init_Pullover_po_decision(::common_msgs_humble::msg::Pullover & msg)
  : msg_(msg)
  {}
  Init_Pullover_po_button po_decision(::common_msgs_humble::msg::Pullover::_po_decision_type arg)
  {
    msg_.po_decision = std::move(arg);
    return Init_Pullover_po_button(msg_);
  }

private:
  ::common_msgs_humble::msg::Pullover msg_;
};

class Init_Pullover_timestamp
{
public:
  Init_Pullover_timestamp()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Pullover_po_decision timestamp(::common_msgs_humble::msg::Pullover::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return Init_Pullover_po_decision(msg_);
  }

private:
  ::common_msgs_humble::msg::Pullover msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Pullover>()
{
  return common_msgs_humble::msg::builder::Init_Pullover_timestamp();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__PULLOVER__BUILDER_HPP_
