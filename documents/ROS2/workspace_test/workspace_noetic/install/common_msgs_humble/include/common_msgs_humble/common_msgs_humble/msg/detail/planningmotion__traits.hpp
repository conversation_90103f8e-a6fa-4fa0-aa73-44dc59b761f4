// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from common_msgs_humble:msg/Planningmotion.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__PLANNINGMOTION__TRAITS_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__PLANNINGMOTION__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "common_msgs_humble/msg/detail/planningmotion__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

// Include directives for member types
// Member 'points'
#include "common_msgs_humble/msg/detail/roadpoint__traits.hpp"

namespace common_msgs_humble
{

namespace msg
{

inline void to_flow_style_yaml(
  const Planningmotion & msg,
  std::ostream & out)
{
  out << "{";
  // member: points
  {
    if (msg.points.size() == 0) {
      out << "points: []";
    } else {
      out << "points: [";
      size_t pending_items = msg.points.size();
      for (auto item : msg.points) {
        to_flow_style_yaml(item, out);
        if (--pending_items > 0) {
          out << ", ";
        }
      }
      out << "]";
    }
    out << ", ";
  }

  // member: guidespeed
  {
    out << "guidespeed: ";
    rosidl_generator_traits::value_to_yaml(msg.guidespeed, out);
    out << ", ";
  }

  // member: guideangle
  {
    out << "guideangle: ";
    rosidl_generator_traits::value_to_yaml(msg.guideangle, out);
    out << ", ";
  }

  // member: changelanedis
  {
    out << "changelanedis: ";
    rosidl_generator_traits::value_to_yaml(msg.changelanedis, out);
    out << ", ";
  }

  // member: obslondis
  {
    out << "obslondis: ";
    rosidl_generator_traits::value_to_yaml(msg.obslondis, out);
    out << ", ";
  }

  // member: obslatdis
  {
    out << "obslatdis: ";
    rosidl_generator_traits::value_to_yaml(msg.obslatdis, out);
    out << ", ";
  }

  // member: isvalid
  {
    out << "isvalid: ";
    rosidl_generator_traits::value_to_yaml(msg.isvalid, out);
    out << ", ";
  }

  // member: timestamp
  {
    out << "timestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.timestamp, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const Planningmotion & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: points
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    if (msg.points.size() == 0) {
      out << "points: []\n";
    } else {
      out << "points:\n";
      for (auto item : msg.points) {
        if (indentation > 0) {
          out << std::string(indentation, ' ');
        }
        out << "-\n";
        to_block_style_yaml(item, out, indentation + 2);
      }
    }
  }

  // member: guidespeed
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "guidespeed: ";
    rosidl_generator_traits::value_to_yaml(msg.guidespeed, out);
    out << "\n";
  }

  // member: guideangle
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "guideangle: ";
    rosidl_generator_traits::value_to_yaml(msg.guideangle, out);
    out << "\n";
  }

  // member: changelanedis
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "changelanedis: ";
    rosidl_generator_traits::value_to_yaml(msg.changelanedis, out);
    out << "\n";
  }

  // member: obslondis
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "obslondis: ";
    rosidl_generator_traits::value_to_yaml(msg.obslondis, out);
    out << "\n";
  }

  // member: obslatdis
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "obslatdis: ";
    rosidl_generator_traits::value_to_yaml(msg.obslatdis, out);
    out << "\n";
  }

  // member: isvalid
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "isvalid: ";
    rosidl_generator_traits::value_to_yaml(msg.isvalid, out);
    out << "\n";
  }

  // member: timestamp
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "timestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.timestamp, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const Planningmotion & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace common_msgs_humble

namespace rosidl_generator_traits
{

[[deprecated("use common_msgs_humble::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const common_msgs_humble::msg::Planningmotion & msg,
  std::ostream & out, size_t indentation = 0)
{
  common_msgs_humble::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use common_msgs_humble::msg::to_yaml() instead")]]
inline std::string to_yaml(const common_msgs_humble::msg::Planningmotion & msg)
{
  return common_msgs_humble::msg::to_yaml(msg);
}

template<>
inline const char * data_type<common_msgs_humble::msg::Planningmotion>()
{
  return "common_msgs_humble::msg::Planningmotion";
}

template<>
inline const char * name<common_msgs_humble::msg::Planningmotion>()
{
  return "common_msgs_humble/msg/Planningmotion";
}

template<>
struct has_fixed_size<common_msgs_humble::msg::Planningmotion>
  : std::integral_constant<bool, false> {};

template<>
struct has_bounded_size<common_msgs_humble::msg::Planningmotion>
  : std::integral_constant<bool, false> {};

template<>
struct is_message<common_msgs_humble::msg::Planningmotion>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__PLANNINGMOTION__TRAITS_HPP_
