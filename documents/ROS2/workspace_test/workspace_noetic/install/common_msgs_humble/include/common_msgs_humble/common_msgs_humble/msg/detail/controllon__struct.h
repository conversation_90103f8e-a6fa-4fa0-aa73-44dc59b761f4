﻿// NOLINT: This file starts with a BOM since it contain non-ASCII characters
// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Controllon.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__CONTROLLON__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__CONTROLLON__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

/// Struct defined in msg/Controllon in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Controllon
{
  /// esp使能
  uint8_t espmethod;
  /// 输出量
  float gpsdis;
  /// 手刹使能
  uint8_t epbmethod;
  /// 手刹使能
  uint8_t epb;
  /// 档位使能
  uint8_t geermethod;
  /// 档位
  uint8_t gear;
  /// 制动信号使能
  uint8_t brakemethod;
  float brakepedal;
  float gaspedal;
  uint8_t station;
  uint8_t light;
  /// 上位机使能
  uint8_t pcmethod;
  /// 前方最危险目标距离
  float objdis;
  /// 前方最危险目标的相对速度
  float objrel;
  /// 模块模式
  uint8_t mode;
  /// 有效位
  uint8_t isvalid;
  /// 时间戳
  int64_t timestamp;
  float targetspeed;
  float apadis;
  uint8_t objtype;
} common_msgs_humble__msg__Controllon;

// Struct for a sequence of common_msgs_humble__msg__Controllon.
typedef struct common_msgs_humble__msg__Controllon__Sequence
{
  common_msgs_humble__msg__Controllon * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Controllon__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__CONTROLLON__STRUCT_H_
