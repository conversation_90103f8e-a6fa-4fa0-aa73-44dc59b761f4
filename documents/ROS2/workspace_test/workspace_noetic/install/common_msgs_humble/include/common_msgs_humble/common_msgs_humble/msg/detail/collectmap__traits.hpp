// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from common_msgs_humble:msg/Collectmap.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__COLLECTMAP__TRAITS_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__COLLECTMAP__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "common_msgs_humble/msg/detail/collectmap__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

namespace common_msgs_humble
{

namespace msg
{

inline void to_flow_style_yaml(
  const Collectmap & msg,
  std::ostream & out)
{
  out << "{";
  // member: mapname
  {
    out << "mapname: ";
    rosidl_generator_traits::value_to_yaml(msg.mapname, out);
    out << ", ";
  }

  // member: zonename
  {
    out << "zonename: ";
    rosidl_generator_traits::value_to_yaml(msg.zonename, out);
    out << ", ";
  }

  // member: property
  {
    out << "property: ";
    rosidl_generator_traits::value_to_yaml(msg.property, out);
    out << ", ";
  }

  // member: laneattr
  {
    out << "laneattr: ";
    rosidl_generator_traits::value_to_yaml(msg.laneattr, out);
    out << ", ";
  }

  // member: speed
  {
    out << "speed: ";
    rosidl_generator_traits::value_to_yaml(msg.speed, out);
    out << ", ";
  }

  // member: sideroadwidth
  {
    out << "sideroadwidth: ";
    rosidl_generator_traits::value_to_yaml(msg.sideroadwidth, out);
    out << ", ";
  }

  // member: mergelanetype
  {
    out << "mergelanetype: ";
    rosidl_generator_traits::value_to_yaml(msg.mergelanetype, out);
    out << ", ";
  }

  // member: sensorlanetype
  {
    out << "sensorlanetype: ";
    rosidl_generator_traits::value_to_yaml(msg.sensorlanetype, out);
    out << ", ";
  }

  // member: leftsearchdis
  {
    out << "leftsearchdis: ";
    rosidl_generator_traits::value_to_yaml(msg.leftsearchdis, out);
    out << ", ";
  }

  // member: rightsearchdis
  {
    out << "rightsearchdis: ";
    rosidl_generator_traits::value_to_yaml(msg.rightsearchdis, out);
    out << ", ";
  }

  // member: timestamp
  {
    out << "timestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.timestamp, out);
    out << ", ";
  }

  // member: lanewidth
  {
    out << "lanewidth: ";
    rosidl_generator_traits::value_to_yaml(msg.lanewidth, out);
    out << ", ";
  }

  // member: leftlanewidth
  {
    out << "leftlanewidth: ";
    rosidl_generator_traits::value_to_yaml(msg.leftlanewidth, out);
    out << ", ";
  }

  // member: rightlanewidth
  {
    out << "rightlanewidth: ";
    rosidl_generator_traits::value_to_yaml(msg.rightlanewidth, out);
    out << ", ";
  }

  // member: laneswitch
  {
    out << "laneswitch: ";
    rosidl_generator_traits::value_to_yaml(msg.laneswitch, out);
    out << ", ";
  }

  // member: sidepass
  {
    out << "sidepass: ";
    rosidl_generator_traits::value_to_yaml(msg.sidepass, out);
    out << ", ";
  }

  // member: lanenum
  {
    out << "lanenum: ";
    rosidl_generator_traits::value_to_yaml(msg.lanenum, out);
    out << ", ";
  }

  // member: lanesite
  {
    out << "lanesite: ";
    rosidl_generator_traits::value_to_yaml(msg.lanesite, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const Collectmap & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: mapname
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "mapname: ";
    rosidl_generator_traits::value_to_yaml(msg.mapname, out);
    out << "\n";
  }

  // member: zonename
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "zonename: ";
    rosidl_generator_traits::value_to_yaml(msg.zonename, out);
    out << "\n";
  }

  // member: property
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "property: ";
    rosidl_generator_traits::value_to_yaml(msg.property, out);
    out << "\n";
  }

  // member: laneattr
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "laneattr: ";
    rosidl_generator_traits::value_to_yaml(msg.laneattr, out);
    out << "\n";
  }

  // member: speed
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "speed: ";
    rosidl_generator_traits::value_to_yaml(msg.speed, out);
    out << "\n";
  }

  // member: sideroadwidth
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "sideroadwidth: ";
    rosidl_generator_traits::value_to_yaml(msg.sideroadwidth, out);
    out << "\n";
  }

  // member: mergelanetype
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "mergelanetype: ";
    rosidl_generator_traits::value_to_yaml(msg.mergelanetype, out);
    out << "\n";
  }

  // member: sensorlanetype
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "sensorlanetype: ";
    rosidl_generator_traits::value_to_yaml(msg.sensorlanetype, out);
    out << "\n";
  }

  // member: leftsearchdis
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "leftsearchdis: ";
    rosidl_generator_traits::value_to_yaml(msg.leftsearchdis, out);
    out << "\n";
  }

  // member: rightsearchdis
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "rightsearchdis: ";
    rosidl_generator_traits::value_to_yaml(msg.rightsearchdis, out);
    out << "\n";
  }

  // member: timestamp
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "timestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.timestamp, out);
    out << "\n";
  }

  // member: lanewidth
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lanewidth: ";
    rosidl_generator_traits::value_to_yaml(msg.lanewidth, out);
    out << "\n";
  }

  // member: leftlanewidth
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "leftlanewidth: ";
    rosidl_generator_traits::value_to_yaml(msg.leftlanewidth, out);
    out << "\n";
  }

  // member: rightlanewidth
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "rightlanewidth: ";
    rosidl_generator_traits::value_to_yaml(msg.rightlanewidth, out);
    out << "\n";
  }

  // member: laneswitch
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "laneswitch: ";
    rosidl_generator_traits::value_to_yaml(msg.laneswitch, out);
    out << "\n";
  }

  // member: sidepass
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "sidepass: ";
    rosidl_generator_traits::value_to_yaml(msg.sidepass, out);
    out << "\n";
  }

  // member: lanenum
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lanenum: ";
    rosidl_generator_traits::value_to_yaml(msg.lanenum, out);
    out << "\n";
  }

  // member: lanesite
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lanesite: ";
    rosidl_generator_traits::value_to_yaml(msg.lanesite, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const Collectmap & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace common_msgs_humble

namespace rosidl_generator_traits
{

[[deprecated("use common_msgs_humble::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const common_msgs_humble::msg::Collectmap & msg,
  std::ostream & out, size_t indentation = 0)
{
  common_msgs_humble::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use common_msgs_humble::msg::to_yaml() instead")]]
inline std::string to_yaml(const common_msgs_humble::msg::Collectmap & msg)
{
  return common_msgs_humble::msg::to_yaml(msg);
}

template<>
inline const char * data_type<common_msgs_humble::msg::Collectmap>()
{
  return "common_msgs_humble::msg::Collectmap";
}

template<>
inline const char * name<common_msgs_humble::msg::Collectmap>()
{
  return "common_msgs_humble/msg/Collectmap";
}

template<>
struct has_fixed_size<common_msgs_humble::msg::Collectmap>
  : std::integral_constant<bool, true> {};

template<>
struct has_bounded_size<common_msgs_humble::msg::Collectmap>
  : std::integral_constant<bool, true> {};

template<>
struct is_message<common_msgs_humble::msg::Collectmap>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__COLLECTMAP__TRAITS_HPP_
