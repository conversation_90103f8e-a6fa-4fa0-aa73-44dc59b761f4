// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from common_msgs_humble:msg/Obupant.idl
// generated code does not contain a copyright notice
#include "common_msgs_humble/msg/detail/obupant__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


// Include directives for member types
// Member `source_id`
#include "rosidl_runtime_c/string_functions.h"
// Member `roadlist`
#include "common_msgs_humble/msg/detail/oburoadlist__functions.h"

bool
common_msgs_humble__msg__Obupant__init(common_msgs_humble__msg__Obupant * msg)
{
  if (!msg) {
    return false;
  }
  // ptc_type
  // ptc_id
  // source
  // source_id
  if (!rosidl_runtime_c__String__init(&msg->source_id)) {
    common_msgs_humble__msg__Obupant__fini(msg);
    return false;
  }
  // sec_mark
  // pos_lon
  // pos_lat
  // pos_latitude
  // speed
  // heading
  // accel
  // accel_angle
  // acc4way_lon
  // acc4way_lat
  // acc4way_vert
  // acc4way_yaw
  // width
  // length
  // height
  // lon
  // lat
  // planlist_num
  // roadlist
  if (!common_msgs_humble__msg__Oburoadlist__Sequence__init(&msg->roadlist, 0)) {
    common_msgs_humble__msg__Obupant__fini(msg);
    return false;
  }
  return true;
}

void
common_msgs_humble__msg__Obupant__fini(common_msgs_humble__msg__Obupant * msg)
{
  if (!msg) {
    return;
  }
  // ptc_type
  // ptc_id
  // source
  // source_id
  rosidl_runtime_c__String__fini(&msg->source_id);
  // sec_mark
  // pos_lon
  // pos_lat
  // pos_latitude
  // speed
  // heading
  // accel
  // accel_angle
  // acc4way_lon
  // acc4way_lat
  // acc4way_vert
  // acc4way_yaw
  // width
  // length
  // height
  // lon
  // lat
  // planlist_num
  // roadlist
  common_msgs_humble__msg__Oburoadlist__Sequence__fini(&msg->roadlist);
}

bool
common_msgs_humble__msg__Obupant__are_equal(const common_msgs_humble__msg__Obupant * lhs, const common_msgs_humble__msg__Obupant * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // ptc_type
  if (lhs->ptc_type != rhs->ptc_type) {
    return false;
  }
  // ptc_id
  if (lhs->ptc_id != rhs->ptc_id) {
    return false;
  }
  // source
  if (lhs->source != rhs->source) {
    return false;
  }
  // source_id
  if (!rosidl_runtime_c__String__are_equal(
      &(lhs->source_id), &(rhs->source_id)))
  {
    return false;
  }
  // sec_mark
  if (lhs->sec_mark != rhs->sec_mark) {
    return false;
  }
  // pos_lon
  if (lhs->pos_lon != rhs->pos_lon) {
    return false;
  }
  // pos_lat
  if (lhs->pos_lat != rhs->pos_lat) {
    return false;
  }
  // pos_latitude
  if (lhs->pos_latitude != rhs->pos_latitude) {
    return false;
  }
  // speed
  if (lhs->speed != rhs->speed) {
    return false;
  }
  // heading
  if (lhs->heading != rhs->heading) {
    return false;
  }
  // accel
  if (lhs->accel != rhs->accel) {
    return false;
  }
  // accel_angle
  if (lhs->accel_angle != rhs->accel_angle) {
    return false;
  }
  // acc4way_lon
  if (lhs->acc4way_lon != rhs->acc4way_lon) {
    return false;
  }
  // acc4way_lat
  if (lhs->acc4way_lat != rhs->acc4way_lat) {
    return false;
  }
  // acc4way_vert
  if (lhs->acc4way_vert != rhs->acc4way_vert) {
    return false;
  }
  // acc4way_yaw
  if (lhs->acc4way_yaw != rhs->acc4way_yaw) {
    return false;
  }
  // width
  if (lhs->width != rhs->width) {
    return false;
  }
  // length
  if (lhs->length != rhs->length) {
    return false;
  }
  // height
  if (lhs->height != rhs->height) {
    return false;
  }
  // lon
  if (lhs->lon != rhs->lon) {
    return false;
  }
  // lat
  if (lhs->lat != rhs->lat) {
    return false;
  }
  // planlist_num
  if (lhs->planlist_num != rhs->planlist_num) {
    return false;
  }
  // roadlist
  if (!common_msgs_humble__msg__Oburoadlist__Sequence__are_equal(
      &(lhs->roadlist), &(rhs->roadlist)))
  {
    return false;
  }
  return true;
}

bool
common_msgs_humble__msg__Obupant__copy(
  const common_msgs_humble__msg__Obupant * input,
  common_msgs_humble__msg__Obupant * output)
{
  if (!input || !output) {
    return false;
  }
  // ptc_type
  output->ptc_type = input->ptc_type;
  // ptc_id
  output->ptc_id = input->ptc_id;
  // source
  output->source = input->source;
  // source_id
  if (!rosidl_runtime_c__String__copy(
      &(input->source_id), &(output->source_id)))
  {
    return false;
  }
  // sec_mark
  output->sec_mark = input->sec_mark;
  // pos_lon
  output->pos_lon = input->pos_lon;
  // pos_lat
  output->pos_lat = input->pos_lat;
  // pos_latitude
  output->pos_latitude = input->pos_latitude;
  // speed
  output->speed = input->speed;
  // heading
  output->heading = input->heading;
  // accel
  output->accel = input->accel;
  // accel_angle
  output->accel_angle = input->accel_angle;
  // acc4way_lon
  output->acc4way_lon = input->acc4way_lon;
  // acc4way_lat
  output->acc4way_lat = input->acc4way_lat;
  // acc4way_vert
  output->acc4way_vert = input->acc4way_vert;
  // acc4way_yaw
  output->acc4way_yaw = input->acc4way_yaw;
  // width
  output->width = input->width;
  // length
  output->length = input->length;
  // height
  output->height = input->height;
  // lon
  output->lon = input->lon;
  // lat
  output->lat = input->lat;
  // planlist_num
  output->planlist_num = input->planlist_num;
  // roadlist
  if (!common_msgs_humble__msg__Oburoadlist__Sequence__copy(
      &(input->roadlist), &(output->roadlist)))
  {
    return false;
  }
  return true;
}

common_msgs_humble__msg__Obupant *
common_msgs_humble__msg__Obupant__create()
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Obupant * msg = (common_msgs_humble__msg__Obupant *)allocator.allocate(sizeof(common_msgs_humble__msg__Obupant), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(common_msgs_humble__msg__Obupant));
  bool success = common_msgs_humble__msg__Obupant__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
common_msgs_humble__msg__Obupant__destroy(common_msgs_humble__msg__Obupant * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    common_msgs_humble__msg__Obupant__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
common_msgs_humble__msg__Obupant__Sequence__init(common_msgs_humble__msg__Obupant__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Obupant * data = NULL;

  if (size) {
    data = (common_msgs_humble__msg__Obupant *)allocator.zero_allocate(size, sizeof(common_msgs_humble__msg__Obupant), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = common_msgs_humble__msg__Obupant__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        common_msgs_humble__msg__Obupant__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
common_msgs_humble__msg__Obupant__Sequence__fini(common_msgs_humble__msg__Obupant__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      common_msgs_humble__msg__Obupant__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

common_msgs_humble__msg__Obupant__Sequence *
common_msgs_humble__msg__Obupant__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Obupant__Sequence * array = (common_msgs_humble__msg__Obupant__Sequence *)allocator.allocate(sizeof(common_msgs_humble__msg__Obupant__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = common_msgs_humble__msg__Obupant__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
common_msgs_humble__msg__Obupant__Sequence__destroy(common_msgs_humble__msg__Obupant__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    common_msgs_humble__msg__Obupant__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
common_msgs_humble__msg__Obupant__Sequence__are_equal(const common_msgs_humble__msg__Obupant__Sequence * lhs, const common_msgs_humble__msg__Obupant__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!common_msgs_humble__msg__Obupant__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
common_msgs_humble__msg__Obupant__Sequence__copy(
  const common_msgs_humble__msg__Obupant__Sequence * input,
  common_msgs_humble__msg__Obupant__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(common_msgs_humble__msg__Obupant);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    common_msgs_humble__msg__Obupant * data =
      (common_msgs_humble__msg__Obupant *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!common_msgs_humble__msg__Obupant__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          common_msgs_humble__msg__Obupant__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!common_msgs_humble__msg__Obupant__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
