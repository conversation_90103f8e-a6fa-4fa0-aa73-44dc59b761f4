﻿// NOLINT: This file starts with a BOM since it contain non-ASCII characters
// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/ParkingActive.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__PARKING_ACTIVE__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__PARKING_ACTIVE__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'iekulist'
#include "common_msgs_humble/msg/detail/ieku__struct.h"

/// Struct defined in msg/ParkingActive in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__ParkingActive
{
  /// 时间戳  123
  int64_t timestamp;
  /// 0  1  2(默认读取planningmotion 话题的轨迹信息)  3(停止)    (泊车不主动发消息,inter需要提供驶出功能)  4(泊出)    5:默认
  uint8_t stage;
  /// 1:请求   0:默认             ---stage0
  uint8_t tips;
  /// 1进入 2不进入 0不处理        ---stage0
  uint8_t answer;
  /// 车库信息                    ---stage1
  common_msgs_humble__msg__Ieku__Sequence iekulist;
  /// ---stage1
  uint8_t iekutargetid;
  /// 0:不处理，  1 驶出        ---stage3      --------> 规划 stage发4(泊出)
  uint8_t driveout;
  uint8_t stop_parking;
} common_msgs_humble__msg__ParkingActive;

// Struct for a sequence of common_msgs_humble__msg__ParkingActive.
typedef struct common_msgs_humble__msg__ParkingActive__Sequence
{
  common_msgs_humble__msg__ParkingActive * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__ParkingActive__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__PARKING_ACTIVE__STRUCT_H_
