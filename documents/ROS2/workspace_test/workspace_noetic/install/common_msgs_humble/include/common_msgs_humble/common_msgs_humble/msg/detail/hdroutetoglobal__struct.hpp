// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Hdroutetoglobal.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__HDROUTETOGLOBAL__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__HDROUTETOGLOBAL__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'map'
#include "common_msgs_humble/msg/detail/hdmap__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Hdroutetoglobal __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Hdroutetoglobal __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Hdroutetoglobal_
{
  using Type = Hdroutetoglobal_<ContainerAllocator>;

  explicit Hdroutetoglobal_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->isvalid = 0;
      this->timestamp = 0ll;
    }
  }

  explicit Hdroutetoglobal_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->isvalid = 0;
      this->timestamp = 0ll;
    }
  }

  // field types and members
  using _map_type =
    std::vector<common_msgs_humble::msg::Hdmap_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<common_msgs_humble::msg::Hdmap_<ContainerAllocator>>>;
  _map_type map;
  using _isvalid_type =
    uint8_t;
  _isvalid_type isvalid;
  using _timestamp_type =
    int64_t;
  _timestamp_type timestamp;

  // setters for named parameter idiom
  Type & set__map(
    const std::vector<common_msgs_humble::msg::Hdmap_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<common_msgs_humble::msg::Hdmap_<ContainerAllocator>>> & _arg)
  {
    this->map = _arg;
    return *this;
  }
  Type & set__isvalid(
    const uint8_t & _arg)
  {
    this->isvalid = _arg;
    return *this;
  }
  Type & set__timestamp(
    const int64_t & _arg)
  {
    this->timestamp = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Hdroutetoglobal_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Hdroutetoglobal_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Hdroutetoglobal_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Hdroutetoglobal_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Hdroutetoglobal_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Hdroutetoglobal_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Hdroutetoglobal_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Hdroutetoglobal_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Hdroutetoglobal_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Hdroutetoglobal_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Hdroutetoglobal
    std::shared_ptr<common_msgs_humble::msg::Hdroutetoglobal_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Hdroutetoglobal
    std::shared_ptr<common_msgs_humble::msg::Hdroutetoglobal_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Hdroutetoglobal_ & other) const
  {
    if (this->map != other.map) {
      return false;
    }
    if (this->isvalid != other.isvalid) {
      return false;
    }
    if (this->timestamp != other.timestamp) {
      return false;
    }
    return true;
  }
  bool operator!=(const Hdroutetoglobal_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Hdroutetoglobal_

// alias to use template instance with default allocator
using Hdroutetoglobal =
  common_msgs_humble::msg::Hdroutetoglobal_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__HDROUTETOGLOBAL__STRUCT_HPP_
