// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Ieku.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__IEKU__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__IEKU__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/ieku__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Ieku_width
{
public:
  explicit Init_Ieku_width(::common_msgs_humble::msg::Ieku & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Ieku width(::common_msgs_humble::msg::Ieku::_width_type arg)
  {
    msg_.width = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Ieku msg_;
};

class Init_Ieku_lonb
{
public:
  explicit Init_Ieku_lonb(::common_msgs_humble::msg::Ieku & msg)
  : msg_(msg)
  {}
  Init_Ieku_width lonb(::common_msgs_humble::msg::Ieku::_lonb_type arg)
  {
    msg_.lonb = std::move(arg);
    return Init_Ieku_width(msg_);
  }

private:
  ::common_msgs_humble::msg::Ieku msg_;
};

class Init_Ieku_latb
{
public:
  explicit Init_Ieku_latb(::common_msgs_humble::msg::Ieku & msg)
  : msg_(msg)
  {}
  Init_Ieku_lonb latb(::common_msgs_humble::msg::Ieku::_latb_type arg)
  {
    msg_.latb = std::move(arg);
    return Init_Ieku_lonb(msg_);
  }

private:
  ::common_msgs_humble::msg::Ieku msg_;
};

class Init_Ieku_lona
{
public:
  explicit Init_Ieku_lona(::common_msgs_humble::msg::Ieku & msg)
  : msg_(msg)
  {}
  Init_Ieku_latb lona(::common_msgs_humble::msg::Ieku::_lona_type arg)
  {
    msg_.lona = std::move(arg);
    return Init_Ieku_latb(msg_);
  }

private:
  ::common_msgs_humble::msg::Ieku msg_;
};

class Init_Ieku_lata
{
public:
  explicit Init_Ieku_lata(::common_msgs_humble::msg::Ieku & msg)
  : msg_(msg)
  {}
  Init_Ieku_lona lata(::common_msgs_humble::msg::Ieku::_lata_type arg)
  {
    msg_.lata = std::move(arg);
    return Init_Ieku_lona(msg_);
  }

private:
  ::common_msgs_humble::msg::Ieku msg_;
};

class Init_Ieku_id
{
public:
  Init_Ieku_id()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Ieku_lata id(::common_msgs_humble::msg::Ieku::_id_type arg)
  {
    msg_.id = std::move(arg);
    return Init_Ieku_lata(msg_);
  }

private:
  ::common_msgs_humble::msg::Ieku msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Ieku>()
{
  return common_msgs_humble::msg::builder::Init_Ieku_id();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__IEKU__BUILDER_HPP_
