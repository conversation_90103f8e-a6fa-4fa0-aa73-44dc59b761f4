﻿// NOLINT: This file starts with a BOM since it contain non-ASCII characters
// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Cloudpant.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__CLOUDPANT__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__CLOUDPANT__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'id'
#include "rosidl_runtime_c/string.h"

/// Struct defined in msg/Cloudpant in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Cloudpant
{
  /// 参与者ID，唯一值；
  rosidl_runtime_c__String id;
  /// 分车辆类型:0-未知；1-小客车；2-大货车；3-大巴车；4-行人；5-自行车；6-摩托车；7-中巴车；8-危化车；9-遗撒物；10-小货车;11-中货车
  int32_t vehicletype;
  /// 长度 单位m
  float length;
  /// 宽度 单位m
  float width;
  /// 高度 单位m
  float height;
  /// 分辨率1e-7°，东经为正，西经为负
  double longitude;
  /// 分辨率1e-7°，北纬为正，南纬为负
  double latitude;
  /// 位置置信度，单位：%
  int32_t locationconfidence;
  /// 速度，单位：m/s
  float speed;
  /// 航向角，单位：rad，保留1位小数，车头与正北夹角
  float courseangle;
  /// 运动参数置信度，单位：%
  int32_t sportconfidence;
} common_msgs_humble__msg__Cloudpant;

// Struct for a sequence of common_msgs_humble__msg__Cloudpant.
typedef struct common_msgs_humble__msg__Cloudpant__Sequence
{
  common_msgs_humble__msg__Cloudpant * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Cloudpant__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__CLOUDPANT__STRUCT_H_
