﻿// NOLINT: This file starts with a BOM since it contain non-ASCII characters
// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Objectprediction.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__OBJECTPREDICTION__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__OBJECTPREDICTION__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

/// Struct defined in msg/Objectprediction in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Objectprediction
{
  /// 时间步长-0.1s
  float timestep;
  /// x-carBackRFU-m
  float x;
  /// y-carBackRFU-m
  float y;
  /// z-carBackRFU-m
  float z;
  /// 纬度    #20220914
  double longtitude;
  /// 经度
  double latitude;
  /// 高度
  double altitude;
  /// 横滚角 rad
  float rollrad;
  /// 俯仰角 rad
  float pitchrad;
  /// 航向角 rad
  float azimuth;
  /// 相对速度-m/s
  float relavx;
  /// 相对速度-m/s
  float relavy;
  /// 绝对速度-m/s
  float absvx;
  /// 绝对速度-m/s
  float absvy;
  /// 自车RFU坐标Y轴正方向顺时针0-2pi,rad
  float heading;
  /// m
  float s;
  /// m
  float l;
  /// m/s
  float speeds;
  /// m/s
  float speedl;
} common_msgs_humble__msg__Objectprediction;

// Struct for a sequence of common_msgs_humble__msg__Objectprediction.
typedef struct common_msgs_humble__msg__Objectprediction__Sequence
{
  common_msgs_humble__msg__Objectprediction * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Objectprediction__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__OBJECTPREDICTION__STRUCT_H_
