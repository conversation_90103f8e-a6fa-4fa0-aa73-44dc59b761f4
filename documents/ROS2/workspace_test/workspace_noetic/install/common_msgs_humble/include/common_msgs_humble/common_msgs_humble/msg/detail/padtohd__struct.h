// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Padtohd.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__PADTOHD__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__PADTOHD__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'path'
#include "rosidl_runtime_c/string.h"

/// Struct defined in msg/Padtohd in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Padtohd
{
  uint8_t index;
  int64_t timestamp;
  rosidl_runtime_c__String path;
} common_msgs_humble__msg__Padtohd;

// Struct for a sequence of common_msgs_humble__msg__Padtohd.
typedef struct common_msgs_humble__msg__Padtohd__Sequence
{
  common_msgs_humble__msg__Padtohd * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Padtohd__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__PADTOHD__STRUCT_H_
