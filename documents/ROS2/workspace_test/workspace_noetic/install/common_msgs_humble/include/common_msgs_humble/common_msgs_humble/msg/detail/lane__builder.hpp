// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Lane.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__LANE__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__LANE__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/lane__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Lane_points
{
public:
  explicit Init_Lane_points(::common_msgs_humble::msg::Lane & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Lane points(::common_msgs_humble::msg::Lane::_points_type arg)
  {
    msg_.points = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Lane msg_;
};

class Init_Lane_confidence
{
public:
  explicit Init_Lane_confidence(::common_msgs_humble::msg::Lane & msg)
  : msg_(msg)
  {}
  Init_Lane_points confidence(::common_msgs_humble::msg::Lane::_confidence_type arg)
  {
    msg_.confidence = std::move(arg);
    return Init_Lane_points(msg_);
  }

private:
  ::common_msgs_humble::msg::Lane msg_;
};

class Init_Lane_value
{
public:
  explicit Init_Lane_value(::common_msgs_humble::msg::Lane & msg)
  : msg_(msg)
  {}
  Init_Lane_confidence value(::common_msgs_humble::msg::Lane::_value_type arg)
  {
    msg_.value = std::move(arg);
    return Init_Lane_confidence(msg_);
  }

private:
  ::common_msgs_humble::msg::Lane msg_;
};

class Init_Lane_classification
{
public:
  explicit Init_Lane_classification(::common_msgs_humble::msg::Lane & msg)
  : msg_(msg)
  {}
  Init_Lane_value classification(::common_msgs_humble::msg::Lane::_classification_type arg)
  {
    msg_.classification = std::move(arg);
    return Init_Lane_value(msg_);
  }

private:
  ::common_msgs_humble::msg::Lane msg_;
};

class Init_Lane_id
{
public:
  Init_Lane_id()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Lane_classification id(::common_msgs_humble::msg::Lane::_id_type arg)
  {
    msg_.id = std::move(arg);
    return Init_Lane_classification(msg_);
  }

private:
  ::common_msgs_humble::msg::Lane msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Lane>()
{
  return common_msgs_humble::msg::builder::Init_Lane_id();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__LANE__BUILDER_HPP_
