// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Collectmap.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__COLLECTMAP__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__COLLECTMAP__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Collectmap __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Collectmap __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Collectmap_
{
  using Type = Collectmap_<ContainerAllocator>;

  explicit Collectmap_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->mapname = 0;
      this->zonename = 0;
      this->property = 0;
      this->laneattr = 0;
      this->speed = 0;
      this->sideroadwidth = 0.0f;
      this->mergelanetype = 0;
      this->sensorlanetype = 0;
      this->leftsearchdis = 0.0f;
      this->rightsearchdis = 0.0f;
      this->timestamp = 0ll;
      this->lanewidth = 0.0f;
      this->leftlanewidth = 0.0f;
      this->rightlanewidth = 0.0f;
      this->laneswitch = 0;
      this->sidepass = 0;
      this->lanenum = 0;
      this->lanesite = 0;
    }
  }

  explicit Collectmap_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->mapname = 0;
      this->zonename = 0;
      this->property = 0;
      this->laneattr = 0;
      this->speed = 0;
      this->sideroadwidth = 0.0f;
      this->mergelanetype = 0;
      this->sensorlanetype = 0;
      this->leftsearchdis = 0.0f;
      this->rightsearchdis = 0.0f;
      this->timestamp = 0ll;
      this->lanewidth = 0.0f;
      this->leftlanewidth = 0.0f;
      this->rightlanewidth = 0.0f;
      this->laneswitch = 0;
      this->sidepass = 0;
      this->lanenum = 0;
      this->lanesite = 0;
    }
  }

  // field types and members
  using _mapname_type =
    uint8_t;
  _mapname_type mapname;
  using _zonename_type =
    uint8_t;
  _zonename_type zonename;
  using _property_type =
    uint8_t;
  _property_type property;
  using _laneattr_type =
    uint8_t;
  _laneattr_type laneattr;
  using _speed_type =
    uint8_t;
  _speed_type speed;
  using _sideroadwidth_type =
    float;
  _sideroadwidth_type sideroadwidth;
  using _mergelanetype_type =
    uint8_t;
  _mergelanetype_type mergelanetype;
  using _sensorlanetype_type =
    uint8_t;
  _sensorlanetype_type sensorlanetype;
  using _leftsearchdis_type =
    float;
  _leftsearchdis_type leftsearchdis;
  using _rightsearchdis_type =
    float;
  _rightsearchdis_type rightsearchdis;
  using _timestamp_type =
    int64_t;
  _timestamp_type timestamp;
  using _lanewidth_type =
    float;
  _lanewidth_type lanewidth;
  using _leftlanewidth_type =
    float;
  _leftlanewidth_type leftlanewidth;
  using _rightlanewidth_type =
    float;
  _rightlanewidth_type rightlanewidth;
  using _laneswitch_type =
    uint8_t;
  _laneswitch_type laneswitch;
  using _sidepass_type =
    uint8_t;
  _sidepass_type sidepass;
  using _lanenum_type =
    uint8_t;
  _lanenum_type lanenum;
  using _lanesite_type =
    uint8_t;
  _lanesite_type lanesite;

  // setters for named parameter idiom
  Type & set__mapname(
    const uint8_t & _arg)
  {
    this->mapname = _arg;
    return *this;
  }
  Type & set__zonename(
    const uint8_t & _arg)
  {
    this->zonename = _arg;
    return *this;
  }
  Type & set__property(
    const uint8_t & _arg)
  {
    this->property = _arg;
    return *this;
  }
  Type & set__laneattr(
    const uint8_t & _arg)
  {
    this->laneattr = _arg;
    return *this;
  }
  Type & set__speed(
    const uint8_t & _arg)
  {
    this->speed = _arg;
    return *this;
  }
  Type & set__sideroadwidth(
    const float & _arg)
  {
    this->sideroadwidth = _arg;
    return *this;
  }
  Type & set__mergelanetype(
    const uint8_t & _arg)
  {
    this->mergelanetype = _arg;
    return *this;
  }
  Type & set__sensorlanetype(
    const uint8_t & _arg)
  {
    this->sensorlanetype = _arg;
    return *this;
  }
  Type & set__leftsearchdis(
    const float & _arg)
  {
    this->leftsearchdis = _arg;
    return *this;
  }
  Type & set__rightsearchdis(
    const float & _arg)
  {
    this->rightsearchdis = _arg;
    return *this;
  }
  Type & set__timestamp(
    const int64_t & _arg)
  {
    this->timestamp = _arg;
    return *this;
  }
  Type & set__lanewidth(
    const float & _arg)
  {
    this->lanewidth = _arg;
    return *this;
  }
  Type & set__leftlanewidth(
    const float & _arg)
  {
    this->leftlanewidth = _arg;
    return *this;
  }
  Type & set__rightlanewidth(
    const float & _arg)
  {
    this->rightlanewidth = _arg;
    return *this;
  }
  Type & set__laneswitch(
    const uint8_t & _arg)
  {
    this->laneswitch = _arg;
    return *this;
  }
  Type & set__sidepass(
    const uint8_t & _arg)
  {
    this->sidepass = _arg;
    return *this;
  }
  Type & set__lanenum(
    const uint8_t & _arg)
  {
    this->lanenum = _arg;
    return *this;
  }
  Type & set__lanesite(
    const uint8_t & _arg)
  {
    this->lanesite = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Collectmap_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Collectmap_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Collectmap_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Collectmap_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Collectmap_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Collectmap_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Collectmap_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Collectmap_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Collectmap_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Collectmap_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Collectmap
    std::shared_ptr<common_msgs_humble::msg::Collectmap_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Collectmap
    std::shared_ptr<common_msgs_humble::msg::Collectmap_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Collectmap_ & other) const
  {
    if (this->mapname != other.mapname) {
      return false;
    }
    if (this->zonename != other.zonename) {
      return false;
    }
    if (this->property != other.property) {
      return false;
    }
    if (this->laneattr != other.laneattr) {
      return false;
    }
    if (this->speed != other.speed) {
      return false;
    }
    if (this->sideroadwidth != other.sideroadwidth) {
      return false;
    }
    if (this->mergelanetype != other.mergelanetype) {
      return false;
    }
    if (this->sensorlanetype != other.sensorlanetype) {
      return false;
    }
    if (this->leftsearchdis != other.leftsearchdis) {
      return false;
    }
    if (this->rightsearchdis != other.rightsearchdis) {
      return false;
    }
    if (this->timestamp != other.timestamp) {
      return false;
    }
    if (this->lanewidth != other.lanewidth) {
      return false;
    }
    if (this->leftlanewidth != other.leftlanewidth) {
      return false;
    }
    if (this->rightlanewidth != other.rightlanewidth) {
      return false;
    }
    if (this->laneswitch != other.laneswitch) {
      return false;
    }
    if (this->sidepass != other.sidepass) {
      return false;
    }
    if (this->lanenum != other.lanenum) {
      return false;
    }
    if (this->lanesite != other.lanesite) {
      return false;
    }
    return true;
  }
  bool operator!=(const Collectmap_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Collectmap_

// alias to use template instance with default allocator
using Collectmap =
  common_msgs_humble::msg::Collectmap_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__COLLECTMAP__STRUCT_HPP_
