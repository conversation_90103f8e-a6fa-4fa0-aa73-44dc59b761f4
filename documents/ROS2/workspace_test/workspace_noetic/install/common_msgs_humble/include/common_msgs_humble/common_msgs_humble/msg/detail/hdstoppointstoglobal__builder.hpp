// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Hdstoppointstoglobal.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__HDSTOPPOINTSTOGLOBAL__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__HDSTOPPOINTSTOGLOBAL__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/hdstoppointstoglobal__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Hdstoppointstoglobal_timestamp
{
public:
  explicit Init_Hdstoppointstoglobal_timestamp(::common_msgs_humble::msg::Hdstoppointstoglobal & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Hdstoppointstoglobal timestamp(::common_msgs_humble::msg::Hdstoppointstoglobal::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Hdstoppointstoglobal msg_;
};

class Init_Hdstoppointstoglobal_isvalid
{
public:
  explicit Init_Hdstoppointstoglobal_isvalid(::common_msgs_humble::msg::Hdstoppointstoglobal & msg)
  : msg_(msg)
  {}
  Init_Hdstoppointstoglobal_timestamp isvalid(::common_msgs_humble::msg::Hdstoppointstoglobal::_isvalid_type arg)
  {
    msg_.isvalid = std::move(arg);
    return Init_Hdstoppointstoglobal_timestamp(msg_);
  }

private:
  ::common_msgs_humble::msg::Hdstoppointstoglobal msg_;
};

class Init_Hdstoppointstoglobal_points
{
public:
  Init_Hdstoppointstoglobal_points()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Hdstoppointstoglobal_isvalid points(::common_msgs_humble::msg::Hdstoppointstoglobal::_points_type arg)
  {
    msg_.points = std::move(arg);
    return Init_Hdstoppointstoglobal_isvalid(msg_);
  }

private:
  ::common_msgs_humble::msg::Hdstoppointstoglobal msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Hdstoppointstoglobal>()
{
  return common_msgs_humble::msg::builder::Init_Hdstoppointstoglobal_points();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__HDSTOPPOINTSTOGLOBAL__BUILDER_HPP_
