// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from common_msgs_humble:msg/Rdcontrol.idl
// generated code does not contain a copyright notice
#include "common_msgs_humble/msg/detail/rdcontrol__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


// Include directives for member types
// Member `header`
#include "std_msgs/msg/detail/header__functions.h"

bool
common_msgs_humble__msg__Rdcontrol__init(common_msgs_humble__msg__Rdcontrol * msg)
{
  if (!msg) {
    return false;
  }
  // header
  if (!std_msgs__msg__Header__init(&msg->header)) {
    common_msgs_humble__msg__Rdcontrol__fini(msg);
    return false;
  }
  // timestamp
  // drivemode
  // drivestate
  // angle
  // gas
  // brake
  // turnlignt
  // gear
  // epb
  return true;
}

void
common_msgs_humble__msg__Rdcontrol__fini(common_msgs_humble__msg__Rdcontrol * msg)
{
  if (!msg) {
    return;
  }
  // header
  std_msgs__msg__Header__fini(&msg->header);
  // timestamp
  // drivemode
  // drivestate
  // angle
  // gas
  // brake
  // turnlignt
  // gear
  // epb
}

bool
common_msgs_humble__msg__Rdcontrol__are_equal(const common_msgs_humble__msg__Rdcontrol * lhs, const common_msgs_humble__msg__Rdcontrol * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // header
  if (!std_msgs__msg__Header__are_equal(
      &(lhs->header), &(rhs->header)))
  {
    return false;
  }
  // timestamp
  if (lhs->timestamp != rhs->timestamp) {
    return false;
  }
  // drivemode
  if (lhs->drivemode != rhs->drivemode) {
    return false;
  }
  // drivestate
  if (lhs->drivestate != rhs->drivestate) {
    return false;
  }
  // angle
  if (lhs->angle != rhs->angle) {
    return false;
  }
  // gas
  if (lhs->gas != rhs->gas) {
    return false;
  }
  // brake
  if (lhs->brake != rhs->brake) {
    return false;
  }
  // turnlignt
  if (lhs->turnlignt != rhs->turnlignt) {
    return false;
  }
  // gear
  if (lhs->gear != rhs->gear) {
    return false;
  }
  // epb
  if (lhs->epb != rhs->epb) {
    return false;
  }
  return true;
}

bool
common_msgs_humble__msg__Rdcontrol__copy(
  const common_msgs_humble__msg__Rdcontrol * input,
  common_msgs_humble__msg__Rdcontrol * output)
{
  if (!input || !output) {
    return false;
  }
  // header
  if (!std_msgs__msg__Header__copy(
      &(input->header), &(output->header)))
  {
    return false;
  }
  // timestamp
  output->timestamp = input->timestamp;
  // drivemode
  output->drivemode = input->drivemode;
  // drivestate
  output->drivestate = input->drivestate;
  // angle
  output->angle = input->angle;
  // gas
  output->gas = input->gas;
  // brake
  output->brake = input->brake;
  // turnlignt
  output->turnlignt = input->turnlignt;
  // gear
  output->gear = input->gear;
  // epb
  output->epb = input->epb;
  return true;
}

common_msgs_humble__msg__Rdcontrol *
common_msgs_humble__msg__Rdcontrol__create()
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Rdcontrol * msg = (common_msgs_humble__msg__Rdcontrol *)allocator.allocate(sizeof(common_msgs_humble__msg__Rdcontrol), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(common_msgs_humble__msg__Rdcontrol));
  bool success = common_msgs_humble__msg__Rdcontrol__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
common_msgs_humble__msg__Rdcontrol__destroy(common_msgs_humble__msg__Rdcontrol * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    common_msgs_humble__msg__Rdcontrol__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
common_msgs_humble__msg__Rdcontrol__Sequence__init(common_msgs_humble__msg__Rdcontrol__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Rdcontrol * data = NULL;

  if (size) {
    data = (common_msgs_humble__msg__Rdcontrol *)allocator.zero_allocate(size, sizeof(common_msgs_humble__msg__Rdcontrol), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = common_msgs_humble__msg__Rdcontrol__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        common_msgs_humble__msg__Rdcontrol__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
common_msgs_humble__msg__Rdcontrol__Sequence__fini(common_msgs_humble__msg__Rdcontrol__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      common_msgs_humble__msg__Rdcontrol__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

common_msgs_humble__msg__Rdcontrol__Sequence *
common_msgs_humble__msg__Rdcontrol__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Rdcontrol__Sequence * array = (common_msgs_humble__msg__Rdcontrol__Sequence *)allocator.allocate(sizeof(common_msgs_humble__msg__Rdcontrol__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = common_msgs_humble__msg__Rdcontrol__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
common_msgs_humble__msg__Rdcontrol__Sequence__destroy(common_msgs_humble__msg__Rdcontrol__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    common_msgs_humble__msg__Rdcontrol__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
common_msgs_humble__msg__Rdcontrol__Sequence__are_equal(const common_msgs_humble__msg__Rdcontrol__Sequence * lhs, const common_msgs_humble__msg__Rdcontrol__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!common_msgs_humble__msg__Rdcontrol__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
common_msgs_humble__msg__Rdcontrol__Sequence__copy(
  const common_msgs_humble__msg__Rdcontrol__Sequence * input,
  common_msgs_humble__msg__Rdcontrol__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(common_msgs_humble__msg__Rdcontrol);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    common_msgs_humble__msg__Rdcontrol * data =
      (common_msgs_humble__msg__Rdcontrol *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!common_msgs_humble__msg__Rdcontrol__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          common_msgs_humble__msg__Rdcontrol__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!common_msgs_humble__msg__Rdcontrol__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
