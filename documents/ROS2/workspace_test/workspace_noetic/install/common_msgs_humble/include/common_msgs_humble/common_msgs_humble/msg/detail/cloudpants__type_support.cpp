// generated from rosidl_typesupport_introspection_cpp/resource/idl__type_support.cpp.em
// with input from common_msgs_humble:msg/Cloudpants.idl
// generated code does not contain a copyright notice

#include "array"
#include "cstddef"
#include "string"
#include "vector"
#include "rosidl_runtime_c/message_type_support_struct.h"
#include "rosidl_typesupport_cpp/message_type_support.hpp"
#include "rosidl_typesupport_interface/macros.h"
#include "common_msgs_humble/msg/detail/cloudpants__struct.hpp"
#include "rosidl_typesupport_introspection_cpp/field_types.hpp"
#include "rosidl_typesupport_introspection_cpp/identifier.hpp"
#include "rosidl_typesupport_introspection_cpp/message_introspection.hpp"
#include "rosidl_typesupport_introspection_cpp/message_type_support_decl.hpp"
#include "rosidl_typesupport_introspection_cpp/visibility_control.h"

namespace common_msgs_humble
{

namespace msg
{

namespace rosidl_typesupport_introspection_cpp
{

void Cloudpants_init_function(
  void * message_memory, rosidl_runtime_cpp::MessageInitialization _init)
{
  new (message_memory) common_msgs_humble::msg::Cloudpants(_init);
}

void Cloudpants_fini_function(void * message_memory)
{
  auto typed_message = static_cast<common_msgs_humble::msg::Cloudpants *>(message_memory);
  typed_message->~Cloudpants();
}

size_t size_function__Cloudpants__pants(const void * untyped_member)
{
  const auto * member = reinterpret_cast<const std::vector<common_msgs_humble::msg::Cloudpant> *>(untyped_member);
  return member->size();
}

const void * get_const_function__Cloudpants__pants(const void * untyped_member, size_t index)
{
  const auto & member =
    *reinterpret_cast<const std::vector<common_msgs_humble::msg::Cloudpant> *>(untyped_member);
  return &member[index];
}

void * get_function__Cloudpants__pants(void * untyped_member, size_t index)
{
  auto & member =
    *reinterpret_cast<std::vector<common_msgs_humble::msg::Cloudpant> *>(untyped_member);
  return &member[index];
}

void fetch_function__Cloudpants__pants(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const auto & item = *reinterpret_cast<const common_msgs_humble::msg::Cloudpant *>(
    get_const_function__Cloudpants__pants(untyped_member, index));
  auto & value = *reinterpret_cast<common_msgs_humble::msg::Cloudpant *>(untyped_value);
  value = item;
}

void assign_function__Cloudpants__pants(
  void * untyped_member, size_t index, const void * untyped_value)
{
  auto & item = *reinterpret_cast<common_msgs_humble::msg::Cloudpant *>(
    get_function__Cloudpants__pants(untyped_member, index));
  const auto & value = *reinterpret_cast<const common_msgs_humble::msg::Cloudpant *>(untyped_value);
  item = value;
}

void resize_function__Cloudpants__pants(void * untyped_member, size_t size)
{
  auto * member =
    reinterpret_cast<std::vector<common_msgs_humble::msg::Cloudpant> *>(untyped_member);
  member->resize(size);
}

static const ::rosidl_typesupport_introspection_cpp::MessageMember Cloudpants_message_member_array[4] = {
  {
    "timestamp",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_INT64,  // type
    0,  // upper bound of string
    nullptr,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble::msg::Cloudpants, timestamp),  // bytes offset in struct
    nullptr,  // default value
    nullptr,  // size() function pointer
    nullptr,  // get_const(index) function pointer
    nullptr,  // get(index) function pointer
    nullptr,  // fetch(index, &value) function pointer
    nullptr,  // assign(index, value) function pointer
    nullptr  // resize(index) function pointer
  },
  {
    "frameid",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_INT32,  // type
    0,  // upper bound of string
    nullptr,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble::msg::Cloudpants, frameid),  // bytes offset in struct
    nullptr,  // default value
    nullptr,  // size() function pointer
    nullptr,  // get_const(index) function pointer
    nullptr,  // get(index) function pointer
    nullptr,  // fetch(index, &value) function pointer
    nullptr,  // assign(index, value) function pointer
    nullptr  // resize(index) function pointer
  },
  {
    "count",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_INT32,  // type
    0,  // upper bound of string
    nullptr,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble::msg::Cloudpants, count),  // bytes offset in struct
    nullptr,  // default value
    nullptr,  // size() function pointer
    nullptr,  // get_const(index) function pointer
    nullptr,  // get(index) function pointer
    nullptr,  // fetch(index, &value) function pointer
    nullptr,  // assign(index, value) function pointer
    nullptr  // resize(index) function pointer
  },
  {
    "pants",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    ::rosidl_typesupport_introspection_cpp::get_message_type_support_handle<common_msgs_humble::msg::Cloudpant>(),  // members of sub message
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble::msg::Cloudpants, pants),  // bytes offset in struct
    nullptr,  // default value
    size_function__Cloudpants__pants,  // size() function pointer
    get_const_function__Cloudpants__pants,  // get_const(index) function pointer
    get_function__Cloudpants__pants,  // get(index) function pointer
    fetch_function__Cloudpants__pants,  // fetch(index, &value) function pointer
    assign_function__Cloudpants__pants,  // assign(index, value) function pointer
    resize_function__Cloudpants__pants  // resize(index) function pointer
  }
};

static const ::rosidl_typesupport_introspection_cpp::MessageMembers Cloudpants_message_members = {
  "common_msgs_humble::msg",  // message namespace
  "Cloudpants",  // message name
  4,  // number of fields
  sizeof(common_msgs_humble::msg::Cloudpants),
  Cloudpants_message_member_array,  // message members
  Cloudpants_init_function,  // function to initialize message memory (memory has to be allocated)
  Cloudpants_fini_function  // function to terminate message instance (will not free memory)
};

static const rosidl_message_type_support_t Cloudpants_message_type_support_handle = {
  ::rosidl_typesupport_introspection_cpp::typesupport_identifier,
  &Cloudpants_message_members,
  get_message_typesupport_handle_function,
};

}  // namespace rosidl_typesupport_introspection_cpp

}  // namespace msg

}  // namespace common_msgs_humble


namespace rosidl_typesupport_introspection_cpp
{

template<>
ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_PUBLIC
const rosidl_message_type_support_t *
get_message_type_support_handle<common_msgs_humble::msg::Cloudpants>()
{
  return &::common_msgs_humble::msg::rosidl_typesupport_introspection_cpp::Cloudpants_message_type_support_handle;
}

}  // namespace rosidl_typesupport_introspection_cpp

#ifdef __cplusplus
extern "C"
{
#endif

ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_PUBLIC
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_cpp, common_msgs_humble, msg, Cloudpants)() {
  return &::common_msgs_humble::msg::rosidl_typesupport_introspection_cpp::Cloudpants_message_type_support_handle;
}

#ifdef __cplusplus
}
#endif
