// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Lanes.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__LANES__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__LANES__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/lanes__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Lanes_gpstime
{
public:
  explicit Init_Lanes_gpstime(::common_msgs_humble::msg::Lanes & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Lanes gpstime(::common_msgs_humble::msg::Lanes::_gpstime_type arg)
  {
    msg_.gpstime = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Lanes msg_;
};

class Init_Lanes_timestamp
{
public:
  explicit Init_Lanes_timestamp(::common_msgs_humble::msg::Lanes & msg)
  : msg_(msg)
  {}
  Init_Lanes_gpstime timestamp(::common_msgs_humble::msg::Lanes::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return Init_Lanes_gpstime(msg_);
  }

private:
  ::common_msgs_humble::msg::Lanes msg_;
};

class Init_Lanes_isvalid
{
public:
  explicit Init_Lanes_isvalid(::common_msgs_humble::msg::Lanes & msg)
  : msg_(msg)
  {}
  Init_Lanes_timestamp isvalid(::common_msgs_humble::msg::Lanes::_isvalid_type arg)
  {
    msg_.isvalid = std::move(arg);
    return Init_Lanes_timestamp(msg_);
  }

private:
  ::common_msgs_humble::msg::Lanes msg_;
};

class Init_Lanes_obs
{
public:
  Init_Lanes_obs()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Lanes_isvalid obs(::common_msgs_humble::msg::Lanes::_obs_type arg)
  {
    msg_.obs = std::move(arg);
    return Init_Lanes_isvalid(msg_);
  }

private:
  ::common_msgs_humble::msg::Lanes msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Lanes>()
{
  return common_msgs_humble::msg::builder::Init_Lanes_obs();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__LANES__BUILDER_HPP_
