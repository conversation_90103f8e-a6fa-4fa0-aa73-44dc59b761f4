// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Obupants.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUPANTS__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUPANTS__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/obupants__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Obupants_msg_cnt
{
public:
  explicit Init_Obupants_msg_cnt(::common_msgs_humble::msg::Obupants & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Obupants msg_cnt(::common_msgs_humble::msg::Obupants::_msg_cnt_type arg)
  {
    msg_.msg_cnt = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Obupants msg_;
};

class Init_Obupants_timestamp
{
public:
  explicit Init_Obupants_timestamp(::common_msgs_humble::msg::Obupants & msg)
  : msg_(msg)
  {}
  Init_Obupants_msg_cnt timestamp(::common_msgs_humble::msg::Obupants::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return Init_Obupants_msg_cnt(msg_);
  }

private:
  ::common_msgs_humble::msg::Obupants msg_;
};

class Init_Obupants_isvalid
{
public:
  explicit Init_Obupants_isvalid(::common_msgs_humble::msg::Obupants & msg)
  : msg_(msg)
  {}
  Init_Obupants_timestamp isvalid(::common_msgs_humble::msg::Obupants::_isvalid_type arg)
  {
    msg_.isvalid = std::move(arg);
    return Init_Obupants_timestamp(msg_);
  }

private:
  ::common_msgs_humble::msg::Obupants msg_;
};

class Init_Obupants_pants
{
public:
  Init_Obupants_pants()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Obupants_isvalid pants(::common_msgs_humble::msg::Obupants::_pants_type arg)
  {
    msg_.pants = std::move(arg);
    return Init_Obupants_isvalid(msg_);
  }

private:
  ::common_msgs_humble::msg::Obupants msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Obupants>()
{
  return common_msgs_humble::msg::builder::Init_Obupants_pants();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUPANTS__BUILDER_HPP_
