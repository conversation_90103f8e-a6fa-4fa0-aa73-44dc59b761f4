﻿// NOLINT: This file starts with a BOM since it contain non-ASCII characters
// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Obupant.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUPANT__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUPANT__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'source_id'
#include "rosidl_runtime_c/string.h"
// Member 'roadlist'
#include "common_msgs_humble/msg/detail/oburoadlist__struct.h"

/// Struct defined in msg/Obupant in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Obupant
{
  /// 0:未知 1：行人 2：小客车 3：巴士 4：货车 5：非机动车 100:僵尸车
  int32_t ptc_type;
  /// 交通参与者id
  int32_t ptc_id;
  /// 0:未知 1：自车 2：v2x 3:视频传感器 4：毫米波雷达 5：地磁线圈传感器  6:激光雷达 7：2类或以上感知数据的融合结果
  int32_t source;
  /// 对象来源ID
  rosidl_runtime_c__String source_id;
  /// 时间戳
  int32_t sec_mark;
  /// 位置经度
  double pos_lon;
  /// 位置纬度
  double pos_lat;
  /// m
  double pos_latitude;
  /// m/s
  float speed;
  /// 正北是0°，范围0-360
  float heading;
  /// m/s
  float accel;
  /// 加速度方向
  float accel_angle;
  /// 四轴纵向加速度
  float acc4way_lon;
  /// 四轴横向加速度
  float acc4way_lat;
  /// 四轴垂直加速度
  float acc4way_vert;
  /// 四轴角速度
  float acc4way_yaw;
  /// 单位m
  float width;
  /// 单位m
  float length;
  /// 单位m
  float height;
  /// 经度
  float lon;
  /// 纬度
  float lat;
  /// 预测轨迹点list
  uint8_t planlist_num;
  /// 预测轨迹
  common_msgs_humble__msg__Oburoadlist__Sequence roadlist;
} common_msgs_humble__msg__Obupant;

// Struct for a sequence of common_msgs_humble__msg__Obupant.
typedef struct common_msgs_humble__msg__Obupant__Sequence
{
  common_msgs_humble__msg__Obupant * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Obupant__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUPANT__STRUCT_H_
