// generated from rosidl_typesupport_introspection_c/resource/idl__type_support.c.em
// with input from common_msgs_humble:msg/Fusiontrackingobjects.idl
// generated code does not contain a copyright notice

#include <stddef.h>
#include "common_msgs_humble/msg/detail/fusiontrackingobjects__rosidl_typesupport_introspection_c.h"
#include "common_msgs_humble/msg/rosidl_typesupport_introspection_c__visibility_control.h"
#include "rosidl_typesupport_introspection_c/field_types.h"
#include "rosidl_typesupport_introspection_c/identifier.h"
#include "rosidl_typesupport_introspection_c/message_introspection.h"
#include "common_msgs_humble/msg/detail/fusiontrackingobjects__functions.h"
#include "common_msgs_humble/msg/detail/fusiontrackingobjects__struct.h"


// Include directives for member types
// Member `obs`
#include "common_msgs_humble/msg/fusiontrackingobject.h"
// Member `obs`
#include "common_msgs_humble/msg/detail/fusiontrackingobject__rosidl_typesupport_introspection_c.h"

#ifdef __cplusplus
extern "C"
{
#endif

void common_msgs_humble__msg__Fusiontrackingobjects__rosidl_typesupport_introspection_c__Fusiontrackingobjects_init_function(
  void * message_memory, enum rosidl_runtime_c__message_initialization _init)
{
  // TODO(karsten1987): initializers are not yet implemented for typesupport c
  // see https://bgithub.xyz/ros2/ros2/issues/397
  (void) _init;
  common_msgs_humble__msg__Fusiontrackingobjects__init(message_memory);
}

void common_msgs_humble__msg__Fusiontrackingobjects__rosidl_typesupport_introspection_c__Fusiontrackingobjects_fini_function(void * message_memory)
{
  common_msgs_humble__msg__Fusiontrackingobjects__fini(message_memory);
}

size_t common_msgs_humble__msg__Fusiontrackingobjects__rosidl_typesupport_introspection_c__size_function__Fusiontrackingobjects__obs(
  const void * untyped_member)
{
  const common_msgs_humble__msg__Fusiontrackingobject__Sequence * member =
    (const common_msgs_humble__msg__Fusiontrackingobject__Sequence *)(untyped_member);
  return member->size;
}

const void * common_msgs_humble__msg__Fusiontrackingobjects__rosidl_typesupport_introspection_c__get_const_function__Fusiontrackingobjects__obs(
  const void * untyped_member, size_t index)
{
  const common_msgs_humble__msg__Fusiontrackingobject__Sequence * member =
    (const common_msgs_humble__msg__Fusiontrackingobject__Sequence *)(untyped_member);
  return &member->data[index];
}

void * common_msgs_humble__msg__Fusiontrackingobjects__rosidl_typesupport_introspection_c__get_function__Fusiontrackingobjects__obs(
  void * untyped_member, size_t index)
{
  common_msgs_humble__msg__Fusiontrackingobject__Sequence * member =
    (common_msgs_humble__msg__Fusiontrackingobject__Sequence *)(untyped_member);
  return &member->data[index];
}

void common_msgs_humble__msg__Fusiontrackingobjects__rosidl_typesupport_introspection_c__fetch_function__Fusiontrackingobjects__obs(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const common_msgs_humble__msg__Fusiontrackingobject * item =
    ((const common_msgs_humble__msg__Fusiontrackingobject *)
    common_msgs_humble__msg__Fusiontrackingobjects__rosidl_typesupport_introspection_c__get_const_function__Fusiontrackingobjects__obs(untyped_member, index));
  common_msgs_humble__msg__Fusiontrackingobject * value =
    (common_msgs_humble__msg__Fusiontrackingobject *)(untyped_value);
  *value = *item;
}

void common_msgs_humble__msg__Fusiontrackingobjects__rosidl_typesupport_introspection_c__assign_function__Fusiontrackingobjects__obs(
  void * untyped_member, size_t index, const void * untyped_value)
{
  common_msgs_humble__msg__Fusiontrackingobject * item =
    ((common_msgs_humble__msg__Fusiontrackingobject *)
    common_msgs_humble__msg__Fusiontrackingobjects__rosidl_typesupport_introspection_c__get_function__Fusiontrackingobjects__obs(untyped_member, index));
  const common_msgs_humble__msg__Fusiontrackingobject * value =
    (const common_msgs_humble__msg__Fusiontrackingobject *)(untyped_value);
  *item = *value;
}

bool common_msgs_humble__msg__Fusiontrackingobjects__rosidl_typesupport_introspection_c__resize_function__Fusiontrackingobjects__obs(
  void * untyped_member, size_t size)
{
  common_msgs_humble__msg__Fusiontrackingobject__Sequence * member =
    (common_msgs_humble__msg__Fusiontrackingobject__Sequence *)(untyped_member);
  common_msgs_humble__msg__Fusiontrackingobject__Sequence__fini(member);
  return common_msgs_humble__msg__Fusiontrackingobject__Sequence__init(member, size);
}

static rosidl_typesupport_introspection_c__MessageMember common_msgs_humble__msg__Fusiontrackingobjects__rosidl_typesupport_introspection_c__Fusiontrackingobjects_message_member_array[4] = {
  {
    "obs",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message (initialized later)
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Fusiontrackingobjects, obs),  // bytes offset in struct
    NULL,  // default value
    common_msgs_humble__msg__Fusiontrackingobjects__rosidl_typesupport_introspection_c__size_function__Fusiontrackingobjects__obs,  // size() function pointer
    common_msgs_humble__msg__Fusiontrackingobjects__rosidl_typesupport_introspection_c__get_const_function__Fusiontrackingobjects__obs,  // get_const(index) function pointer
    common_msgs_humble__msg__Fusiontrackingobjects__rosidl_typesupport_introspection_c__get_function__Fusiontrackingobjects__obs,  // get(index) function pointer
    common_msgs_humble__msg__Fusiontrackingobjects__rosidl_typesupport_introspection_c__fetch_function__Fusiontrackingobjects__obs,  // fetch(index, &value) function pointer
    common_msgs_humble__msg__Fusiontrackingobjects__rosidl_typesupport_introspection_c__assign_function__Fusiontrackingobjects__obs,  // assign(index, value) function pointer
    common_msgs_humble__msg__Fusiontrackingobjects__rosidl_typesupport_introspection_c__resize_function__Fusiontrackingobjects__obs  // resize(index) function pointer
  },
  {
    "isvalid",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_UINT8,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Fusiontrackingobjects, isvalid),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "timestamp",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_INT64,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Fusiontrackingobjects, timestamp),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "gpstime",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_INT64,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Fusiontrackingobjects, gpstime),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  }
};

static const rosidl_typesupport_introspection_c__MessageMembers common_msgs_humble__msg__Fusiontrackingobjects__rosidl_typesupport_introspection_c__Fusiontrackingobjects_message_members = {
  "common_msgs_humble__msg",  // message namespace
  "Fusiontrackingobjects",  // message name
  4,  // number of fields
  sizeof(common_msgs_humble__msg__Fusiontrackingobjects),
  common_msgs_humble__msg__Fusiontrackingobjects__rosidl_typesupport_introspection_c__Fusiontrackingobjects_message_member_array,  // message members
  common_msgs_humble__msg__Fusiontrackingobjects__rosidl_typesupport_introspection_c__Fusiontrackingobjects_init_function,  // function to initialize message memory (memory has to be allocated)
  common_msgs_humble__msg__Fusiontrackingobjects__rosidl_typesupport_introspection_c__Fusiontrackingobjects_fini_function  // function to terminate message instance (will not free memory)
};

// this is not const since it must be initialized on first access
// since C does not allow non-integral compile-time constants
static rosidl_message_type_support_t common_msgs_humble__msg__Fusiontrackingobjects__rosidl_typesupport_introspection_c__Fusiontrackingobjects_message_type_support_handle = {
  0,
  &common_msgs_humble__msg__Fusiontrackingobjects__rosidl_typesupport_introspection_c__Fusiontrackingobjects_message_members,
  get_message_typesupport_handle_function,
};

ROSIDL_TYPESUPPORT_INTROSPECTION_C_EXPORT_common_msgs_humble
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, common_msgs_humble, msg, Fusiontrackingobjects)() {
  common_msgs_humble__msg__Fusiontrackingobjects__rosidl_typesupport_introspection_c__Fusiontrackingobjects_message_member_array[0].members_ =
    ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, common_msgs_humble, msg, Fusiontrackingobject)();
  if (!common_msgs_humble__msg__Fusiontrackingobjects__rosidl_typesupport_introspection_c__Fusiontrackingobjects_message_type_support_handle.typesupport_identifier) {
    common_msgs_humble__msg__Fusiontrackingobjects__rosidl_typesupport_introspection_c__Fusiontrackingobjects_message_type_support_handle.typesupport_identifier =
      rosidl_typesupport_introspection_c__identifier;
  }
  return &common_msgs_humble__msg__Fusiontrackingobjects__rosidl_typesupport_introspection_c__Fusiontrackingobjects_message_type_support_handle;
}
#ifdef __cplusplus
}
#endif
