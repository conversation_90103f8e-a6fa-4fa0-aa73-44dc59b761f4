// generated from rosidl_typesupport_introspection_c/resource/idl__type_support.c.em
// with input from common_msgs_humble:msg/Monitor.idl
// generated code does not contain a copyright notice

#include <stddef.h>
#include "common_msgs_humble/msg/detail/monitor__rosidl_typesupport_introspection_c.h"
#include "common_msgs_humble/msg/rosidl_typesupport_introspection_c__visibility_control.h"
#include "rosidl_typesupport_introspection_c/field_types.h"
#include "rosidl_typesupport_introspection_c/identifier.h"
#include "rosidl_typesupport_introspection_c/message_introspection.h"
#include "common_msgs_humble/msg/detail/monitor__functions.h"
#include "common_msgs_humble/msg/detail/monitor__struct.h"


// Include directives for member types
// Member `valuelight`
// Member `valuetext`
// Member `dotcnt`
#include "rosidl_runtime_c/primitives_sequence_functions.h"
// Member `deslight`
// Member `destext`
#include "rosidl_runtime_c/string_functions.h"

#ifdef __cplusplus
extern "C"
{
#endif

void common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__Monitor_init_function(
  void * message_memory, enum rosidl_runtime_c__message_initialization _init)
{
  // TODO(karsten1987): initializers are not yet implemented for typesupport c
  // see https://bgithub.xyz/ros2/ros2/issues/397
  (void) _init;
  common_msgs_humble__msg__Monitor__init(message_memory);
}

void common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__Monitor_fini_function(void * message_memory)
{
  common_msgs_humble__msg__Monitor__fini(message_memory);
}

size_t common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__size_function__Monitor__valuelight(
  const void * untyped_member)
{
  const rosidl_runtime_c__octet__Sequence * member =
    (const rosidl_runtime_c__octet__Sequence *)(untyped_member);
  return member->size;
}

const void * common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__get_const_function__Monitor__valuelight(
  const void * untyped_member, size_t index)
{
  const rosidl_runtime_c__octet__Sequence * member =
    (const rosidl_runtime_c__octet__Sequence *)(untyped_member);
  return &member->data[index];
}

void * common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__get_function__Monitor__valuelight(
  void * untyped_member, size_t index)
{
  rosidl_runtime_c__octet__Sequence * member =
    (rosidl_runtime_c__octet__Sequence *)(untyped_member);
  return &member->data[index];
}

void common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__fetch_function__Monitor__valuelight(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const uint8_t * item =
    ((const uint8_t *)
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__get_const_function__Monitor__valuelight(untyped_member, index));
  uint8_t * value =
    (uint8_t *)(untyped_value);
  *value = *item;
}

void common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__assign_function__Monitor__valuelight(
  void * untyped_member, size_t index, const void * untyped_value)
{
  uint8_t * item =
    ((uint8_t *)
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__get_function__Monitor__valuelight(untyped_member, index));
  const uint8_t * value =
    (const uint8_t *)(untyped_value);
  *item = *value;
}

bool common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__resize_function__Monitor__valuelight(
  void * untyped_member, size_t size)
{
  rosidl_runtime_c__octet__Sequence * member =
    (rosidl_runtime_c__octet__Sequence *)(untyped_member);
  rosidl_runtime_c__octet__Sequence__fini(member);
  return rosidl_runtime_c__octet__Sequence__init(member, size);
}

size_t common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__size_function__Monitor__deslight(
  const void * untyped_member)
{
  const rosidl_runtime_c__String__Sequence * member =
    (const rosidl_runtime_c__String__Sequence *)(untyped_member);
  return member->size;
}

const void * common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__get_const_function__Monitor__deslight(
  const void * untyped_member, size_t index)
{
  const rosidl_runtime_c__String__Sequence * member =
    (const rosidl_runtime_c__String__Sequence *)(untyped_member);
  return &member->data[index];
}

void * common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__get_function__Monitor__deslight(
  void * untyped_member, size_t index)
{
  rosidl_runtime_c__String__Sequence * member =
    (rosidl_runtime_c__String__Sequence *)(untyped_member);
  return &member->data[index];
}

void common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__fetch_function__Monitor__deslight(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const rosidl_runtime_c__String * item =
    ((const rosidl_runtime_c__String *)
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__get_const_function__Monitor__deslight(untyped_member, index));
  rosidl_runtime_c__String * value =
    (rosidl_runtime_c__String *)(untyped_value);
  *value = *item;
}

void common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__assign_function__Monitor__deslight(
  void * untyped_member, size_t index, const void * untyped_value)
{
  rosidl_runtime_c__String * item =
    ((rosidl_runtime_c__String *)
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__get_function__Monitor__deslight(untyped_member, index));
  const rosidl_runtime_c__String * value =
    (const rosidl_runtime_c__String *)(untyped_value);
  *item = *value;
}

bool common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__resize_function__Monitor__deslight(
  void * untyped_member, size_t size)
{
  rosidl_runtime_c__String__Sequence * member =
    (rosidl_runtime_c__String__Sequence *)(untyped_member);
  rosidl_runtime_c__String__Sequence__fini(member);
  return rosidl_runtime_c__String__Sequence__init(member, size);
}

size_t common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__size_function__Monitor__valuetext(
  const void * untyped_member)
{
  const rosidl_runtime_c__double__Sequence * member =
    (const rosidl_runtime_c__double__Sequence *)(untyped_member);
  return member->size;
}

const void * common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__get_const_function__Monitor__valuetext(
  const void * untyped_member, size_t index)
{
  const rosidl_runtime_c__double__Sequence * member =
    (const rosidl_runtime_c__double__Sequence *)(untyped_member);
  return &member->data[index];
}

void * common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__get_function__Monitor__valuetext(
  void * untyped_member, size_t index)
{
  rosidl_runtime_c__double__Sequence * member =
    (rosidl_runtime_c__double__Sequence *)(untyped_member);
  return &member->data[index];
}

void common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__fetch_function__Monitor__valuetext(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const double * item =
    ((const double *)
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__get_const_function__Monitor__valuetext(untyped_member, index));
  double * value =
    (double *)(untyped_value);
  *value = *item;
}

void common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__assign_function__Monitor__valuetext(
  void * untyped_member, size_t index, const void * untyped_value)
{
  double * item =
    ((double *)
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__get_function__Monitor__valuetext(untyped_member, index));
  const double * value =
    (const double *)(untyped_value);
  *item = *value;
}

bool common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__resize_function__Monitor__valuetext(
  void * untyped_member, size_t size)
{
  rosidl_runtime_c__double__Sequence * member =
    (rosidl_runtime_c__double__Sequence *)(untyped_member);
  rosidl_runtime_c__double__Sequence__fini(member);
  return rosidl_runtime_c__double__Sequence__init(member, size);
}

size_t common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__size_function__Monitor__destext(
  const void * untyped_member)
{
  const rosidl_runtime_c__String__Sequence * member =
    (const rosidl_runtime_c__String__Sequence *)(untyped_member);
  return member->size;
}

const void * common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__get_const_function__Monitor__destext(
  const void * untyped_member, size_t index)
{
  const rosidl_runtime_c__String__Sequence * member =
    (const rosidl_runtime_c__String__Sequence *)(untyped_member);
  return &member->data[index];
}

void * common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__get_function__Monitor__destext(
  void * untyped_member, size_t index)
{
  rosidl_runtime_c__String__Sequence * member =
    (rosidl_runtime_c__String__Sequence *)(untyped_member);
  return &member->data[index];
}

void common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__fetch_function__Monitor__destext(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const rosidl_runtime_c__String * item =
    ((const rosidl_runtime_c__String *)
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__get_const_function__Monitor__destext(untyped_member, index));
  rosidl_runtime_c__String * value =
    (rosidl_runtime_c__String *)(untyped_value);
  *value = *item;
}

void common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__assign_function__Monitor__destext(
  void * untyped_member, size_t index, const void * untyped_value)
{
  rosidl_runtime_c__String * item =
    ((rosidl_runtime_c__String *)
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__get_function__Monitor__destext(untyped_member, index));
  const rosidl_runtime_c__String * value =
    (const rosidl_runtime_c__String *)(untyped_value);
  *item = *value;
}

bool common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__resize_function__Monitor__destext(
  void * untyped_member, size_t size)
{
  rosidl_runtime_c__String__Sequence * member =
    (rosidl_runtime_c__String__Sequence *)(untyped_member);
  rosidl_runtime_c__String__Sequence__fini(member);
  return rosidl_runtime_c__String__Sequence__init(member, size);
}

size_t common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__size_function__Monitor__dotcnt(
  const void * untyped_member)
{
  const rosidl_runtime_c__octet__Sequence * member =
    (const rosidl_runtime_c__octet__Sequence *)(untyped_member);
  return member->size;
}

const void * common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__get_const_function__Monitor__dotcnt(
  const void * untyped_member, size_t index)
{
  const rosidl_runtime_c__octet__Sequence * member =
    (const rosidl_runtime_c__octet__Sequence *)(untyped_member);
  return &member->data[index];
}

void * common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__get_function__Monitor__dotcnt(
  void * untyped_member, size_t index)
{
  rosidl_runtime_c__octet__Sequence * member =
    (rosidl_runtime_c__octet__Sequence *)(untyped_member);
  return &member->data[index];
}

void common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__fetch_function__Monitor__dotcnt(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const uint8_t * item =
    ((const uint8_t *)
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__get_const_function__Monitor__dotcnt(untyped_member, index));
  uint8_t * value =
    (uint8_t *)(untyped_value);
  *value = *item;
}

void common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__assign_function__Monitor__dotcnt(
  void * untyped_member, size_t index, const void * untyped_value)
{
  uint8_t * item =
    ((uint8_t *)
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__get_function__Monitor__dotcnt(untyped_member, index));
  const uint8_t * value =
    (const uint8_t *)(untyped_value);
  *item = *value;
}

bool common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__resize_function__Monitor__dotcnt(
  void * untyped_member, size_t size)
{
  rosidl_runtime_c__octet__Sequence * member =
    (rosidl_runtime_c__octet__Sequence *)(untyped_member);
  rosidl_runtime_c__octet__Sequence__fini(member);
  return rosidl_runtime_c__octet__Sequence__init(member, size);
}

static rosidl_typesupport_introspection_c__MessageMember common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__Monitor_message_member_array[8] = {
  {
    "valuelight",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_OCTET,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Monitor, valuelight),  // bytes offset in struct
    NULL,  // default value
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__size_function__Monitor__valuelight,  // size() function pointer
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__get_const_function__Monitor__valuelight,  // get_const(index) function pointer
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__get_function__Monitor__valuelight,  // get(index) function pointer
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__fetch_function__Monitor__valuelight,  // fetch(index, &value) function pointer
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__assign_function__Monitor__valuelight,  // assign(index, value) function pointer
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__resize_function__Monitor__valuelight  // resize(index) function pointer
  },
  {
    "deslight",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_STRING,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Monitor, deslight),  // bytes offset in struct
    NULL,  // default value
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__size_function__Monitor__deslight,  // size() function pointer
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__get_const_function__Monitor__deslight,  // get_const(index) function pointer
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__get_function__Monitor__deslight,  // get(index) function pointer
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__fetch_function__Monitor__deslight,  // fetch(index, &value) function pointer
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__assign_function__Monitor__deslight,  // assign(index, value) function pointer
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__resize_function__Monitor__deslight  // resize(index) function pointer
  },
  {
    "valuetext",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_DOUBLE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Monitor, valuetext),  // bytes offset in struct
    NULL,  // default value
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__size_function__Monitor__valuetext,  // size() function pointer
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__get_const_function__Monitor__valuetext,  // get_const(index) function pointer
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__get_function__Monitor__valuetext,  // get(index) function pointer
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__fetch_function__Monitor__valuetext,  // fetch(index, &value) function pointer
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__assign_function__Monitor__valuetext,  // assign(index, value) function pointer
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__resize_function__Monitor__valuetext  // resize(index) function pointer
  },
  {
    "destext",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_STRING,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Monitor, destext),  // bytes offset in struct
    NULL,  // default value
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__size_function__Monitor__destext,  // size() function pointer
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__get_const_function__Monitor__destext,  // get_const(index) function pointer
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__get_function__Monitor__destext,  // get(index) function pointer
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__fetch_function__Monitor__destext,  // fetch(index, &value) function pointer
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__assign_function__Monitor__destext,  // assign(index, value) function pointer
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__resize_function__Monitor__destext  // resize(index) function pointer
  },
  {
    "dotcnt",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_OCTET,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Monitor, dotcnt),  // bytes offset in struct
    NULL,  // default value
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__size_function__Monitor__dotcnt,  // size() function pointer
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__get_const_function__Monitor__dotcnt,  // get_const(index) function pointer
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__get_function__Monitor__dotcnt,  // get(index) function pointer
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__fetch_function__Monitor__dotcnt,  // fetch(index, &value) function pointer
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__assign_function__Monitor__dotcnt,  // assign(index, value) function pointer
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__resize_function__Monitor__dotcnt  // resize(index) function pointer
  },
  {
    "timestamp",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_INT64,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Monitor, timestamp),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "status",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_INT64,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Monitor, status),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "sensorstate",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_INT64,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Monitor, sensorstate),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  }
};

static const rosidl_typesupport_introspection_c__MessageMembers common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__Monitor_message_members = {
  "common_msgs_humble__msg",  // message namespace
  "Monitor",  // message name
  8,  // number of fields
  sizeof(common_msgs_humble__msg__Monitor),
  common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__Monitor_message_member_array,  // message members
  common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__Monitor_init_function,  // function to initialize message memory (memory has to be allocated)
  common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__Monitor_fini_function  // function to terminate message instance (will not free memory)
};

// this is not const since it must be initialized on first access
// since C does not allow non-integral compile-time constants
static rosidl_message_type_support_t common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__Monitor_message_type_support_handle = {
  0,
  &common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__Monitor_message_members,
  get_message_typesupport_handle_function,
};

ROSIDL_TYPESUPPORT_INTROSPECTION_C_EXPORT_common_msgs_humble
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, common_msgs_humble, msg, Monitor)() {
  if (!common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__Monitor_message_type_support_handle.typesupport_identifier) {
    common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__Monitor_message_type_support_handle.typesupport_identifier =
      rosidl_typesupport_introspection_c__identifier;
  }
  return &common_msgs_humble__msg__Monitor__rosidl_typesupport_introspection_c__Monitor_message_type_support_handle;
}
#ifdef __cplusplus
}
#endif
