// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Collectpoint.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__COLLECTPOINT__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__COLLECTPOINT__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Collectpoint __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Collectpoint __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Collectpoint_
{
  using Type = Collectpoint_<ContainerAllocator>;

  explicit Collectpoint_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->zonename = 0;
      this->index = 0.0;
      this->stoptime = 0l;
      this->property = 0;
      this->orientation = 0;
    }
  }

  explicit Collectpoint_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->zonename = 0;
      this->index = 0.0;
      this->stoptime = 0l;
      this->property = 0;
      this->orientation = 0;
    }
  }

  // field types and members
  using _zonename_type =
    uint8_t;
  _zonename_type zonename;
  using _index_type =
    double;
  _index_type index;
  using _stoptime_type =
    int32_t;
  _stoptime_type stoptime;
  using _property_type =
    uint8_t;
  _property_type property;
  using _orientation_type =
    uint8_t;
  _orientation_type orientation;

  // setters for named parameter idiom
  Type & set__zonename(
    const uint8_t & _arg)
  {
    this->zonename = _arg;
    return *this;
  }
  Type & set__index(
    const double & _arg)
  {
    this->index = _arg;
    return *this;
  }
  Type & set__stoptime(
    const int32_t & _arg)
  {
    this->stoptime = _arg;
    return *this;
  }
  Type & set__property(
    const uint8_t & _arg)
  {
    this->property = _arg;
    return *this;
  }
  Type & set__orientation(
    const uint8_t & _arg)
  {
    this->orientation = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Collectpoint_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Collectpoint_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Collectpoint_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Collectpoint_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Collectpoint_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Collectpoint_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Collectpoint_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Collectpoint_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Collectpoint_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Collectpoint_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Collectpoint
    std::shared_ptr<common_msgs_humble::msg::Collectpoint_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Collectpoint
    std::shared_ptr<common_msgs_humble::msg::Collectpoint_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Collectpoint_ & other) const
  {
    if (this->zonename != other.zonename) {
      return false;
    }
    if (this->index != other.index) {
      return false;
    }
    if (this->stoptime != other.stoptime) {
      return false;
    }
    if (this->property != other.property) {
      return false;
    }
    if (this->orientation != other.orientation) {
      return false;
    }
    return true;
  }
  bool operator!=(const Collectpoint_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Collectpoint_

// alias to use template instance with default allocator
using Collectpoint =
  common_msgs_humble::msg::Collectpoint_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__COLLECTPOINT__STRUCT_HPP_
