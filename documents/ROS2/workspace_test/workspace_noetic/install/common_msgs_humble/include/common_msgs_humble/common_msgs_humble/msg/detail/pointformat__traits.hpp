// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from common_msgs_humble:msg/Pointformat.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__POINTFORMAT__TRAITS_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__POINTFORMAT__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "common_msgs_humble/msg/detail/pointformat__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

namespace common_msgs_humble
{

namespace msg
{

inline void to_flow_style_yaml(
  const Pointformat & msg,
  std::ostream & out)
{
  out << "{";
  // member: lon
  {
    out << "lon: ";
    rosidl_generator_traits::value_to_yaml(msg.lon, out);
    out << ", ";
  }

  // member: lat
  {
    out << "lat: ";
    rosidl_generator_traits::value_to_yaml(msg.lat, out);
    out << ", ";
  }

  // member: heading
  {
    out << "heading: ";
    rosidl_generator_traits::value_to_yaml(msg.heading, out);
    out << ", ";
  }

  // member: index
  {
    out << "index: ";
    rosidl_generator_traits::value_to_yaml(msg.index, out);
    out << ", ";
  }

  // member: backup1
  {
    out << "backup1: ";
    rosidl_generator_traits::value_to_yaml(msg.backup1, out);
    out << ", ";
  }

  // member: backup2
  {
    out << "backup2: ";
    rosidl_generator_traits::value_to_yaml(msg.backup2, out);
    out << ", ";
  }

  // member: backup3
  {
    out << "backup3: ";
    rosidl_generator_traits::value_to_yaml(msg.backup3, out);
    out << ", ";
  }

  // member: backup4
  {
    out << "backup4: ";
    rosidl_generator_traits::value_to_yaml(msg.backup4, out);
    out << ", ";
  }

  // member: backup5
  {
    out << "backup5: ";
    rosidl_generator_traits::value_to_yaml(msg.backup5, out);
    out << ", ";
  }

  // member: backup6
  {
    out << "backup6: ";
    rosidl_generator_traits::value_to_yaml(msg.backup6, out);
    out << ", ";
  }

  // member: backup7
  {
    out << "backup7: ";
    rosidl_generator_traits::value_to_yaml(msg.backup7, out);
    out << ", ";
  }

  // member: backup8
  {
    out << "backup8: ";
    rosidl_generator_traits::value_to_yaml(msg.backup8, out);
    out << ", ";
  }

  // member: backup9
  {
    out << "backup9: ";
    rosidl_generator_traits::value_to_yaml(msg.backup9, out);
    out << ", ";
  }

  // member: path
  {
    out << "path: ";
    rosidl_generator_traits::value_to_yaml(msg.path, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const Pointformat & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: lon
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lon: ";
    rosidl_generator_traits::value_to_yaml(msg.lon, out);
    out << "\n";
  }

  // member: lat
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lat: ";
    rosidl_generator_traits::value_to_yaml(msg.lat, out);
    out << "\n";
  }

  // member: heading
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "heading: ";
    rosidl_generator_traits::value_to_yaml(msg.heading, out);
    out << "\n";
  }

  // member: index
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "index: ";
    rosidl_generator_traits::value_to_yaml(msg.index, out);
    out << "\n";
  }

  // member: backup1
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "backup1: ";
    rosidl_generator_traits::value_to_yaml(msg.backup1, out);
    out << "\n";
  }

  // member: backup2
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "backup2: ";
    rosidl_generator_traits::value_to_yaml(msg.backup2, out);
    out << "\n";
  }

  // member: backup3
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "backup3: ";
    rosidl_generator_traits::value_to_yaml(msg.backup3, out);
    out << "\n";
  }

  // member: backup4
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "backup4: ";
    rosidl_generator_traits::value_to_yaml(msg.backup4, out);
    out << "\n";
  }

  // member: backup5
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "backup5: ";
    rosidl_generator_traits::value_to_yaml(msg.backup5, out);
    out << "\n";
  }

  // member: backup6
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "backup6: ";
    rosidl_generator_traits::value_to_yaml(msg.backup6, out);
    out << "\n";
  }

  // member: backup7
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "backup7: ";
    rosidl_generator_traits::value_to_yaml(msg.backup7, out);
    out << "\n";
  }

  // member: backup8
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "backup8: ";
    rosidl_generator_traits::value_to_yaml(msg.backup8, out);
    out << "\n";
  }

  // member: backup9
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "backup9: ";
    rosidl_generator_traits::value_to_yaml(msg.backup9, out);
    out << "\n";
  }

  // member: path
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "path: ";
    rosidl_generator_traits::value_to_yaml(msg.path, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const Pointformat & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace common_msgs_humble

namespace rosidl_generator_traits
{

[[deprecated("use common_msgs_humble::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const common_msgs_humble::msg::Pointformat & msg,
  std::ostream & out, size_t indentation = 0)
{
  common_msgs_humble::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use common_msgs_humble::msg::to_yaml() instead")]]
inline std::string to_yaml(const common_msgs_humble::msg::Pointformat & msg)
{
  return common_msgs_humble::msg::to_yaml(msg);
}

template<>
inline const char * data_type<common_msgs_humble::msg::Pointformat>()
{
  return "common_msgs_humble::msg::Pointformat";
}

template<>
inline const char * name<common_msgs_humble::msg::Pointformat>()
{
  return "common_msgs_humble/msg/Pointformat";
}

template<>
struct has_fixed_size<common_msgs_humble::msg::Pointformat>
  : std::integral_constant<bool, false> {};

template<>
struct has_bounded_size<common_msgs_humble::msg::Pointformat>
  : std::integral_constant<bool, false> {};

template<>
struct is_message<common_msgs_humble::msg::Pointformat>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__POINTFORMAT__TRAITS_HPP_
