// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Planningmotion.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__PLANNINGMOTION__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__PLANNINGMOTION__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'points'
#include "common_msgs_humble/msg/detail/roadpoint__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Planningmotion __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Planningmotion __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Planningmotion_
{
  using Type = Planningmotion_<ContainerAllocator>;

  explicit Planningmotion_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->guidespeed = 0.0f;
      this->guideangle = 0.0f;
      this->changelanedis = 0.0f;
      this->obslondis = 0.0f;
      this->obslatdis = 0.0f;
      this->isvalid = 0;
      this->timestamp = 0ll;
    }
  }

  explicit Planningmotion_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->guidespeed = 0.0f;
      this->guideangle = 0.0f;
      this->changelanedis = 0.0f;
      this->obslondis = 0.0f;
      this->obslatdis = 0.0f;
      this->isvalid = 0;
      this->timestamp = 0ll;
    }
  }

  // field types and members
  using _points_type =
    std::vector<common_msgs_humble::msg::Roadpoint_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<common_msgs_humble::msg::Roadpoint_<ContainerAllocator>>>;
  _points_type points;
  using _guidespeed_type =
    float;
  _guidespeed_type guidespeed;
  using _guideangle_type =
    float;
  _guideangle_type guideangle;
  using _changelanedis_type =
    float;
  _changelanedis_type changelanedis;
  using _obslondis_type =
    float;
  _obslondis_type obslondis;
  using _obslatdis_type =
    float;
  _obslatdis_type obslatdis;
  using _isvalid_type =
    uint8_t;
  _isvalid_type isvalid;
  using _timestamp_type =
    int64_t;
  _timestamp_type timestamp;

  // setters for named parameter idiom
  Type & set__points(
    const std::vector<common_msgs_humble::msg::Roadpoint_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<common_msgs_humble::msg::Roadpoint_<ContainerAllocator>>> & _arg)
  {
    this->points = _arg;
    return *this;
  }
  Type & set__guidespeed(
    const float & _arg)
  {
    this->guidespeed = _arg;
    return *this;
  }
  Type & set__guideangle(
    const float & _arg)
  {
    this->guideangle = _arg;
    return *this;
  }
  Type & set__changelanedis(
    const float & _arg)
  {
    this->changelanedis = _arg;
    return *this;
  }
  Type & set__obslondis(
    const float & _arg)
  {
    this->obslondis = _arg;
    return *this;
  }
  Type & set__obslatdis(
    const float & _arg)
  {
    this->obslatdis = _arg;
    return *this;
  }
  Type & set__isvalid(
    const uint8_t & _arg)
  {
    this->isvalid = _arg;
    return *this;
  }
  Type & set__timestamp(
    const int64_t & _arg)
  {
    this->timestamp = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Planningmotion_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Planningmotion_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Planningmotion_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Planningmotion_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Planningmotion_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Planningmotion_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Planningmotion_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Planningmotion_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Planningmotion_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Planningmotion_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Planningmotion
    std::shared_ptr<common_msgs_humble::msg::Planningmotion_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Planningmotion
    std::shared_ptr<common_msgs_humble::msg::Planningmotion_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Planningmotion_ & other) const
  {
    if (this->points != other.points) {
      return false;
    }
    if (this->guidespeed != other.guidespeed) {
      return false;
    }
    if (this->guideangle != other.guideangle) {
      return false;
    }
    if (this->changelanedis != other.changelanedis) {
      return false;
    }
    if (this->obslondis != other.obslondis) {
      return false;
    }
    if (this->obslatdis != other.obslatdis) {
      return false;
    }
    if (this->isvalid != other.isvalid) {
      return false;
    }
    if (this->timestamp != other.timestamp) {
      return false;
    }
    return true;
  }
  bool operator!=(const Planningmotion_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Planningmotion_

// alias to use template instance with default allocator
using Planningmotion =
  common_msgs_humble::msg::Planningmotion_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__PLANNINGMOTION__STRUCT_HPP_
