﻿// NOLINT: This file starts with a BOM since it contain non-ASCII characters
// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Actuator.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__ACTUATOR__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__ACTUATOR__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

/// Struct defined in msg/Actuator in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Actuator
{
  /// 方向盘使能位
  uint8_t epsmethod;
  /// 放向盘角度
  int16_t epsangle;
  /// esp使能位
  uint8_t espmethod;
  /// 刹车压力
  float escbrakepress;
  /// 油门开度
  uint8_t gaspedal;
  /// 系统状态
  uint8_t sysstatus;
  /// 车速
  float speed;
  /// 灯光
  uint8_t lights;
  uint8_t turnlight;
  uint8_t gear;
  uint8_t epb;
  uint8_t door;
  /// 有效位
  uint8_t isvalid;
  /// 时间戳
  int64_t timestamp;
  uint8_t sendsuccess;
  uint8_t brakepedal;
  uint8_t warning;
  int16_t error;
  uint8_t battery;
  uint8_t controlover;
  int32_t steerspeed;
  int32_t accelpos;
  int32_t breakflag;
  int32_t breakpos;
  int32_t yaw;
  int32_t mil;
  float soc;
  float batvol;
  int32_t acc;
  int32_t oilperhour;
  int32_t oilhundredkmconsume;
  int32_t oilconsume;
  int32_t autoctrlsig;
  float totalvoltage;
  float totalcurrent;
  int32_t motorspeed;
  int32_t motortorque;
  /// 线控状态，0关闭，1打开
  uint8_t wirecontrolstatus;
  /// 双闪状态，0关闭，1打开
  uint8_t blinkerstatus;
  /// 纵向加速度 m/s2
  float accx;
  /// 油门踏板开度
  float gaspedalcar;
} common_msgs_humble__msg__Actuator;

// Struct for a sequence of common_msgs_humble__msg__Actuator.
typedef struct common_msgs_humble__msg__Actuator__Sequence
{
  common_msgs_humble__msg__Actuator * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Actuator__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__ACTUATOR__STRUCT_H_
