// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Sensorobjects.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSOROBJECTS__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSOROBJECTS__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'obs'
#include "common_msgs_humble/msg/detail/sensorobject__struct.h"

/// Struct defined in msg/Sensorobjects in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Sensorobjects
{
  common_msgs_humble__msg__Sensorobject__Sequence obs;
  uint8_t isvalid;
  int64_t timestamp;
  int64_t gpstime;
} common_msgs_humble__msg__Sensorobjects;

// Struct for a sequence of common_msgs_humble__msg__Sensorobjects.
typedef struct common_msgs_humble__msg__Sensorobjects__Sequence
{
  common_msgs_humble__msg__Sensorobjects * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Sensorobjects__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSOROBJECTS__STRUCT_H_
