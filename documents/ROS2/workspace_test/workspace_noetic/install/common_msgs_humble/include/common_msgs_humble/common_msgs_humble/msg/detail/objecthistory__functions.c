// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from common_msgs_humble:msg/Objecthistory.idl
// generated code does not contain a copyright notice
#include "common_msgs_humble/msg/detail/objecthistory__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


// Include directives for member types
// Member `trajectorypoint`
#include "common_msgs_humble/msg/detail/point3d__functions.h"

bool
common_msgs_humble__msg__Objecthistory__init(common_msgs_humble__msg__Objecthistory * msg)
{
  if (!msg) {
    return false;
  }
  // timestamp
  // trajectorypoint
  if (!common_msgs_humble__msg__Point3d__init(&msg->trajectorypoint)) {
    common_msgs_humble__msg__Objecthistory__fini(msg);
    return false;
  }
  // lon
  // lat
  // alt
  // roll
  // pitch
  // heading
  // relavx
  // relavy
  // absvx
  // absvy
  // s
  // l
  // speeds
  // speedl
  return true;
}

void
common_msgs_humble__msg__Objecthistory__fini(common_msgs_humble__msg__Objecthistory * msg)
{
  if (!msg) {
    return;
  }
  // timestamp
  // trajectorypoint
  common_msgs_humble__msg__Point3d__fini(&msg->trajectorypoint);
  // lon
  // lat
  // alt
  // roll
  // pitch
  // heading
  // relavx
  // relavy
  // absvx
  // absvy
  // s
  // l
  // speeds
  // speedl
}

bool
common_msgs_humble__msg__Objecthistory__are_equal(const common_msgs_humble__msg__Objecthistory * lhs, const common_msgs_humble__msg__Objecthistory * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // timestamp
  if (lhs->timestamp != rhs->timestamp) {
    return false;
  }
  // trajectorypoint
  if (!common_msgs_humble__msg__Point3d__are_equal(
      &(lhs->trajectorypoint), &(rhs->trajectorypoint)))
  {
    return false;
  }
  // lon
  if (lhs->lon != rhs->lon) {
    return false;
  }
  // lat
  if (lhs->lat != rhs->lat) {
    return false;
  }
  // alt
  if (lhs->alt != rhs->alt) {
    return false;
  }
  // roll
  if (lhs->roll != rhs->roll) {
    return false;
  }
  // pitch
  if (lhs->pitch != rhs->pitch) {
    return false;
  }
  // heading
  if (lhs->heading != rhs->heading) {
    return false;
  }
  // relavx
  if (lhs->relavx != rhs->relavx) {
    return false;
  }
  // relavy
  if (lhs->relavy != rhs->relavy) {
    return false;
  }
  // absvx
  if (lhs->absvx != rhs->absvx) {
    return false;
  }
  // absvy
  if (lhs->absvy != rhs->absvy) {
    return false;
  }
  // s
  if (lhs->s != rhs->s) {
    return false;
  }
  // l
  if (lhs->l != rhs->l) {
    return false;
  }
  // speeds
  if (lhs->speeds != rhs->speeds) {
    return false;
  }
  // speedl
  if (lhs->speedl != rhs->speedl) {
    return false;
  }
  return true;
}

bool
common_msgs_humble__msg__Objecthistory__copy(
  const common_msgs_humble__msg__Objecthistory * input,
  common_msgs_humble__msg__Objecthistory * output)
{
  if (!input || !output) {
    return false;
  }
  // timestamp
  output->timestamp = input->timestamp;
  // trajectorypoint
  if (!common_msgs_humble__msg__Point3d__copy(
      &(input->trajectorypoint), &(output->trajectorypoint)))
  {
    return false;
  }
  // lon
  output->lon = input->lon;
  // lat
  output->lat = input->lat;
  // alt
  output->alt = input->alt;
  // roll
  output->roll = input->roll;
  // pitch
  output->pitch = input->pitch;
  // heading
  output->heading = input->heading;
  // relavx
  output->relavx = input->relavx;
  // relavy
  output->relavy = input->relavy;
  // absvx
  output->absvx = input->absvx;
  // absvy
  output->absvy = input->absvy;
  // s
  output->s = input->s;
  // l
  output->l = input->l;
  // speeds
  output->speeds = input->speeds;
  // speedl
  output->speedl = input->speedl;
  return true;
}

common_msgs_humble__msg__Objecthistory *
common_msgs_humble__msg__Objecthistory__create()
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Objecthistory * msg = (common_msgs_humble__msg__Objecthistory *)allocator.allocate(sizeof(common_msgs_humble__msg__Objecthistory), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(common_msgs_humble__msg__Objecthistory));
  bool success = common_msgs_humble__msg__Objecthistory__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
common_msgs_humble__msg__Objecthistory__destroy(common_msgs_humble__msg__Objecthistory * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    common_msgs_humble__msg__Objecthistory__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
common_msgs_humble__msg__Objecthistory__Sequence__init(common_msgs_humble__msg__Objecthistory__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Objecthistory * data = NULL;

  if (size) {
    data = (common_msgs_humble__msg__Objecthistory *)allocator.zero_allocate(size, sizeof(common_msgs_humble__msg__Objecthistory), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = common_msgs_humble__msg__Objecthistory__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        common_msgs_humble__msg__Objecthistory__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
common_msgs_humble__msg__Objecthistory__Sequence__fini(common_msgs_humble__msg__Objecthistory__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      common_msgs_humble__msg__Objecthistory__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

common_msgs_humble__msg__Objecthistory__Sequence *
common_msgs_humble__msg__Objecthistory__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Objecthistory__Sequence * array = (common_msgs_humble__msg__Objecthistory__Sequence *)allocator.allocate(sizeof(common_msgs_humble__msg__Objecthistory__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = common_msgs_humble__msg__Objecthistory__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
common_msgs_humble__msg__Objecthistory__Sequence__destroy(common_msgs_humble__msg__Objecthistory__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    common_msgs_humble__msg__Objecthistory__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
common_msgs_humble__msg__Objecthistory__Sequence__are_equal(const common_msgs_humble__msg__Objecthistory__Sequence * lhs, const common_msgs_humble__msg__Objecthistory__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!common_msgs_humble__msg__Objecthistory__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
common_msgs_humble__msg__Objecthistory__Sequence__copy(
  const common_msgs_humble__msg__Objecthistory__Sequence * input,
  common_msgs_humble__msg__Objecthistory__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(common_msgs_humble__msg__Objecthistory);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    common_msgs_humble__msg__Objecthistory * data =
      (common_msgs_humble__msg__Objecthistory *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!common_msgs_humble__msg__Objecthistory__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          common_msgs_humble__msg__Objecthistory__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!common_msgs_humble__msg__Objecthistory__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
