// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Obulight.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__OBULIGHT__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__OBULIGHT__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

/// Struct defined in msg/Obulight in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Obulight
{
  int32_t phase_id;
  uint8_t status;
  int64_t start_time;
  int64_t end_time;
  int64_t next_start_time;
  float lane_speed_lower;
  float lane_speed_upper;
} common_msgs_humble__msg__Obulight;

// Struct for a sequence of common_msgs_humble__msg__Obulight.
typedef struct common_msgs_humble__msg__Obulight__Sequence
{
  common_msgs_humble__msg__Obulight * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Obulight__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__OBULIGHT__STRUCT_H_
