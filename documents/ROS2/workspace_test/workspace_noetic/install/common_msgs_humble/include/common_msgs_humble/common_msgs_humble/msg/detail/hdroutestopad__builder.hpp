// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Hdroutestopad.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__HDROUTESTOPAD__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__HDROUTESTOPAD__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/hdroutestopad__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Hdroutestopad_timestamp
{
public:
  explicit Init_Hdroutestopad_timestamp(::common_msgs_humble::msg::Hdroutestopad & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Hdroutestopad timestamp(::common_msgs_humble::msg::Hdroutestopad::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Hdroutestopad msg_;
};

class Init_Hdroutestopad_isvalid
{
public:
  explicit Init_Hdroutestopad_isvalid(::common_msgs_humble::msg::Hdroutestopad & msg)
  : msg_(msg)
  {}
  Init_Hdroutestopad_timestamp isvalid(::common_msgs_humble::msg::Hdroutestopad::_isvalid_type arg)
  {
    msg_.isvalid = std::move(arg);
    return Init_Hdroutestopad_timestamp(msg_);
  }

private:
  ::common_msgs_humble::msg::Hdroutestopad msg_;
};

class Init_Hdroutestopad_route
{
public:
  Init_Hdroutestopad_route()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Hdroutestopad_isvalid route(::common_msgs_humble::msg::Hdroutestopad::_route_type arg)
  {
    msg_.route = std::move(arg);
    return Init_Hdroutestopad_isvalid(msg_);
  }

private:
  ::common_msgs_humble::msg::Hdroutestopad msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Hdroutestopad>()
{
  return common_msgs_humble::msg::builder::Init_Hdroutestopad_route();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__HDROUTESTOPAD__BUILDER_HPP_
