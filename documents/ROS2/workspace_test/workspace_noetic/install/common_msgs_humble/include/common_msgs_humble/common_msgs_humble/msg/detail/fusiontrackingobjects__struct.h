// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Fusiontrackingobjects.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__FUSIONTRACKINGOBJECTS__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__FUSIONTRACKINGOBJECTS__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'obs'
#include "common_msgs_humble/msg/detail/fusiontrackingobject__struct.h"

/// Struct defined in msg/Fusiontrackingobjects in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Fusiontrackingobjects
{
  common_msgs_humble__msg__Fusiontrackingobject__Sequence obs;
  uint8_t isvalid;
  int64_t timestamp;
  int64_t gpstime;
} common_msgs_humble__msg__Fusiontrackingobjects;

// Struct for a sequence of common_msgs_humble__msg__Fusiontrackingobjects.
typedef struct common_msgs_humble__msg__Fusiontrackingobjects__Sequence
{
  common_msgs_humble__msg__Fusiontrackingobjects * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Fusiontrackingobjects__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__FUSIONTRACKINGOBJECTS__STRUCT_H_
