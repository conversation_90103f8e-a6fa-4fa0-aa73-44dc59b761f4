// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from common_msgs_humble:msg/Monitor.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__MONITOR__TRAITS_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__MONITOR__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "common_msgs_humble/msg/detail/monitor__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

namespace common_msgs_humble
{

namespace msg
{

inline void to_flow_style_yaml(
  const Monitor & msg,
  std::ostream & out)
{
  out << "{";
  // member: valuelight
  {
    if (msg.valuelight.size() == 0) {
      out << "valuelight: []";
    } else {
      out << "valuelight: [";
      size_t pending_items = msg.valuelight.size();
      for (auto item : msg.valuelight) {
        rosidl_generator_traits::character_value_to_yaml(item, out);
        if (--pending_items > 0) {
          out << ", ";
        }
      }
      out << "]";
    }
    out << ", ";
  }

  // member: deslight
  {
    if (msg.deslight.size() == 0) {
      out << "deslight: []";
    } else {
      out << "deslight: [";
      size_t pending_items = msg.deslight.size();
      for (auto item : msg.deslight) {
        rosidl_generator_traits::value_to_yaml(item, out);
        if (--pending_items > 0) {
          out << ", ";
        }
      }
      out << "]";
    }
    out << ", ";
  }

  // member: valuetext
  {
    if (msg.valuetext.size() == 0) {
      out << "valuetext: []";
    } else {
      out << "valuetext: [";
      size_t pending_items = msg.valuetext.size();
      for (auto item : msg.valuetext) {
        rosidl_generator_traits::value_to_yaml(item, out);
        if (--pending_items > 0) {
          out << ", ";
        }
      }
      out << "]";
    }
    out << ", ";
  }

  // member: destext
  {
    if (msg.destext.size() == 0) {
      out << "destext: []";
    } else {
      out << "destext: [";
      size_t pending_items = msg.destext.size();
      for (auto item : msg.destext) {
        rosidl_generator_traits::value_to_yaml(item, out);
        if (--pending_items > 0) {
          out << ", ";
        }
      }
      out << "]";
    }
    out << ", ";
  }

  // member: dotcnt
  {
    if (msg.dotcnt.size() == 0) {
      out << "dotcnt: []";
    } else {
      out << "dotcnt: [";
      size_t pending_items = msg.dotcnt.size();
      for (auto item : msg.dotcnt) {
        rosidl_generator_traits::character_value_to_yaml(item, out);
        if (--pending_items > 0) {
          out << ", ";
        }
      }
      out << "]";
    }
    out << ", ";
  }

  // member: timestamp
  {
    out << "timestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.timestamp, out);
    out << ", ";
  }

  // member: status
  {
    out << "status: ";
    rosidl_generator_traits::value_to_yaml(msg.status, out);
    out << ", ";
  }

  // member: sensorstate
  {
    out << "sensorstate: ";
    rosidl_generator_traits::value_to_yaml(msg.sensorstate, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const Monitor & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: valuelight
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    if (msg.valuelight.size() == 0) {
      out << "valuelight: []\n";
    } else {
      out << "valuelight:\n";
      for (auto item : msg.valuelight) {
        if (indentation > 0) {
          out << std::string(indentation, ' ');
        }
        out << "- ";
        rosidl_generator_traits::character_value_to_yaml(item, out);
        out << "\n";
      }
    }
  }

  // member: deslight
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    if (msg.deslight.size() == 0) {
      out << "deslight: []\n";
    } else {
      out << "deslight:\n";
      for (auto item : msg.deslight) {
        if (indentation > 0) {
          out << std::string(indentation, ' ');
        }
        out << "- ";
        rosidl_generator_traits::value_to_yaml(item, out);
        out << "\n";
      }
    }
  }

  // member: valuetext
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    if (msg.valuetext.size() == 0) {
      out << "valuetext: []\n";
    } else {
      out << "valuetext:\n";
      for (auto item : msg.valuetext) {
        if (indentation > 0) {
          out << std::string(indentation, ' ');
        }
        out << "- ";
        rosidl_generator_traits::value_to_yaml(item, out);
        out << "\n";
      }
    }
  }

  // member: destext
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    if (msg.destext.size() == 0) {
      out << "destext: []\n";
    } else {
      out << "destext:\n";
      for (auto item : msg.destext) {
        if (indentation > 0) {
          out << std::string(indentation, ' ');
        }
        out << "- ";
        rosidl_generator_traits::value_to_yaml(item, out);
        out << "\n";
      }
    }
  }

  // member: dotcnt
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    if (msg.dotcnt.size() == 0) {
      out << "dotcnt: []\n";
    } else {
      out << "dotcnt:\n";
      for (auto item : msg.dotcnt) {
        if (indentation > 0) {
          out << std::string(indentation, ' ');
        }
        out << "- ";
        rosidl_generator_traits::character_value_to_yaml(item, out);
        out << "\n";
      }
    }
  }

  // member: timestamp
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "timestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.timestamp, out);
    out << "\n";
  }

  // member: status
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "status: ";
    rosidl_generator_traits::value_to_yaml(msg.status, out);
    out << "\n";
  }

  // member: sensorstate
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "sensorstate: ";
    rosidl_generator_traits::value_to_yaml(msg.sensorstate, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const Monitor & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace common_msgs_humble

namespace rosidl_generator_traits
{

[[deprecated("use common_msgs_humble::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const common_msgs_humble::msg::Monitor & msg,
  std::ostream & out, size_t indentation = 0)
{
  common_msgs_humble::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use common_msgs_humble::msg::to_yaml() instead")]]
inline std::string to_yaml(const common_msgs_humble::msg::Monitor & msg)
{
  return common_msgs_humble::msg::to_yaml(msg);
}

template<>
inline const char * data_type<common_msgs_humble::msg::Monitor>()
{
  return "common_msgs_humble::msg::Monitor";
}

template<>
inline const char * name<common_msgs_humble::msg::Monitor>()
{
  return "common_msgs_humble/msg/Monitor";
}

template<>
struct has_fixed_size<common_msgs_humble::msg::Monitor>
  : std::integral_constant<bool, false> {};

template<>
struct has_bounded_size<common_msgs_humble::msg::Monitor>
  : std::integral_constant<bool, false> {};

template<>
struct is_message<common_msgs_humble::msg::Monitor>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__MONITOR__TRAITS_HPP_
