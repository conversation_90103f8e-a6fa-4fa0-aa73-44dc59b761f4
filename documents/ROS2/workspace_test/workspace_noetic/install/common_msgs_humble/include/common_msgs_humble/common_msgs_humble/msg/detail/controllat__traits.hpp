// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from common_msgs_humble:msg/Controllat.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__CONTROLLAT__TRAITS_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__CONTROLLAT__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "common_msgs_humble/msg/detail/controllat__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

namespace common_msgs_humble
{

namespace msg
{

inline void to_flow_style_yaml(
  const Controllat & msg,
  std::ostream & out)
{
  out << "{";
  // member: epsmethod
  {
    out << "epsmethod: ";
    rosidl_generator_traits::value_to_yaml(msg.epsmethod, out);
    out << ", ";
  }

  // member: epsangle
  {
    out << "epsangle: ";
    rosidl_generator_traits::value_to_yaml(msg.epsangle, out);
    out << ", ";
  }

  // member: limitspeed
  {
    out << "limitspeed: ";
    rosidl_generator_traits::value_to_yaml(msg.limitspeed, out);
    out << ", ";
  }

  // member: epstorque
  {
    out << "epstorque: ";
    rosidl_generator_traits::value_to_yaml(msg.epstorque, out);
    out << ", ";
  }

  // member: lightmethod
  {
    out << "lightmethod: ";
    rosidl_generator_traits::value_to_yaml(msg.lightmethod, out);
    out << ", ";
  }

  // member: lights
  {
    out << "lights: ";
    rosidl_generator_traits::value_to_yaml(msg.lights, out);
    out << ", ";
  }

  // member: isvalid
  {
    out << "isvalid: ";
    rosidl_generator_traits::value_to_yaml(msg.isvalid, out);
    out << ", ";
  }

  // member: deviation
  {
    out << "deviation: ";
    rosidl_generator_traits::value_to_yaml(msg.deviation, out);
    out << ", ";
  }

  // member: timestamp
  {
    out << "timestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.timestamp, out);
    out << ", ";
  }

  // member: apavstatus
  {
    out << "apavstatus: ";
    rosidl_generator_traits::value_to_yaml(msg.apavstatus, out);
    out << ", ";
  }

  // member: apsstate
  {
    out << "apsstate: ";
    rosidl_generator_traits::value_to_yaml(msg.apsstate, out);
    out << ", ";
  }

  // member: curve
  {
    out << "curve: ";
    rosidl_generator_traits::value_to_yaml(msg.curve, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const Controllat & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: epsmethod
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "epsmethod: ";
    rosidl_generator_traits::value_to_yaml(msg.epsmethod, out);
    out << "\n";
  }

  // member: epsangle
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "epsangle: ";
    rosidl_generator_traits::value_to_yaml(msg.epsangle, out);
    out << "\n";
  }

  // member: limitspeed
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "limitspeed: ";
    rosidl_generator_traits::value_to_yaml(msg.limitspeed, out);
    out << "\n";
  }

  // member: epstorque
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "epstorque: ";
    rosidl_generator_traits::value_to_yaml(msg.epstorque, out);
    out << "\n";
  }

  // member: lightmethod
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lightmethod: ";
    rosidl_generator_traits::value_to_yaml(msg.lightmethod, out);
    out << "\n";
  }

  // member: lights
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lights: ";
    rosidl_generator_traits::value_to_yaml(msg.lights, out);
    out << "\n";
  }

  // member: isvalid
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "isvalid: ";
    rosidl_generator_traits::value_to_yaml(msg.isvalid, out);
    out << "\n";
  }

  // member: deviation
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "deviation: ";
    rosidl_generator_traits::value_to_yaml(msg.deviation, out);
    out << "\n";
  }

  // member: timestamp
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "timestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.timestamp, out);
    out << "\n";
  }

  // member: apavstatus
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "apavstatus: ";
    rosidl_generator_traits::value_to_yaml(msg.apavstatus, out);
    out << "\n";
  }

  // member: apsstate
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "apsstate: ";
    rosidl_generator_traits::value_to_yaml(msg.apsstate, out);
    out << "\n";
  }

  // member: curve
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "curve: ";
    rosidl_generator_traits::value_to_yaml(msg.curve, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const Controllat & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace common_msgs_humble

namespace rosidl_generator_traits
{

[[deprecated("use common_msgs_humble::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const common_msgs_humble::msg::Controllat & msg,
  std::ostream & out, size_t indentation = 0)
{
  common_msgs_humble::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use common_msgs_humble::msg::to_yaml() instead")]]
inline std::string to_yaml(const common_msgs_humble::msg::Controllat & msg)
{
  return common_msgs_humble::msg::to_yaml(msg);
}

template<>
inline const char * data_type<common_msgs_humble::msg::Controllat>()
{
  return "common_msgs_humble::msg::Controllat";
}

template<>
inline const char * name<common_msgs_humble::msg::Controllat>()
{
  return "common_msgs_humble/msg/Controllat";
}

template<>
struct has_fixed_size<common_msgs_humble::msg::Controllat>
  : std::integral_constant<bool, true> {};

template<>
struct has_bounded_size<common_msgs_humble::msg::Controllat>
  : std::integral_constant<bool, true> {};

template<>
struct is_message<common_msgs_humble::msg::Controllat>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__CONTROLLAT__TRAITS_HPP_
