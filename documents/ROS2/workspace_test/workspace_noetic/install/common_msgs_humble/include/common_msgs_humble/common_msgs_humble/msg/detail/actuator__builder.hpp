// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Actuator.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__ACTUATOR__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__ACTUATOR__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/actuator__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Actuator_gaspedalcar
{
public:
  explicit Init_Actuator_gaspedalcar(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Actuator gaspedalcar(::common_msgs_humble::msg::Actuator::_gaspedalcar_type arg)
  {
    msg_.gaspedalcar = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_accx
{
public:
  explicit Init_Actuator_accx(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_gaspedalcar accx(::common_msgs_humble::msg::Actuator::_accx_type arg)
  {
    msg_.accx = std::move(arg);
    return Init_Actuator_gaspedalcar(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_blinkerstatus
{
public:
  explicit Init_Actuator_blinkerstatus(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_accx blinkerstatus(::common_msgs_humble::msg::Actuator::_blinkerstatus_type arg)
  {
    msg_.blinkerstatus = std::move(arg);
    return Init_Actuator_accx(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_wirecontrolstatus
{
public:
  explicit Init_Actuator_wirecontrolstatus(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_blinkerstatus wirecontrolstatus(::common_msgs_humble::msg::Actuator::_wirecontrolstatus_type arg)
  {
    msg_.wirecontrolstatus = std::move(arg);
    return Init_Actuator_blinkerstatus(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_motortorque
{
public:
  explicit Init_Actuator_motortorque(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_wirecontrolstatus motortorque(::common_msgs_humble::msg::Actuator::_motortorque_type arg)
  {
    msg_.motortorque = std::move(arg);
    return Init_Actuator_wirecontrolstatus(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_motorspeed
{
public:
  explicit Init_Actuator_motorspeed(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_motortorque motorspeed(::common_msgs_humble::msg::Actuator::_motorspeed_type arg)
  {
    msg_.motorspeed = std::move(arg);
    return Init_Actuator_motortorque(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_totalcurrent
{
public:
  explicit Init_Actuator_totalcurrent(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_motorspeed totalcurrent(::common_msgs_humble::msg::Actuator::_totalcurrent_type arg)
  {
    msg_.totalcurrent = std::move(arg);
    return Init_Actuator_motorspeed(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_totalvoltage
{
public:
  explicit Init_Actuator_totalvoltage(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_totalcurrent totalvoltage(::common_msgs_humble::msg::Actuator::_totalvoltage_type arg)
  {
    msg_.totalvoltage = std::move(arg);
    return Init_Actuator_totalcurrent(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_autoctrlsig
{
public:
  explicit Init_Actuator_autoctrlsig(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_totalvoltage autoctrlsig(::common_msgs_humble::msg::Actuator::_autoctrlsig_type arg)
  {
    msg_.autoctrlsig = std::move(arg);
    return Init_Actuator_totalvoltage(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_oilconsume
{
public:
  explicit Init_Actuator_oilconsume(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_autoctrlsig oilconsume(::common_msgs_humble::msg::Actuator::_oilconsume_type arg)
  {
    msg_.oilconsume = std::move(arg);
    return Init_Actuator_autoctrlsig(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_oilhundredkmconsume
{
public:
  explicit Init_Actuator_oilhundredkmconsume(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_oilconsume oilhundredkmconsume(::common_msgs_humble::msg::Actuator::_oilhundredkmconsume_type arg)
  {
    msg_.oilhundredkmconsume = std::move(arg);
    return Init_Actuator_oilconsume(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_oilperhour
{
public:
  explicit Init_Actuator_oilperhour(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_oilhundredkmconsume oilperhour(::common_msgs_humble::msg::Actuator::_oilperhour_type arg)
  {
    msg_.oilperhour = std::move(arg);
    return Init_Actuator_oilhundredkmconsume(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_acc
{
public:
  explicit Init_Actuator_acc(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_oilperhour acc(::common_msgs_humble::msg::Actuator::_acc_type arg)
  {
    msg_.acc = std::move(arg);
    return Init_Actuator_oilperhour(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_batvol
{
public:
  explicit Init_Actuator_batvol(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_acc batvol(::common_msgs_humble::msg::Actuator::_batvol_type arg)
  {
    msg_.batvol = std::move(arg);
    return Init_Actuator_acc(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_soc
{
public:
  explicit Init_Actuator_soc(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_batvol soc(::common_msgs_humble::msg::Actuator::_soc_type arg)
  {
    msg_.soc = std::move(arg);
    return Init_Actuator_batvol(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_mil
{
public:
  explicit Init_Actuator_mil(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_soc mil(::common_msgs_humble::msg::Actuator::_mil_type arg)
  {
    msg_.mil = std::move(arg);
    return Init_Actuator_soc(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_yaw
{
public:
  explicit Init_Actuator_yaw(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_mil yaw(::common_msgs_humble::msg::Actuator::_yaw_type arg)
  {
    msg_.yaw = std::move(arg);
    return Init_Actuator_mil(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_breakpos
{
public:
  explicit Init_Actuator_breakpos(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_yaw breakpos(::common_msgs_humble::msg::Actuator::_breakpos_type arg)
  {
    msg_.breakpos = std::move(arg);
    return Init_Actuator_yaw(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_breakflag
{
public:
  explicit Init_Actuator_breakflag(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_breakpos breakflag(::common_msgs_humble::msg::Actuator::_breakflag_type arg)
  {
    msg_.breakflag = std::move(arg);
    return Init_Actuator_breakpos(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_accelpos
{
public:
  explicit Init_Actuator_accelpos(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_breakflag accelpos(::common_msgs_humble::msg::Actuator::_accelpos_type arg)
  {
    msg_.accelpos = std::move(arg);
    return Init_Actuator_breakflag(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_steerspeed
{
public:
  explicit Init_Actuator_steerspeed(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_accelpos steerspeed(::common_msgs_humble::msg::Actuator::_steerspeed_type arg)
  {
    msg_.steerspeed = std::move(arg);
    return Init_Actuator_accelpos(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_controlover
{
public:
  explicit Init_Actuator_controlover(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_steerspeed controlover(::common_msgs_humble::msg::Actuator::_controlover_type arg)
  {
    msg_.controlover = std::move(arg);
    return Init_Actuator_steerspeed(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_battery
{
public:
  explicit Init_Actuator_battery(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_controlover battery(::common_msgs_humble::msg::Actuator::_battery_type arg)
  {
    msg_.battery = std::move(arg);
    return Init_Actuator_controlover(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_error
{
public:
  explicit Init_Actuator_error(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_battery error(::common_msgs_humble::msg::Actuator::_error_type arg)
  {
    msg_.error = std::move(arg);
    return Init_Actuator_battery(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_warning
{
public:
  explicit Init_Actuator_warning(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_error warning(::common_msgs_humble::msg::Actuator::_warning_type arg)
  {
    msg_.warning = std::move(arg);
    return Init_Actuator_error(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_brakepedal
{
public:
  explicit Init_Actuator_brakepedal(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_warning brakepedal(::common_msgs_humble::msg::Actuator::_brakepedal_type arg)
  {
    msg_.brakepedal = std::move(arg);
    return Init_Actuator_warning(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_sendsuccess
{
public:
  explicit Init_Actuator_sendsuccess(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_brakepedal sendsuccess(::common_msgs_humble::msg::Actuator::_sendsuccess_type arg)
  {
    msg_.sendsuccess = std::move(arg);
    return Init_Actuator_brakepedal(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_timestamp
{
public:
  explicit Init_Actuator_timestamp(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_sendsuccess timestamp(::common_msgs_humble::msg::Actuator::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return Init_Actuator_sendsuccess(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_isvalid
{
public:
  explicit Init_Actuator_isvalid(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_timestamp isvalid(::common_msgs_humble::msg::Actuator::_isvalid_type arg)
  {
    msg_.isvalid = std::move(arg);
    return Init_Actuator_timestamp(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_door
{
public:
  explicit Init_Actuator_door(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_isvalid door(::common_msgs_humble::msg::Actuator::_door_type arg)
  {
    msg_.door = std::move(arg);
    return Init_Actuator_isvalid(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_epb
{
public:
  explicit Init_Actuator_epb(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_door epb(::common_msgs_humble::msg::Actuator::_epb_type arg)
  {
    msg_.epb = std::move(arg);
    return Init_Actuator_door(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_gear
{
public:
  explicit Init_Actuator_gear(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_epb gear(::common_msgs_humble::msg::Actuator::_gear_type arg)
  {
    msg_.gear = std::move(arg);
    return Init_Actuator_epb(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_turnlight
{
public:
  explicit Init_Actuator_turnlight(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_gear turnlight(::common_msgs_humble::msg::Actuator::_turnlight_type arg)
  {
    msg_.turnlight = std::move(arg);
    return Init_Actuator_gear(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_lights
{
public:
  explicit Init_Actuator_lights(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_turnlight lights(::common_msgs_humble::msg::Actuator::_lights_type arg)
  {
    msg_.lights = std::move(arg);
    return Init_Actuator_turnlight(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_speed
{
public:
  explicit Init_Actuator_speed(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_lights speed(::common_msgs_humble::msg::Actuator::_speed_type arg)
  {
    msg_.speed = std::move(arg);
    return Init_Actuator_lights(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_sysstatus
{
public:
  explicit Init_Actuator_sysstatus(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_speed sysstatus(::common_msgs_humble::msg::Actuator::_sysstatus_type arg)
  {
    msg_.sysstatus = std::move(arg);
    return Init_Actuator_speed(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_gaspedal
{
public:
  explicit Init_Actuator_gaspedal(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_sysstatus gaspedal(::common_msgs_humble::msg::Actuator::_gaspedal_type arg)
  {
    msg_.gaspedal = std::move(arg);
    return Init_Actuator_sysstatus(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_escbrakepress
{
public:
  explicit Init_Actuator_escbrakepress(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_gaspedal escbrakepress(::common_msgs_humble::msg::Actuator::_escbrakepress_type arg)
  {
    msg_.escbrakepress = std::move(arg);
    return Init_Actuator_gaspedal(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_espmethod
{
public:
  explicit Init_Actuator_espmethod(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_escbrakepress espmethod(::common_msgs_humble::msg::Actuator::_espmethod_type arg)
  {
    msg_.espmethod = std::move(arg);
    return Init_Actuator_escbrakepress(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_epsangle
{
public:
  explicit Init_Actuator_epsangle(::common_msgs_humble::msg::Actuator & msg)
  : msg_(msg)
  {}
  Init_Actuator_espmethod epsangle(::common_msgs_humble::msg::Actuator::_epsangle_type arg)
  {
    msg_.epsangle = std::move(arg);
    return Init_Actuator_espmethod(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

class Init_Actuator_epsmethod
{
public:
  Init_Actuator_epsmethod()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Actuator_epsangle epsmethod(::common_msgs_humble::msg::Actuator::_epsmethod_type arg)
  {
    msg_.epsmethod = std::move(arg);
    return Init_Actuator_epsangle(msg_);
  }

private:
  ::common_msgs_humble::msg::Actuator msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Actuator>()
{
  return common_msgs_humble::msg::builder::Init_Actuator_epsmethod();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__ACTUATOR__BUILDER_HPP_
