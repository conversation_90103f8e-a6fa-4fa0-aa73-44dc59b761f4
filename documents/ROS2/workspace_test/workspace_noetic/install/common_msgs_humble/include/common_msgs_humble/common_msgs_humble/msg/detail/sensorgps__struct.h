﻿// NOLINT: This file starts with a BOM since it contain non-ASCII characters
// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Sensorgps.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSORGPS__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSORGPS__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

/// Struct defined in msg/Sensorgps in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Sensorgps
{
  /// 纬度
  double lon;
  /// 经度
  double lat;
  /// 高度
  double alt;
  /// 道路属性
  uint8_t roadtype;
  /// 当前车道|总车道(4|4)
  uint8_t lanetype;
  /// 航向角 degree
  double heading;
  /// 俯仰角
  double pitch;
  /// 横滚角
  double roll;
  /// deg/s
  double pitchrate;
  /// deg/s
  double rollrate;
  /// deg/s
  double yawrate;
  /// m/s^2
  double accx;
  /// m/s^2
  double accy;
  /// m/s^2
  double accz;
  /// 里程 m
  double mile;
  /// 速度
  double velocity;
  /// 导航状态
  uint8_t status;
  uint8_t rawstatus;
  /// 卫星个数
  uint8_t satenum;
  /// gps时间
  int64_t gpstime;
  /// 有效位
  uint8_t isvalid;
  /// 时间戳
  int64_t timestamp;
  /// X轴速度 韩双全 20220926
  double speed_n;
  /// Y轴速度 韩双全 20220926
  double speed_e;
  /// Z轴速度 韩双全 20220926
  double speed_d;
} common_msgs_humble__msg__Sensorgps;

// Struct for a sequence of common_msgs_humble__msg__Sensorgps.
typedef struct common_msgs_humble__msg__Sensorgps__Sequence
{
  common_msgs_humble__msg__Sensorgps * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Sensorgps__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSORGPS__STRUCT_H_
