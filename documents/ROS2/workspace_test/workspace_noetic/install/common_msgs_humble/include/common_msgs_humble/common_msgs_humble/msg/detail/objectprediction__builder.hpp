// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Objectprediction.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__OBJECTPREDICTION__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__OBJECTPREDICTION__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/objectprediction__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Objectprediction_speedl
{
public:
  explicit Init_Objectprediction_speedl(::common_msgs_humble::msg::Objectprediction & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Objectprediction speedl(::common_msgs_humble::msg::Objectprediction::_speedl_type arg)
  {
    msg_.speedl = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Objectprediction msg_;
};

class Init_Objectprediction_speeds
{
public:
  explicit Init_Objectprediction_speeds(::common_msgs_humble::msg::Objectprediction & msg)
  : msg_(msg)
  {}
  Init_Objectprediction_speedl speeds(::common_msgs_humble::msg::Objectprediction::_speeds_type arg)
  {
    msg_.speeds = std::move(arg);
    return Init_Objectprediction_speedl(msg_);
  }

private:
  ::common_msgs_humble::msg::Objectprediction msg_;
};

class Init_Objectprediction_l
{
public:
  explicit Init_Objectprediction_l(::common_msgs_humble::msg::Objectprediction & msg)
  : msg_(msg)
  {}
  Init_Objectprediction_speeds l(::common_msgs_humble::msg::Objectprediction::_l_type arg)
  {
    msg_.l = std::move(arg);
    return Init_Objectprediction_speeds(msg_);
  }

private:
  ::common_msgs_humble::msg::Objectprediction msg_;
};

class Init_Objectprediction_s
{
public:
  explicit Init_Objectprediction_s(::common_msgs_humble::msg::Objectprediction & msg)
  : msg_(msg)
  {}
  Init_Objectprediction_l s(::common_msgs_humble::msg::Objectprediction::_s_type arg)
  {
    msg_.s = std::move(arg);
    return Init_Objectprediction_l(msg_);
  }

private:
  ::common_msgs_humble::msg::Objectprediction msg_;
};

class Init_Objectprediction_heading
{
public:
  explicit Init_Objectprediction_heading(::common_msgs_humble::msg::Objectprediction & msg)
  : msg_(msg)
  {}
  Init_Objectprediction_s heading(::common_msgs_humble::msg::Objectprediction::_heading_type arg)
  {
    msg_.heading = std::move(arg);
    return Init_Objectprediction_s(msg_);
  }

private:
  ::common_msgs_humble::msg::Objectprediction msg_;
};

class Init_Objectprediction_absvy
{
public:
  explicit Init_Objectprediction_absvy(::common_msgs_humble::msg::Objectprediction & msg)
  : msg_(msg)
  {}
  Init_Objectprediction_heading absvy(::common_msgs_humble::msg::Objectprediction::_absvy_type arg)
  {
    msg_.absvy = std::move(arg);
    return Init_Objectprediction_heading(msg_);
  }

private:
  ::common_msgs_humble::msg::Objectprediction msg_;
};

class Init_Objectprediction_absvx
{
public:
  explicit Init_Objectprediction_absvx(::common_msgs_humble::msg::Objectprediction & msg)
  : msg_(msg)
  {}
  Init_Objectprediction_absvy absvx(::common_msgs_humble::msg::Objectprediction::_absvx_type arg)
  {
    msg_.absvx = std::move(arg);
    return Init_Objectprediction_absvy(msg_);
  }

private:
  ::common_msgs_humble::msg::Objectprediction msg_;
};

class Init_Objectprediction_relavy
{
public:
  explicit Init_Objectprediction_relavy(::common_msgs_humble::msg::Objectprediction & msg)
  : msg_(msg)
  {}
  Init_Objectprediction_absvx relavy(::common_msgs_humble::msg::Objectprediction::_relavy_type arg)
  {
    msg_.relavy = std::move(arg);
    return Init_Objectprediction_absvx(msg_);
  }

private:
  ::common_msgs_humble::msg::Objectprediction msg_;
};

class Init_Objectprediction_relavx
{
public:
  explicit Init_Objectprediction_relavx(::common_msgs_humble::msg::Objectprediction & msg)
  : msg_(msg)
  {}
  Init_Objectprediction_relavy relavx(::common_msgs_humble::msg::Objectprediction::_relavx_type arg)
  {
    msg_.relavx = std::move(arg);
    return Init_Objectprediction_relavy(msg_);
  }

private:
  ::common_msgs_humble::msg::Objectprediction msg_;
};

class Init_Objectprediction_azimuth
{
public:
  explicit Init_Objectprediction_azimuth(::common_msgs_humble::msg::Objectprediction & msg)
  : msg_(msg)
  {}
  Init_Objectprediction_relavx azimuth(::common_msgs_humble::msg::Objectprediction::_azimuth_type arg)
  {
    msg_.azimuth = std::move(arg);
    return Init_Objectprediction_relavx(msg_);
  }

private:
  ::common_msgs_humble::msg::Objectprediction msg_;
};

class Init_Objectprediction_pitchrad
{
public:
  explicit Init_Objectprediction_pitchrad(::common_msgs_humble::msg::Objectprediction & msg)
  : msg_(msg)
  {}
  Init_Objectprediction_azimuth pitchrad(::common_msgs_humble::msg::Objectprediction::_pitchrad_type arg)
  {
    msg_.pitchrad = std::move(arg);
    return Init_Objectprediction_azimuth(msg_);
  }

private:
  ::common_msgs_humble::msg::Objectprediction msg_;
};

class Init_Objectprediction_rollrad
{
public:
  explicit Init_Objectprediction_rollrad(::common_msgs_humble::msg::Objectprediction & msg)
  : msg_(msg)
  {}
  Init_Objectprediction_pitchrad rollrad(::common_msgs_humble::msg::Objectprediction::_rollrad_type arg)
  {
    msg_.rollrad = std::move(arg);
    return Init_Objectprediction_pitchrad(msg_);
  }

private:
  ::common_msgs_humble::msg::Objectprediction msg_;
};

class Init_Objectprediction_altitude
{
public:
  explicit Init_Objectprediction_altitude(::common_msgs_humble::msg::Objectprediction & msg)
  : msg_(msg)
  {}
  Init_Objectprediction_rollrad altitude(::common_msgs_humble::msg::Objectprediction::_altitude_type arg)
  {
    msg_.altitude = std::move(arg);
    return Init_Objectprediction_rollrad(msg_);
  }

private:
  ::common_msgs_humble::msg::Objectprediction msg_;
};

class Init_Objectprediction_latitude
{
public:
  explicit Init_Objectprediction_latitude(::common_msgs_humble::msg::Objectprediction & msg)
  : msg_(msg)
  {}
  Init_Objectprediction_altitude latitude(::common_msgs_humble::msg::Objectprediction::_latitude_type arg)
  {
    msg_.latitude = std::move(arg);
    return Init_Objectprediction_altitude(msg_);
  }

private:
  ::common_msgs_humble::msg::Objectprediction msg_;
};

class Init_Objectprediction_longtitude
{
public:
  explicit Init_Objectprediction_longtitude(::common_msgs_humble::msg::Objectprediction & msg)
  : msg_(msg)
  {}
  Init_Objectprediction_latitude longtitude(::common_msgs_humble::msg::Objectprediction::_longtitude_type arg)
  {
    msg_.longtitude = std::move(arg);
    return Init_Objectprediction_latitude(msg_);
  }

private:
  ::common_msgs_humble::msg::Objectprediction msg_;
};

class Init_Objectprediction_z
{
public:
  explicit Init_Objectprediction_z(::common_msgs_humble::msg::Objectprediction & msg)
  : msg_(msg)
  {}
  Init_Objectprediction_longtitude z(::common_msgs_humble::msg::Objectprediction::_z_type arg)
  {
    msg_.z = std::move(arg);
    return Init_Objectprediction_longtitude(msg_);
  }

private:
  ::common_msgs_humble::msg::Objectprediction msg_;
};

class Init_Objectprediction_y
{
public:
  explicit Init_Objectprediction_y(::common_msgs_humble::msg::Objectprediction & msg)
  : msg_(msg)
  {}
  Init_Objectprediction_z y(::common_msgs_humble::msg::Objectprediction::_y_type arg)
  {
    msg_.y = std::move(arg);
    return Init_Objectprediction_z(msg_);
  }

private:
  ::common_msgs_humble::msg::Objectprediction msg_;
};

class Init_Objectprediction_x
{
public:
  explicit Init_Objectprediction_x(::common_msgs_humble::msg::Objectprediction & msg)
  : msg_(msg)
  {}
  Init_Objectprediction_y x(::common_msgs_humble::msg::Objectprediction::_x_type arg)
  {
    msg_.x = std::move(arg);
    return Init_Objectprediction_y(msg_);
  }

private:
  ::common_msgs_humble::msg::Objectprediction msg_;
};

class Init_Objectprediction_timestep
{
public:
  Init_Objectprediction_timestep()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Objectprediction_x timestep(::common_msgs_humble::msg::Objectprediction::_timestep_type arg)
  {
    msg_.timestep = std::move(arg);
    return Init_Objectprediction_x(msg_);
  }

private:
  ::common_msgs_humble::msg::Objectprediction msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Objectprediction>()
{
  return common_msgs_humble::msg::builder::Init_Objectprediction_timestep();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__OBJECTPREDICTION__BUILDER_HPP_
