// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Oburoadpoint.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUROADPOINT__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUROADPOINT__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/oburoadpoint__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Oburoadpoint_availability
{
public:
  explicit Init_Oburoadpoint_availability(::common_msgs_humble::msg::Oburoadpoint & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Oburoadpoint availability(::common_msgs_humble::msg::Oburoadpoint::_availability_type arg)
  {
    msg_.availability = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Oburoadpoint msg_;
};

class Init_Oburoadpoint_heading
{
public:
  explicit Init_Oburoadpoint_heading(::common_msgs_humble::msg::Oburoadpoint & msg)
  : msg_(msg)
  {}
  Init_Oburoadpoint_availability heading(::common_msgs_humble::msg::Oburoadpoint::_heading_type arg)
  {
    msg_.heading = std::move(arg);
    return Init_Oburoadpoint_availability(msg_);
  }

private:
  ::common_msgs_humble::msg::Oburoadpoint msg_;
};

class Init_Oburoadpoint_accel
{
public:
  explicit Init_Oburoadpoint_accel(::common_msgs_humble::msg::Oburoadpoint & msg)
  : msg_(msg)
  {}
  Init_Oburoadpoint_heading accel(::common_msgs_humble::msg::Oburoadpoint::_accel_type arg)
  {
    msg_.accel = std::move(arg);
    return Init_Oburoadpoint_heading(msg_);
  }

private:
  ::common_msgs_humble::msg::Oburoadpoint msg_;
};

class Init_Oburoadpoint_speed
{
public:
  explicit Init_Oburoadpoint_speed(::common_msgs_humble::msg::Oburoadpoint & msg)
  : msg_(msg)
  {}
  Init_Oburoadpoint_accel speed(::common_msgs_humble::msg::Oburoadpoint::_speed_type arg)
  {
    msg_.speed = std::move(arg);
    return Init_Oburoadpoint_accel(msg_);
  }

private:
  ::common_msgs_humble::msg::Oburoadpoint msg_;
};

class Init_Oburoadpoint_lat
{
public:
  explicit Init_Oburoadpoint_lat(::common_msgs_humble::msg::Oburoadpoint & msg)
  : msg_(msg)
  {}
  Init_Oburoadpoint_speed lat(::common_msgs_humble::msg::Oburoadpoint::_lat_type arg)
  {
    msg_.lat = std::move(arg);
    return Init_Oburoadpoint_speed(msg_);
  }

private:
  ::common_msgs_humble::msg::Oburoadpoint msg_;
};

class Init_Oburoadpoint_lon
{
public:
  Init_Oburoadpoint_lon()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Oburoadpoint_lat lon(::common_msgs_humble::msg::Oburoadpoint::_lon_type arg)
  {
    msg_.lon = std::move(arg);
    return Init_Oburoadpoint_lat(msg_);
  }

private:
  ::common_msgs_humble::msg::Oburoadpoint msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Oburoadpoint>()
{
  return common_msgs_humble::msg::builder::Init_Oburoadpoint_lon();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUROADPOINT__BUILDER_HPP_
