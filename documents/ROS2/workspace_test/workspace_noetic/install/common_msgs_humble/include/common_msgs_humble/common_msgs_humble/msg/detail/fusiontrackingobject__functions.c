// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from common_msgs_humble:msg/Fusiontrackingobject.idl
// generated code does not contain a copyright notice
#include "common_msgs_humble/msg/detail/fusiontrackingobject__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


// Include directives for member types
// Member `lidarobject`
// Member `radarobject`
// Member `obuobject`
#include "common_msgs_humble/msg/detail/sensorobject__functions.h"
// Member `obupantobject`
#include "common_msgs_humble/msg/detail/obupant__functions.h"

bool
common_msgs_humble__msg__Fusiontrackingobject__init(common_msgs_humble__msg__Fusiontrackingobject * msg)
{
  if (!msg) {
    return false;
  }
  // objectsource
  // lidartimestamp
  // lidargpstime
  // lidarobject
  if (!common_msgs_humble__msg__Sensorobject__init(&msg->lidarobject)) {
    common_msgs_humble__msg__Fusiontrackingobject__fini(msg);
    return false;
  }
  // radartimestamp
  // radargpstime
  // radarobject
  if (!common_msgs_humble__msg__Sensorobject__init(&msg->radarobject)) {
    common_msgs_humble__msg__Fusiontrackingobject__fini(msg);
    return false;
  }
  // obutimestamp
  // obugpstime
  // obuobject
  if (!common_msgs_humble__msg__Sensorobject__init(&msg->obuobject)) {
    common_msgs_humble__msg__Fusiontrackingobject__fini(msg);
    return false;
  }
  // obupantobject
  if (!common_msgs_humble__msg__Obupant__init(&msg->obupantobject)) {
    common_msgs_humble__msg__Fusiontrackingobject__fini(msg);
    return false;
  }
  return true;
}

void
common_msgs_humble__msg__Fusiontrackingobject__fini(common_msgs_humble__msg__Fusiontrackingobject * msg)
{
  if (!msg) {
    return;
  }
  // objectsource
  // lidartimestamp
  // lidargpstime
  // lidarobject
  common_msgs_humble__msg__Sensorobject__fini(&msg->lidarobject);
  // radartimestamp
  // radargpstime
  // radarobject
  common_msgs_humble__msg__Sensorobject__fini(&msg->radarobject);
  // obutimestamp
  // obugpstime
  // obuobject
  common_msgs_humble__msg__Sensorobject__fini(&msg->obuobject);
  // obupantobject
  common_msgs_humble__msg__Obupant__fini(&msg->obupantobject);
}

bool
common_msgs_humble__msg__Fusiontrackingobject__are_equal(const common_msgs_humble__msg__Fusiontrackingobject * lhs, const common_msgs_humble__msg__Fusiontrackingobject * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // objectsource
  if (lhs->objectsource != rhs->objectsource) {
    return false;
  }
  // lidartimestamp
  if (lhs->lidartimestamp != rhs->lidartimestamp) {
    return false;
  }
  // lidargpstime
  if (lhs->lidargpstime != rhs->lidargpstime) {
    return false;
  }
  // lidarobject
  if (!common_msgs_humble__msg__Sensorobject__are_equal(
      &(lhs->lidarobject), &(rhs->lidarobject)))
  {
    return false;
  }
  // radartimestamp
  if (lhs->radartimestamp != rhs->radartimestamp) {
    return false;
  }
  // radargpstime
  if (lhs->radargpstime != rhs->radargpstime) {
    return false;
  }
  // radarobject
  if (!common_msgs_humble__msg__Sensorobject__are_equal(
      &(lhs->radarobject), &(rhs->radarobject)))
  {
    return false;
  }
  // obutimestamp
  if (lhs->obutimestamp != rhs->obutimestamp) {
    return false;
  }
  // obugpstime
  if (lhs->obugpstime != rhs->obugpstime) {
    return false;
  }
  // obuobject
  if (!common_msgs_humble__msg__Sensorobject__are_equal(
      &(lhs->obuobject), &(rhs->obuobject)))
  {
    return false;
  }
  // obupantobject
  if (!common_msgs_humble__msg__Obupant__are_equal(
      &(lhs->obupantobject), &(rhs->obupantobject)))
  {
    return false;
  }
  return true;
}

bool
common_msgs_humble__msg__Fusiontrackingobject__copy(
  const common_msgs_humble__msg__Fusiontrackingobject * input,
  common_msgs_humble__msg__Fusiontrackingobject * output)
{
  if (!input || !output) {
    return false;
  }
  // objectsource
  output->objectsource = input->objectsource;
  // lidartimestamp
  output->lidartimestamp = input->lidartimestamp;
  // lidargpstime
  output->lidargpstime = input->lidargpstime;
  // lidarobject
  if (!common_msgs_humble__msg__Sensorobject__copy(
      &(input->lidarobject), &(output->lidarobject)))
  {
    return false;
  }
  // radartimestamp
  output->radartimestamp = input->radartimestamp;
  // radargpstime
  output->radargpstime = input->radargpstime;
  // radarobject
  if (!common_msgs_humble__msg__Sensorobject__copy(
      &(input->radarobject), &(output->radarobject)))
  {
    return false;
  }
  // obutimestamp
  output->obutimestamp = input->obutimestamp;
  // obugpstime
  output->obugpstime = input->obugpstime;
  // obuobject
  if (!common_msgs_humble__msg__Sensorobject__copy(
      &(input->obuobject), &(output->obuobject)))
  {
    return false;
  }
  // obupantobject
  if (!common_msgs_humble__msg__Obupant__copy(
      &(input->obupantobject), &(output->obupantobject)))
  {
    return false;
  }
  return true;
}

common_msgs_humble__msg__Fusiontrackingobject *
common_msgs_humble__msg__Fusiontrackingobject__create()
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Fusiontrackingobject * msg = (common_msgs_humble__msg__Fusiontrackingobject *)allocator.allocate(sizeof(common_msgs_humble__msg__Fusiontrackingobject), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(common_msgs_humble__msg__Fusiontrackingobject));
  bool success = common_msgs_humble__msg__Fusiontrackingobject__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
common_msgs_humble__msg__Fusiontrackingobject__destroy(common_msgs_humble__msg__Fusiontrackingobject * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    common_msgs_humble__msg__Fusiontrackingobject__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
common_msgs_humble__msg__Fusiontrackingobject__Sequence__init(common_msgs_humble__msg__Fusiontrackingobject__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Fusiontrackingobject * data = NULL;

  if (size) {
    data = (common_msgs_humble__msg__Fusiontrackingobject *)allocator.zero_allocate(size, sizeof(common_msgs_humble__msg__Fusiontrackingobject), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = common_msgs_humble__msg__Fusiontrackingobject__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        common_msgs_humble__msg__Fusiontrackingobject__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
common_msgs_humble__msg__Fusiontrackingobject__Sequence__fini(common_msgs_humble__msg__Fusiontrackingobject__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      common_msgs_humble__msg__Fusiontrackingobject__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

common_msgs_humble__msg__Fusiontrackingobject__Sequence *
common_msgs_humble__msg__Fusiontrackingobject__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Fusiontrackingobject__Sequence * array = (common_msgs_humble__msg__Fusiontrackingobject__Sequence *)allocator.allocate(sizeof(common_msgs_humble__msg__Fusiontrackingobject__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = common_msgs_humble__msg__Fusiontrackingobject__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
common_msgs_humble__msg__Fusiontrackingobject__Sequence__destroy(common_msgs_humble__msg__Fusiontrackingobject__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    common_msgs_humble__msg__Fusiontrackingobject__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
common_msgs_humble__msg__Fusiontrackingobject__Sequence__are_equal(const common_msgs_humble__msg__Fusiontrackingobject__Sequence * lhs, const common_msgs_humble__msg__Fusiontrackingobject__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!common_msgs_humble__msg__Fusiontrackingobject__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
common_msgs_humble__msg__Fusiontrackingobject__Sequence__copy(
  const common_msgs_humble__msg__Fusiontrackingobject__Sequence * input,
  common_msgs_humble__msg__Fusiontrackingobject__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(common_msgs_humble__msg__Fusiontrackingobject);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    common_msgs_humble__msg__Fusiontrackingobject * data =
      (common_msgs_humble__msg__Fusiontrackingobject *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!common_msgs_humble__msg__Fusiontrackingobject__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          common_msgs_humble__msg__Fusiontrackingobject__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!common_msgs_humble__msg__Fusiontrackingobject__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
