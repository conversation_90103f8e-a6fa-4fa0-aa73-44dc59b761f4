// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from common_msgs_humble:msg/Cloudpant.idl
// generated code does not contain a copyright notice
#include "common_msgs_humble/msg/detail/cloudpant__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


// Include directives for member types
// Member `id`
#include "rosidl_runtime_c/string_functions.h"

bool
common_msgs_humble__msg__Cloudpant__init(common_msgs_humble__msg__Cloudpant * msg)
{
  if (!msg) {
    return false;
  }
  // id
  if (!rosidl_runtime_c__String__init(&msg->id)) {
    common_msgs_humble__msg__Cloudpant__fini(msg);
    return false;
  }
  // vehicletype
  // length
  // width
  // height
  // longitude
  // latitude
  // locationconfidence
  // speed
  // courseangle
  // sportconfidence
  return true;
}

void
common_msgs_humble__msg__Cloudpant__fini(common_msgs_humble__msg__Cloudpant * msg)
{
  if (!msg) {
    return;
  }
  // id
  rosidl_runtime_c__String__fini(&msg->id);
  // vehicletype
  // length
  // width
  // height
  // longitude
  // latitude
  // locationconfidence
  // speed
  // courseangle
  // sportconfidence
}

bool
common_msgs_humble__msg__Cloudpant__are_equal(const common_msgs_humble__msg__Cloudpant * lhs, const common_msgs_humble__msg__Cloudpant * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // id
  if (!rosidl_runtime_c__String__are_equal(
      &(lhs->id), &(rhs->id)))
  {
    return false;
  }
  // vehicletype
  if (lhs->vehicletype != rhs->vehicletype) {
    return false;
  }
  // length
  if (lhs->length != rhs->length) {
    return false;
  }
  // width
  if (lhs->width != rhs->width) {
    return false;
  }
  // height
  if (lhs->height != rhs->height) {
    return false;
  }
  // longitude
  if (lhs->longitude != rhs->longitude) {
    return false;
  }
  // latitude
  if (lhs->latitude != rhs->latitude) {
    return false;
  }
  // locationconfidence
  if (lhs->locationconfidence != rhs->locationconfidence) {
    return false;
  }
  // speed
  if (lhs->speed != rhs->speed) {
    return false;
  }
  // courseangle
  if (lhs->courseangle != rhs->courseangle) {
    return false;
  }
  // sportconfidence
  if (lhs->sportconfidence != rhs->sportconfidence) {
    return false;
  }
  return true;
}

bool
common_msgs_humble__msg__Cloudpant__copy(
  const common_msgs_humble__msg__Cloudpant * input,
  common_msgs_humble__msg__Cloudpant * output)
{
  if (!input || !output) {
    return false;
  }
  // id
  if (!rosidl_runtime_c__String__copy(
      &(input->id), &(output->id)))
  {
    return false;
  }
  // vehicletype
  output->vehicletype = input->vehicletype;
  // length
  output->length = input->length;
  // width
  output->width = input->width;
  // height
  output->height = input->height;
  // longitude
  output->longitude = input->longitude;
  // latitude
  output->latitude = input->latitude;
  // locationconfidence
  output->locationconfidence = input->locationconfidence;
  // speed
  output->speed = input->speed;
  // courseangle
  output->courseangle = input->courseangle;
  // sportconfidence
  output->sportconfidence = input->sportconfidence;
  return true;
}

common_msgs_humble__msg__Cloudpant *
common_msgs_humble__msg__Cloudpant__create()
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Cloudpant * msg = (common_msgs_humble__msg__Cloudpant *)allocator.allocate(sizeof(common_msgs_humble__msg__Cloudpant), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(common_msgs_humble__msg__Cloudpant));
  bool success = common_msgs_humble__msg__Cloudpant__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
common_msgs_humble__msg__Cloudpant__destroy(common_msgs_humble__msg__Cloudpant * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    common_msgs_humble__msg__Cloudpant__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
common_msgs_humble__msg__Cloudpant__Sequence__init(common_msgs_humble__msg__Cloudpant__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Cloudpant * data = NULL;

  if (size) {
    data = (common_msgs_humble__msg__Cloudpant *)allocator.zero_allocate(size, sizeof(common_msgs_humble__msg__Cloudpant), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = common_msgs_humble__msg__Cloudpant__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        common_msgs_humble__msg__Cloudpant__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
common_msgs_humble__msg__Cloudpant__Sequence__fini(common_msgs_humble__msg__Cloudpant__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      common_msgs_humble__msg__Cloudpant__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

common_msgs_humble__msg__Cloudpant__Sequence *
common_msgs_humble__msg__Cloudpant__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Cloudpant__Sequence * array = (common_msgs_humble__msg__Cloudpant__Sequence *)allocator.allocate(sizeof(common_msgs_humble__msg__Cloudpant__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = common_msgs_humble__msg__Cloudpant__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
common_msgs_humble__msg__Cloudpant__Sequence__destroy(common_msgs_humble__msg__Cloudpant__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    common_msgs_humble__msg__Cloudpant__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
common_msgs_humble__msg__Cloudpant__Sequence__are_equal(const common_msgs_humble__msg__Cloudpant__Sequence * lhs, const common_msgs_humble__msg__Cloudpant__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!common_msgs_humble__msg__Cloudpant__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
common_msgs_humble__msg__Cloudpant__Sequence__copy(
  const common_msgs_humble__msg__Cloudpant__Sequence * input,
  common_msgs_humble__msg__Cloudpant__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(common_msgs_humble__msg__Cloudpant);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    common_msgs_humble__msg__Cloudpant * data =
      (common_msgs_humble__msg__Cloudpant *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!common_msgs_humble__msg__Cloudpant__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          common_msgs_humble__msg__Cloudpant__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!common_msgs_humble__msg__Cloudpant__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
