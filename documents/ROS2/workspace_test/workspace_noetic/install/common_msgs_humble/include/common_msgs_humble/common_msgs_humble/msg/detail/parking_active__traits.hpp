// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from common_msgs_humble:msg/ParkingActive.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__PARKING_ACTIVE__TRAITS_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__PARKING_ACTIVE__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "common_msgs_humble/msg/detail/parking_active__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

// Include directives for member types
// Member 'iekulist'
#include "common_msgs_humble/msg/detail/ieku__traits.hpp"

namespace common_msgs_humble
{

namespace msg
{

inline void to_flow_style_yaml(
  const ParkingActive & msg,
  std::ostream & out)
{
  out << "{";
  // member: timestamp
  {
    out << "timestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.timestamp, out);
    out << ", ";
  }

  // member: stage
  {
    out << "stage: ";
    rosidl_generator_traits::value_to_yaml(msg.stage, out);
    out << ", ";
  }

  // member: tips
  {
    out << "tips: ";
    rosidl_generator_traits::value_to_yaml(msg.tips, out);
    out << ", ";
  }

  // member: answer
  {
    out << "answer: ";
    rosidl_generator_traits::value_to_yaml(msg.answer, out);
    out << ", ";
  }

  // member: iekulist
  {
    if (msg.iekulist.size() == 0) {
      out << "iekulist: []";
    } else {
      out << "iekulist: [";
      size_t pending_items = msg.iekulist.size();
      for (auto item : msg.iekulist) {
        to_flow_style_yaml(item, out);
        if (--pending_items > 0) {
          out << ", ";
        }
      }
      out << "]";
    }
    out << ", ";
  }

  // member: iekutargetid
  {
    out << "iekutargetid: ";
    rosidl_generator_traits::value_to_yaml(msg.iekutargetid, out);
    out << ", ";
  }

  // member: driveout
  {
    out << "driveout: ";
    rosidl_generator_traits::value_to_yaml(msg.driveout, out);
    out << ", ";
  }

  // member: stop_parking
  {
    out << "stop_parking: ";
    rosidl_generator_traits::value_to_yaml(msg.stop_parking, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const ParkingActive & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: timestamp
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "timestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.timestamp, out);
    out << "\n";
  }

  // member: stage
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "stage: ";
    rosidl_generator_traits::value_to_yaml(msg.stage, out);
    out << "\n";
  }

  // member: tips
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "tips: ";
    rosidl_generator_traits::value_to_yaml(msg.tips, out);
    out << "\n";
  }

  // member: answer
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "answer: ";
    rosidl_generator_traits::value_to_yaml(msg.answer, out);
    out << "\n";
  }

  // member: iekulist
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    if (msg.iekulist.size() == 0) {
      out << "iekulist: []\n";
    } else {
      out << "iekulist:\n";
      for (auto item : msg.iekulist) {
        if (indentation > 0) {
          out << std::string(indentation, ' ');
        }
        out << "-\n";
        to_block_style_yaml(item, out, indentation + 2);
      }
    }
  }

  // member: iekutargetid
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "iekutargetid: ";
    rosidl_generator_traits::value_to_yaml(msg.iekutargetid, out);
    out << "\n";
  }

  // member: driveout
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "driveout: ";
    rosidl_generator_traits::value_to_yaml(msg.driveout, out);
    out << "\n";
  }

  // member: stop_parking
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "stop_parking: ";
    rosidl_generator_traits::value_to_yaml(msg.stop_parking, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const ParkingActive & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace common_msgs_humble

namespace rosidl_generator_traits
{

[[deprecated("use common_msgs_humble::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const common_msgs_humble::msg::ParkingActive & msg,
  std::ostream & out, size_t indentation = 0)
{
  common_msgs_humble::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use common_msgs_humble::msg::to_yaml() instead")]]
inline std::string to_yaml(const common_msgs_humble::msg::ParkingActive & msg)
{
  return common_msgs_humble::msg::to_yaml(msg);
}

template<>
inline const char * data_type<common_msgs_humble::msg::ParkingActive>()
{
  return "common_msgs_humble::msg::ParkingActive";
}

template<>
inline const char * name<common_msgs_humble::msg::ParkingActive>()
{
  return "common_msgs_humble/msg/ParkingActive";
}

template<>
struct has_fixed_size<common_msgs_humble::msg::ParkingActive>
  : std::integral_constant<bool, false> {};

template<>
struct has_bounded_size<common_msgs_humble::msg::ParkingActive>
  : std::integral_constant<bool, false> {};

template<>
struct is_message<common_msgs_humble::msg::ParkingActive>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__PARKING_ACTIVE__TRAITS_HPP_
