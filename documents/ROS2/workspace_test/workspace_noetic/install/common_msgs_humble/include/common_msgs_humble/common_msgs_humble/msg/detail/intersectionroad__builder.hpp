// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Intersectionroad.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__INTERSECTIONROAD__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__INTERSECTIONROAD__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/intersectionroad__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Intersectionroad_lane
{
public:
  Init_Intersectionroad_lane()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  ::common_msgs_humble::msg::Intersectionroad lane(::common_msgs_humble::msg::Intersectionroad::_lane_type arg)
  {
    msg_.lane = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Intersectionroad msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Intersectionroad>()
{
  return common_msgs_humble::msg::builder::Init_Intersectionroad_lane();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__INTERSECTIONROAD__BUILDER_HPP_
