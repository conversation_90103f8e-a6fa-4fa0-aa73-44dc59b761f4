// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from common_msgs_humble:msg/Intersectionroad.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__INTERSECTIONROAD__TRAITS_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__INTERSECTIONROAD__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "common_msgs_humble/msg/detail/intersectionroad__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

// Include directives for member types
// Member 'lane'
#include "common_msgs_humble/msg/detail/roadpoint__traits.hpp"

namespace common_msgs_humble
{

namespace msg
{

inline void to_flow_style_yaml(
  const Intersectionroad & msg,
  std::ostream & out)
{
  out << "{";
  // member: lane
  {
    if (msg.lane.size() == 0) {
      out << "lane: []";
    } else {
      out << "lane: [";
      size_t pending_items = msg.lane.size();
      for (auto item : msg.lane) {
        to_flow_style_yaml(item, out);
        if (--pending_items > 0) {
          out << ", ";
        }
      }
      out << "]";
    }
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const Intersectionroad & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: lane
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    if (msg.lane.size() == 0) {
      out << "lane: []\n";
    } else {
      out << "lane:\n";
      for (auto item : msg.lane) {
        if (indentation > 0) {
          out << std::string(indentation, ' ');
        }
        out << "-\n";
        to_block_style_yaml(item, out, indentation + 2);
      }
    }
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const Intersectionroad & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace common_msgs_humble

namespace rosidl_generator_traits
{

[[deprecated("use common_msgs_humble::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const common_msgs_humble::msg::Intersectionroad & msg,
  std::ostream & out, size_t indentation = 0)
{
  common_msgs_humble::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use common_msgs_humble::msg::to_yaml() instead")]]
inline std::string to_yaml(const common_msgs_humble::msg::Intersectionroad & msg)
{
  return common_msgs_humble::msg::to_yaml(msg);
}

template<>
inline const char * data_type<common_msgs_humble::msg::Intersectionroad>()
{
  return "common_msgs_humble::msg::Intersectionroad";
}

template<>
inline const char * name<common_msgs_humble::msg::Intersectionroad>()
{
  return "common_msgs_humble/msg/Intersectionroad";
}

template<>
struct has_fixed_size<common_msgs_humble::msg::Intersectionroad>
  : std::integral_constant<bool, false> {};

template<>
struct has_bounded_size<common_msgs_humble::msg::Intersectionroad>
  : std::integral_constant<bool, false> {};

template<>
struct is_message<common_msgs_humble::msg::Intersectionroad>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__INTERSECTIONROAD__TRAITS_HPP_
