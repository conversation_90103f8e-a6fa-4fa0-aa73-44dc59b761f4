// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Padtohd.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__PADTOHD__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__PADTOHD__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Padtohd __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Padtohd __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Padtohd_
{
  using Type = Padtohd_<ContainerAllocator>;

  explicit Padtohd_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->index = 0;
      this->timestamp = 0ll;
      this->path = "";
    }
  }

  explicit Padtohd_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : path(_alloc)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->index = 0;
      this->timestamp = 0ll;
      this->path = "";
    }
  }

  // field types and members
  using _index_type =
    uint8_t;
  _index_type index;
  using _timestamp_type =
    int64_t;
  _timestamp_type timestamp;
  using _path_type =
    std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>;
  _path_type path;

  // setters for named parameter idiom
  Type & set__index(
    const uint8_t & _arg)
  {
    this->index = _arg;
    return *this;
  }
  Type & set__timestamp(
    const int64_t & _arg)
  {
    this->timestamp = _arg;
    return *this;
  }
  Type & set__path(
    const std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> & _arg)
  {
    this->path = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Padtohd_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Padtohd_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Padtohd_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Padtohd_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Padtohd_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Padtohd_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Padtohd_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Padtohd_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Padtohd_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Padtohd_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Padtohd
    std::shared_ptr<common_msgs_humble::msg::Padtohd_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Padtohd
    std::shared_ptr<common_msgs_humble::msg::Padtohd_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Padtohd_ & other) const
  {
    if (this->index != other.index) {
      return false;
    }
    if (this->timestamp != other.timestamp) {
      return false;
    }
    if (this->path != other.path) {
      return false;
    }
    return true;
  }
  bool operator!=(const Padtohd_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Padtohd_

// alias to use template instance with default allocator
using Padtohd =
  common_msgs_humble::msg::Padtohd_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__PADTOHD__STRUCT_HPP_
