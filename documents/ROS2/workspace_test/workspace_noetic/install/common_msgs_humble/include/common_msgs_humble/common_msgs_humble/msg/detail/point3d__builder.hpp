// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Point3d.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__POINT3D__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__POINT3D__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/point3d__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Point3d_z
{
public:
  explicit Init_Point3d_z(::common_msgs_humble::msg::Point3d & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Point3d z(::common_msgs_humble::msg::Point3d::_z_type arg)
  {
    msg_.z = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Point3d msg_;
};

class Init_Point3d_y
{
public:
  explicit Init_Point3d_y(::common_msgs_humble::msg::Point3d & msg)
  : msg_(msg)
  {}
  Init_Point3d_z y(::common_msgs_humble::msg::Point3d::_y_type arg)
  {
    msg_.y = std::move(arg);
    return Init_Point3d_z(msg_);
  }

private:
  ::common_msgs_humble::msg::Point3d msg_;
};

class Init_Point3d_x
{
public:
  Init_Point3d_x()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Point3d_y x(::common_msgs_humble::msg::Point3d::_x_type arg)
  {
    msg_.x = std::move(arg);
    return Init_Point3d_y(msg_);
  }

private:
  ::common_msgs_humble::msg::Point3d msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Point3d>()
{
  return common_msgs_humble::msg::builder::Init_Point3d_x();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__POINT3D__BUILDER_HPP_
