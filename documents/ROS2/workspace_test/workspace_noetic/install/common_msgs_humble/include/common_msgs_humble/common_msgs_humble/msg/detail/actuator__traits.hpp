// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from common_msgs_humble:msg/Actuator.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__ACTUATOR__TRAITS_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__ACTUATOR__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "common_msgs_humble/msg/detail/actuator__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

namespace common_msgs_humble
{

namespace msg
{

inline void to_flow_style_yaml(
  const Actuator & msg,
  std::ostream & out)
{
  out << "{";
  // member: epsmethod
  {
    out << "epsmethod: ";
    rosidl_generator_traits::value_to_yaml(msg.epsmethod, out);
    out << ", ";
  }

  // member: epsangle
  {
    out << "epsangle: ";
    rosidl_generator_traits::value_to_yaml(msg.epsangle, out);
    out << ", ";
  }

  // member: espmethod
  {
    out << "espmethod: ";
    rosidl_generator_traits::value_to_yaml(msg.espmethod, out);
    out << ", ";
  }

  // member: escbrakepress
  {
    out << "escbrakepress: ";
    rosidl_generator_traits::value_to_yaml(msg.escbrakepress, out);
    out << ", ";
  }

  // member: gaspedal
  {
    out << "gaspedal: ";
    rosidl_generator_traits::value_to_yaml(msg.gaspedal, out);
    out << ", ";
  }

  // member: sysstatus
  {
    out << "sysstatus: ";
    rosidl_generator_traits::value_to_yaml(msg.sysstatus, out);
    out << ", ";
  }

  // member: speed
  {
    out << "speed: ";
    rosidl_generator_traits::value_to_yaml(msg.speed, out);
    out << ", ";
  }

  // member: lights
  {
    out << "lights: ";
    rosidl_generator_traits::value_to_yaml(msg.lights, out);
    out << ", ";
  }

  // member: turnlight
  {
    out << "turnlight: ";
    rosidl_generator_traits::value_to_yaml(msg.turnlight, out);
    out << ", ";
  }

  // member: gear
  {
    out << "gear: ";
    rosidl_generator_traits::value_to_yaml(msg.gear, out);
    out << ", ";
  }

  // member: epb
  {
    out << "epb: ";
    rosidl_generator_traits::value_to_yaml(msg.epb, out);
    out << ", ";
  }

  // member: door
  {
    out << "door: ";
    rosidl_generator_traits::value_to_yaml(msg.door, out);
    out << ", ";
  }

  // member: isvalid
  {
    out << "isvalid: ";
    rosidl_generator_traits::value_to_yaml(msg.isvalid, out);
    out << ", ";
  }

  // member: timestamp
  {
    out << "timestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.timestamp, out);
    out << ", ";
  }

  // member: sendsuccess
  {
    out << "sendsuccess: ";
    rosidl_generator_traits::value_to_yaml(msg.sendsuccess, out);
    out << ", ";
  }

  // member: brakepedal
  {
    out << "brakepedal: ";
    rosidl_generator_traits::value_to_yaml(msg.brakepedal, out);
    out << ", ";
  }

  // member: warning
  {
    out << "warning: ";
    rosidl_generator_traits::value_to_yaml(msg.warning, out);
    out << ", ";
  }

  // member: error
  {
    out << "error: ";
    rosidl_generator_traits::value_to_yaml(msg.error, out);
    out << ", ";
  }

  // member: battery
  {
    out << "battery: ";
    rosidl_generator_traits::value_to_yaml(msg.battery, out);
    out << ", ";
  }

  // member: controlover
  {
    out << "controlover: ";
    rosidl_generator_traits::value_to_yaml(msg.controlover, out);
    out << ", ";
  }

  // member: steerspeed
  {
    out << "steerspeed: ";
    rosidl_generator_traits::value_to_yaml(msg.steerspeed, out);
    out << ", ";
  }

  // member: accelpos
  {
    out << "accelpos: ";
    rosidl_generator_traits::value_to_yaml(msg.accelpos, out);
    out << ", ";
  }

  // member: breakflag
  {
    out << "breakflag: ";
    rosidl_generator_traits::value_to_yaml(msg.breakflag, out);
    out << ", ";
  }

  // member: breakpos
  {
    out << "breakpos: ";
    rosidl_generator_traits::value_to_yaml(msg.breakpos, out);
    out << ", ";
  }

  // member: yaw
  {
    out << "yaw: ";
    rosidl_generator_traits::value_to_yaml(msg.yaw, out);
    out << ", ";
  }

  // member: mil
  {
    out << "mil: ";
    rosidl_generator_traits::value_to_yaml(msg.mil, out);
    out << ", ";
  }

  // member: soc
  {
    out << "soc: ";
    rosidl_generator_traits::value_to_yaml(msg.soc, out);
    out << ", ";
  }

  // member: batvol
  {
    out << "batvol: ";
    rosidl_generator_traits::value_to_yaml(msg.batvol, out);
    out << ", ";
  }

  // member: acc
  {
    out << "acc: ";
    rosidl_generator_traits::value_to_yaml(msg.acc, out);
    out << ", ";
  }

  // member: oilperhour
  {
    out << "oilperhour: ";
    rosidl_generator_traits::value_to_yaml(msg.oilperhour, out);
    out << ", ";
  }

  // member: oilhundredkmconsume
  {
    out << "oilhundredkmconsume: ";
    rosidl_generator_traits::value_to_yaml(msg.oilhundredkmconsume, out);
    out << ", ";
  }

  // member: oilconsume
  {
    out << "oilconsume: ";
    rosidl_generator_traits::value_to_yaml(msg.oilconsume, out);
    out << ", ";
  }

  // member: autoctrlsig
  {
    out << "autoctrlsig: ";
    rosidl_generator_traits::value_to_yaml(msg.autoctrlsig, out);
    out << ", ";
  }

  // member: totalvoltage
  {
    out << "totalvoltage: ";
    rosidl_generator_traits::value_to_yaml(msg.totalvoltage, out);
    out << ", ";
  }

  // member: totalcurrent
  {
    out << "totalcurrent: ";
    rosidl_generator_traits::value_to_yaml(msg.totalcurrent, out);
    out << ", ";
  }

  // member: motorspeed
  {
    out << "motorspeed: ";
    rosidl_generator_traits::value_to_yaml(msg.motorspeed, out);
    out << ", ";
  }

  // member: motortorque
  {
    out << "motortorque: ";
    rosidl_generator_traits::value_to_yaml(msg.motortorque, out);
    out << ", ";
  }

  // member: wirecontrolstatus
  {
    out << "wirecontrolstatus: ";
    rosidl_generator_traits::value_to_yaml(msg.wirecontrolstatus, out);
    out << ", ";
  }

  // member: blinkerstatus
  {
    out << "blinkerstatus: ";
    rosidl_generator_traits::value_to_yaml(msg.blinkerstatus, out);
    out << ", ";
  }

  // member: accx
  {
    out << "accx: ";
    rosidl_generator_traits::value_to_yaml(msg.accx, out);
    out << ", ";
  }

  // member: gaspedalcar
  {
    out << "gaspedalcar: ";
    rosidl_generator_traits::value_to_yaml(msg.gaspedalcar, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const Actuator & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: epsmethod
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "epsmethod: ";
    rosidl_generator_traits::value_to_yaml(msg.epsmethod, out);
    out << "\n";
  }

  // member: epsangle
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "epsangle: ";
    rosidl_generator_traits::value_to_yaml(msg.epsangle, out);
    out << "\n";
  }

  // member: espmethod
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "espmethod: ";
    rosidl_generator_traits::value_to_yaml(msg.espmethod, out);
    out << "\n";
  }

  // member: escbrakepress
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "escbrakepress: ";
    rosidl_generator_traits::value_to_yaml(msg.escbrakepress, out);
    out << "\n";
  }

  // member: gaspedal
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "gaspedal: ";
    rosidl_generator_traits::value_to_yaml(msg.gaspedal, out);
    out << "\n";
  }

  // member: sysstatus
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "sysstatus: ";
    rosidl_generator_traits::value_to_yaml(msg.sysstatus, out);
    out << "\n";
  }

  // member: speed
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "speed: ";
    rosidl_generator_traits::value_to_yaml(msg.speed, out);
    out << "\n";
  }

  // member: lights
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lights: ";
    rosidl_generator_traits::value_to_yaml(msg.lights, out);
    out << "\n";
  }

  // member: turnlight
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "turnlight: ";
    rosidl_generator_traits::value_to_yaml(msg.turnlight, out);
    out << "\n";
  }

  // member: gear
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "gear: ";
    rosidl_generator_traits::value_to_yaml(msg.gear, out);
    out << "\n";
  }

  // member: epb
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "epb: ";
    rosidl_generator_traits::value_to_yaml(msg.epb, out);
    out << "\n";
  }

  // member: door
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "door: ";
    rosidl_generator_traits::value_to_yaml(msg.door, out);
    out << "\n";
  }

  // member: isvalid
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "isvalid: ";
    rosidl_generator_traits::value_to_yaml(msg.isvalid, out);
    out << "\n";
  }

  // member: timestamp
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "timestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.timestamp, out);
    out << "\n";
  }

  // member: sendsuccess
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "sendsuccess: ";
    rosidl_generator_traits::value_to_yaml(msg.sendsuccess, out);
    out << "\n";
  }

  // member: brakepedal
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "brakepedal: ";
    rosidl_generator_traits::value_to_yaml(msg.brakepedal, out);
    out << "\n";
  }

  // member: warning
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "warning: ";
    rosidl_generator_traits::value_to_yaml(msg.warning, out);
    out << "\n";
  }

  // member: error
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "error: ";
    rosidl_generator_traits::value_to_yaml(msg.error, out);
    out << "\n";
  }

  // member: battery
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "battery: ";
    rosidl_generator_traits::value_to_yaml(msg.battery, out);
    out << "\n";
  }

  // member: controlover
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "controlover: ";
    rosidl_generator_traits::value_to_yaml(msg.controlover, out);
    out << "\n";
  }

  // member: steerspeed
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "steerspeed: ";
    rosidl_generator_traits::value_to_yaml(msg.steerspeed, out);
    out << "\n";
  }

  // member: accelpos
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "accelpos: ";
    rosidl_generator_traits::value_to_yaml(msg.accelpos, out);
    out << "\n";
  }

  // member: breakflag
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "breakflag: ";
    rosidl_generator_traits::value_to_yaml(msg.breakflag, out);
    out << "\n";
  }

  // member: breakpos
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "breakpos: ";
    rosidl_generator_traits::value_to_yaml(msg.breakpos, out);
    out << "\n";
  }

  // member: yaw
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "yaw: ";
    rosidl_generator_traits::value_to_yaml(msg.yaw, out);
    out << "\n";
  }

  // member: mil
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "mil: ";
    rosidl_generator_traits::value_to_yaml(msg.mil, out);
    out << "\n";
  }

  // member: soc
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "soc: ";
    rosidl_generator_traits::value_to_yaml(msg.soc, out);
    out << "\n";
  }

  // member: batvol
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "batvol: ";
    rosidl_generator_traits::value_to_yaml(msg.batvol, out);
    out << "\n";
  }

  // member: acc
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "acc: ";
    rosidl_generator_traits::value_to_yaml(msg.acc, out);
    out << "\n";
  }

  // member: oilperhour
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "oilperhour: ";
    rosidl_generator_traits::value_to_yaml(msg.oilperhour, out);
    out << "\n";
  }

  // member: oilhundredkmconsume
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "oilhundredkmconsume: ";
    rosidl_generator_traits::value_to_yaml(msg.oilhundredkmconsume, out);
    out << "\n";
  }

  // member: oilconsume
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "oilconsume: ";
    rosidl_generator_traits::value_to_yaml(msg.oilconsume, out);
    out << "\n";
  }

  // member: autoctrlsig
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "autoctrlsig: ";
    rosidl_generator_traits::value_to_yaml(msg.autoctrlsig, out);
    out << "\n";
  }

  // member: totalvoltage
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "totalvoltage: ";
    rosidl_generator_traits::value_to_yaml(msg.totalvoltage, out);
    out << "\n";
  }

  // member: totalcurrent
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "totalcurrent: ";
    rosidl_generator_traits::value_to_yaml(msg.totalcurrent, out);
    out << "\n";
  }

  // member: motorspeed
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "motorspeed: ";
    rosidl_generator_traits::value_to_yaml(msg.motorspeed, out);
    out << "\n";
  }

  // member: motortorque
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "motortorque: ";
    rosidl_generator_traits::value_to_yaml(msg.motortorque, out);
    out << "\n";
  }

  // member: wirecontrolstatus
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "wirecontrolstatus: ";
    rosidl_generator_traits::value_to_yaml(msg.wirecontrolstatus, out);
    out << "\n";
  }

  // member: blinkerstatus
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "blinkerstatus: ";
    rosidl_generator_traits::value_to_yaml(msg.blinkerstatus, out);
    out << "\n";
  }

  // member: accx
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "accx: ";
    rosidl_generator_traits::value_to_yaml(msg.accx, out);
    out << "\n";
  }

  // member: gaspedalcar
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "gaspedalcar: ";
    rosidl_generator_traits::value_to_yaml(msg.gaspedalcar, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const Actuator & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace common_msgs_humble

namespace rosidl_generator_traits
{

[[deprecated("use common_msgs_humble::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const common_msgs_humble::msg::Actuator & msg,
  std::ostream & out, size_t indentation = 0)
{
  common_msgs_humble::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use common_msgs_humble::msg::to_yaml() instead")]]
inline std::string to_yaml(const common_msgs_humble::msg::Actuator & msg)
{
  return common_msgs_humble::msg::to_yaml(msg);
}

template<>
inline const char * data_type<common_msgs_humble::msg::Actuator>()
{
  return "common_msgs_humble::msg::Actuator";
}

template<>
inline const char * name<common_msgs_humble::msg::Actuator>()
{
  return "common_msgs_humble/msg/Actuator";
}

template<>
struct has_fixed_size<common_msgs_humble::msg::Actuator>
  : std::integral_constant<bool, true> {};

template<>
struct has_bounded_size<common_msgs_humble::msg::Actuator>
  : std::integral_constant<bool, true> {};

template<>
struct is_message<common_msgs_humble::msg::Actuator>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__ACTUATOR__TRAITS_HPP_
