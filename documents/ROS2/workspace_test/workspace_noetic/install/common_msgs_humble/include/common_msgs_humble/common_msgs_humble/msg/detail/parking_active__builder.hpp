// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/ParkingActive.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__PARKING_ACTIVE__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__PARKING_ACTIVE__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/parking_active__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_ParkingActive_stop_parking
{
public:
  explicit Init_ParkingActive_stop_parking(::common_msgs_humble::msg::ParkingActive & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::ParkingActive stop_parking(::common_msgs_humble::msg::ParkingActive::_stop_parking_type arg)
  {
    msg_.stop_parking = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::ParkingActive msg_;
};

class Init_ParkingActive_driveout
{
public:
  explicit Init_ParkingActive_driveout(::common_msgs_humble::msg::ParkingActive & msg)
  : msg_(msg)
  {}
  Init_ParkingActive_stop_parking driveout(::common_msgs_humble::msg::ParkingActive::_driveout_type arg)
  {
    msg_.driveout = std::move(arg);
    return Init_ParkingActive_stop_parking(msg_);
  }

private:
  ::common_msgs_humble::msg::ParkingActive msg_;
};

class Init_ParkingActive_iekutargetid
{
public:
  explicit Init_ParkingActive_iekutargetid(::common_msgs_humble::msg::ParkingActive & msg)
  : msg_(msg)
  {}
  Init_ParkingActive_driveout iekutargetid(::common_msgs_humble::msg::ParkingActive::_iekutargetid_type arg)
  {
    msg_.iekutargetid = std::move(arg);
    return Init_ParkingActive_driveout(msg_);
  }

private:
  ::common_msgs_humble::msg::ParkingActive msg_;
};

class Init_ParkingActive_iekulist
{
public:
  explicit Init_ParkingActive_iekulist(::common_msgs_humble::msg::ParkingActive & msg)
  : msg_(msg)
  {}
  Init_ParkingActive_iekutargetid iekulist(::common_msgs_humble::msg::ParkingActive::_iekulist_type arg)
  {
    msg_.iekulist = std::move(arg);
    return Init_ParkingActive_iekutargetid(msg_);
  }

private:
  ::common_msgs_humble::msg::ParkingActive msg_;
};

class Init_ParkingActive_answer
{
public:
  explicit Init_ParkingActive_answer(::common_msgs_humble::msg::ParkingActive & msg)
  : msg_(msg)
  {}
  Init_ParkingActive_iekulist answer(::common_msgs_humble::msg::ParkingActive::_answer_type arg)
  {
    msg_.answer = std::move(arg);
    return Init_ParkingActive_iekulist(msg_);
  }

private:
  ::common_msgs_humble::msg::ParkingActive msg_;
};

class Init_ParkingActive_tips
{
public:
  explicit Init_ParkingActive_tips(::common_msgs_humble::msg::ParkingActive & msg)
  : msg_(msg)
  {}
  Init_ParkingActive_answer tips(::common_msgs_humble::msg::ParkingActive::_tips_type arg)
  {
    msg_.tips = std::move(arg);
    return Init_ParkingActive_answer(msg_);
  }

private:
  ::common_msgs_humble::msg::ParkingActive msg_;
};

class Init_ParkingActive_stage
{
public:
  explicit Init_ParkingActive_stage(::common_msgs_humble::msg::ParkingActive & msg)
  : msg_(msg)
  {}
  Init_ParkingActive_tips stage(::common_msgs_humble::msg::ParkingActive::_stage_type arg)
  {
    msg_.stage = std::move(arg);
    return Init_ParkingActive_tips(msg_);
  }

private:
  ::common_msgs_humble::msg::ParkingActive msg_;
};

class Init_ParkingActive_timestamp
{
public:
  Init_ParkingActive_timestamp()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_ParkingActive_stage timestamp(::common_msgs_humble::msg::ParkingActive::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return Init_ParkingActive_stage(msg_);
  }

private:
  ::common_msgs_humble::msg::ParkingActive msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::ParkingActive>()
{
  return common_msgs_humble::msg::builder::Init_ParkingActive_timestamp();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__PARKING_ACTIVE__BUILDER_HPP_
