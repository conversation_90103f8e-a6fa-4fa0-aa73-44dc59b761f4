// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Sensorobject.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSOROBJECT__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSOROBJECT__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/sensorobject__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Sensorobject_object_prediction
{
public:
  explicit Init_Sensorobject_object_prediction(::common_msgs_humble::msg::Sensorobject & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Sensorobject object_prediction(::common_msgs_humble::msg::Sensorobject::_object_prediction_type arg)
  {
    msg_.object_prediction = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobject msg_;
};

class Init_Sensorobject_object_history
{
public:
  explicit Init_Sensorobject_object_history(::common_msgs_humble::msg::Sensorobject & msg)
  : msg_(msg)
  {}
  Init_Sensorobject_object_prediction object_history(::common_msgs_humble::msg::Sensorobject::_object_history_type arg)
  {
    msg_.object_history = std::move(arg);
    return Init_Sensorobject_object_prediction(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobject msg_;
};

class Init_Sensorobject_object_decision
{
public:
  explicit Init_Sensorobject_object_decision(::common_msgs_humble::msg::Sensorobject & msg)
  : msg_(msg)
  {}
  Init_Sensorobject_object_history object_decision(::common_msgs_humble::msg::Sensorobject::_object_decision_type arg)
  {
    msg_.object_decision = std::move(arg);
    return Init_Sensorobject_object_history(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobject msg_;
};

class Init_Sensorobject_speedl
{
public:
  explicit Init_Sensorobject_speedl(::common_msgs_humble::msg::Sensorobject & msg)
  : msg_(msg)
  {}
  Init_Sensorobject_object_decision speedl(::common_msgs_humble::msg::Sensorobject::_speedl_type arg)
  {
    msg_.speedl = std::move(arg);
    return Init_Sensorobject_object_decision(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobject msg_;
};

class Init_Sensorobject_speeds
{
public:
  explicit Init_Sensorobject_speeds(::common_msgs_humble::msg::Sensorobject & msg)
  : msg_(msg)
  {}
  Init_Sensorobject_speedl speeds(::common_msgs_humble::msg::Sensorobject::_speeds_type arg)
  {
    msg_.speeds = std::move(arg);
    return Init_Sensorobject_speedl(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobject msg_;
};

class Init_Sensorobject_l
{
public:
  explicit Init_Sensorobject_l(::common_msgs_humble::msg::Sensorobject & msg)
  : msg_(msg)
  {}
  Init_Sensorobject_speeds l(::common_msgs_humble::msg::Sensorobject::_l_type arg)
  {
    msg_.l = std::move(arg);
    return Init_Sensorobject_speeds(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobject msg_;
};

class Init_Sensorobject_s
{
public:
  explicit Init_Sensorobject_s(::common_msgs_humble::msg::Sensorobject & msg)
  : msg_(msg)
  {}
  Init_Sensorobject_l s(::common_msgs_humble::msg::Sensorobject::_s_type arg)
  {
    msg_.s = std::move(arg);
    return Init_Sensorobject_l(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobject msg_;
};

class Init_Sensorobject_radarobjectid
{
public:
  explicit Init_Sensorobject_radarobjectid(::common_msgs_humble::msg::Sensorobject & msg)
  : msg_(msg)
  {}
  Init_Sensorobject_s radarobjectid(::common_msgs_humble::msg::Sensorobject::_radarobjectid_type arg)
  {
    msg_.radarobjectid = std::move(arg);
    return Init_Sensorobject_s(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobject msg_;
};

class Init_Sensorobject_radarindex
{
public:
  explicit Init_Sensorobject_radarindex(::common_msgs_humble::msg::Sensorobject & msg)
  : msg_(msg)
  {}
  Init_Sensorobject_radarobjectid radarindex(::common_msgs_humble::msg::Sensorobject::_radarindex_type arg)
  {
    msg_.radarindex = std::move(arg);
    return Init_Sensorobject_radarobjectid(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobject msg_;
};

class Init_Sensorobject_behavior_state
{
public:
  explicit Init_Sensorobject_behavior_state(::common_msgs_humble::msg::Sensorobject & msg)
  : msg_(msg)
  {}
  Init_Sensorobject_radarindex behavior_state(::common_msgs_humble::msg::Sensorobject::_behavior_state_type arg)
  {
    msg_.behavior_state = std::move(arg);
    return Init_Sensorobject_radarindex(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobject msg_;
};

class Init_Sensorobject_driving_intent
{
public:
  explicit Init_Sensorobject_driving_intent(::common_msgs_humble::msg::Sensorobject & msg)
  : msg_(msg)
  {}
  Init_Sensorobject_behavior_state driving_intent(::common_msgs_humble::msg::Sensorobject::_driving_intent_type arg)
  {
    msg_.driving_intent = std::move(arg);
    return Init_Sensorobject_behavior_state(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobject msg_;
};

class Init_Sensorobject_points
{
public:
  explicit Init_Sensorobject_points(::common_msgs_humble::msg::Sensorobject & msg)
  : msg_(msg)
  {}
  Init_Sensorobject_driving_intent points(::common_msgs_humble::msg::Sensorobject::_points_type arg)
  {
    msg_.points = std::move(arg);
    return Init_Sensorobject_driving_intent(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobject msg_;
};

class Init_Sensorobject_confidence
{
public:
  explicit Init_Sensorobject_confidence(::common_msgs_humble::msg::Sensorobject & msg)
  : msg_(msg)
  {}
  Init_Sensorobject_points confidence(::common_msgs_humble::msg::Sensorobject::_confidence_type arg)
  {
    msg_.confidence = std::move(arg);
    return Init_Sensorobject_points(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobject msg_;
};

class Init_Sensorobject_value
{
public:
  explicit Init_Sensorobject_value(::common_msgs_humble::msg::Sensorobject & msg)
  : msg_(msg)
  {}
  Init_Sensorobject_confidence value(::common_msgs_humble::msg::Sensorobject::_value_type arg)
  {
    msg_.value = std::move(arg);
    return Init_Sensorobject_confidence(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobject msg_;
};

class Init_Sensorobject_classification
{
public:
  explicit Init_Sensorobject_classification(::common_msgs_humble::msg::Sensorobject & msg)
  : msg_(msg)
  {}
  Init_Sensorobject_value classification(::common_msgs_humble::msg::Sensorobject::_classification_type arg)
  {
    msg_.classification = std::move(arg);
    return Init_Sensorobject_value(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobject msg_;
};

class Init_Sensorobject_height
{
public:
  explicit Init_Sensorobject_height(::common_msgs_humble::msg::Sensorobject & msg)
  : msg_(msg)
  {}
  Init_Sensorobject_classification height(::common_msgs_humble::msg::Sensorobject::_height_type arg)
  {
    msg_.height = std::move(arg);
    return Init_Sensorobject_classification(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobject msg_;
};

class Init_Sensorobject_length
{
public:
  explicit Init_Sensorobject_length(::common_msgs_humble::msg::Sensorobject & msg)
  : msg_(msg)
  {}
  Init_Sensorobject_height length(::common_msgs_humble::msg::Sensorobject::_length_type arg)
  {
    msg_.length = std::move(arg);
    return Init_Sensorobject_height(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobject msg_;
};

class Init_Sensorobject_width
{
public:
  explicit Init_Sensorobject_width(::common_msgs_humble::msg::Sensorobject & msg)
  : msg_(msg)
  {}
  Init_Sensorobject_length width(::common_msgs_humble::msg::Sensorobject::_width_type arg)
  {
    msg_.width = std::move(arg);
    return Init_Sensorobject_length(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobject msg_;
};

class Init_Sensorobject_yawrate
{
public:
  explicit Init_Sensorobject_yawrate(::common_msgs_humble::msg::Sensorobject & msg)
  : msg_(msg)
  {}
  Init_Sensorobject_width yawrate(::common_msgs_humble::msg::Sensorobject::_yawrate_type arg)
  {
    msg_.yawrate = std::move(arg);
    return Init_Sensorobject_width(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobject msg_;
};

class Init_Sensorobject_rollrate
{
public:
  explicit Init_Sensorobject_rollrate(::common_msgs_humble::msg::Sensorobject & msg)
  : msg_(msg)
  {}
  Init_Sensorobject_yawrate rollrate(::common_msgs_humble::msg::Sensorobject::_rollrate_type arg)
  {
    msg_.rollrate = std::move(arg);
    return Init_Sensorobject_yawrate(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobject msg_;
};

class Init_Sensorobject_pitchrate
{
public:
  explicit Init_Sensorobject_pitchrate(::common_msgs_humble::msg::Sensorobject & msg)
  : msg_(msg)
  {}
  Init_Sensorobject_rollrate pitchrate(::common_msgs_humble::msg::Sensorobject::_pitchrate_type arg)
  {
    msg_.pitchrate = std::move(arg);
    return Init_Sensorobject_rollrate(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobject msg_;
};

class Init_Sensorobject_azimuth
{
public:
  explicit Init_Sensorobject_azimuth(::common_msgs_humble::msg::Sensorobject & msg)
  : msg_(msg)
  {}
  Init_Sensorobject_pitchrate azimuth(::common_msgs_humble::msg::Sensorobject::_azimuth_type arg)
  {
    msg_.azimuth = std::move(arg);
    return Init_Sensorobject_pitchrate(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobject msg_;
};

class Init_Sensorobject_pitchrad
{
public:
  explicit Init_Sensorobject_pitchrad(::common_msgs_humble::msg::Sensorobject & msg)
  : msg_(msg)
  {}
  Init_Sensorobject_azimuth pitchrad(::common_msgs_humble::msg::Sensorobject::_pitchrad_type arg)
  {
    msg_.pitchrad = std::move(arg);
    return Init_Sensorobject_azimuth(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobject msg_;
};

class Init_Sensorobject_rollrad
{
public:
  explicit Init_Sensorobject_rollrad(::common_msgs_humble::msg::Sensorobject & msg)
  : msg_(msg)
  {}
  Init_Sensorobject_pitchrad rollrad(::common_msgs_humble::msg::Sensorobject::_rollrad_type arg)
  {
    msg_.rollrad = std::move(arg);
    return Init_Sensorobject_pitchrad(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobject msg_;
};

class Init_Sensorobject_relspeedx
{
public:
  explicit Init_Sensorobject_relspeedx(::common_msgs_humble::msg::Sensorobject & msg)
  : msg_(msg)
  {}
  Init_Sensorobject_rollrad relspeedx(::common_msgs_humble::msg::Sensorobject::_relspeedx_type arg)
  {
    msg_.relspeedx = std::move(arg);
    return Init_Sensorobject_rollrad(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobject msg_;
};

class Init_Sensorobject_relspeedy
{
public:
  explicit Init_Sensorobject_relspeedy(::common_msgs_humble::msg::Sensorobject & msg)
  : msg_(msg)
  {}
  Init_Sensorobject_relspeedx relspeedy(::common_msgs_humble::msg::Sensorobject::_relspeedy_type arg)
  {
    msg_.relspeedy = std::move(arg);
    return Init_Sensorobject_relspeedx(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobject msg_;
};

class Init_Sensorobject_altitude
{
public:
  explicit Init_Sensorobject_altitude(::common_msgs_humble::msg::Sensorobject & msg)
  : msg_(msg)
  {}
  Init_Sensorobject_relspeedy altitude(::common_msgs_humble::msg::Sensorobject::_altitude_type arg)
  {
    msg_.altitude = std::move(arg);
    return Init_Sensorobject_relspeedy(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobject msg_;
};

class Init_Sensorobject_latitude
{
public:
  explicit Init_Sensorobject_latitude(::common_msgs_humble::msg::Sensorobject & msg)
  : msg_(msg)
  {}
  Init_Sensorobject_altitude latitude(::common_msgs_humble::msg::Sensorobject::_latitude_type arg)
  {
    msg_.latitude = std::move(arg);
    return Init_Sensorobject_altitude(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobject msg_;
};

class Init_Sensorobject_longtitude
{
public:
  explicit Init_Sensorobject_longtitude(::common_msgs_humble::msg::Sensorobject & msg)
  : msg_(msg)
  {}
  Init_Sensorobject_latitude longtitude(::common_msgs_humble::msg::Sensorobject::_longtitude_type arg)
  {
    msg_.longtitude = std::move(arg);
    return Init_Sensorobject_latitude(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobject msg_;
};

class Init_Sensorobject_z
{
public:
  explicit Init_Sensorobject_z(::common_msgs_humble::msg::Sensorobject & msg)
  : msg_(msg)
  {}
  Init_Sensorobject_longtitude z(::common_msgs_humble::msg::Sensorobject::_z_type arg)
  {
    msg_.z = std::move(arg);
    return Init_Sensorobject_longtitude(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobject msg_;
};

class Init_Sensorobject_y
{
public:
  explicit Init_Sensorobject_y(::common_msgs_humble::msg::Sensorobject & msg)
  : msg_(msg)
  {}
  Init_Sensorobject_z y(::common_msgs_humble::msg::Sensorobject::_y_type arg)
  {
    msg_.y = std::move(arg);
    return Init_Sensorobject_z(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobject msg_;
};

class Init_Sensorobject_x
{
public:
  explicit Init_Sensorobject_x(::common_msgs_humble::msg::Sensorobject & msg)
  : msg_(msg)
  {}
  Init_Sensorobject_y x(::common_msgs_humble::msg::Sensorobject::_x_type arg)
  {
    msg_.x = std::move(arg);
    return Init_Sensorobject_y(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobject msg_;
};

class Init_Sensorobject_id
{
public:
  Init_Sensorobject_id()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Sensorobject_x id(::common_msgs_humble::msg::Sensorobject::_id_type arg)
  {
    msg_.id = std::move(arg);
    return Init_Sensorobject_x(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobject msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Sensorobject>()
{
  return common_msgs_humble::msg::builder::Init_Sensorobject_id();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSOROBJECT__BUILDER_HPP_
