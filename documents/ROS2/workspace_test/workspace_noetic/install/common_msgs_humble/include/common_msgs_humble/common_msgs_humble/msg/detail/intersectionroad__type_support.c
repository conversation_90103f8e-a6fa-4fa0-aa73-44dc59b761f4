// generated from rosidl_typesupport_introspection_c/resource/idl__type_support.c.em
// with input from common_msgs_humble:msg/Intersectionroad.idl
// generated code does not contain a copyright notice

#include <stddef.h>
#include "common_msgs_humble/msg/detail/intersectionroad__rosidl_typesupport_introspection_c.h"
#include "common_msgs_humble/msg/rosidl_typesupport_introspection_c__visibility_control.h"
#include "rosidl_typesupport_introspection_c/field_types.h"
#include "rosidl_typesupport_introspection_c/identifier.h"
#include "rosidl_typesupport_introspection_c/message_introspection.h"
#include "common_msgs_humble/msg/detail/intersectionroad__functions.h"
#include "common_msgs_humble/msg/detail/intersectionroad__struct.h"


// Include directives for member types
// Member `lane`
#include "common_msgs_humble/msg/roadpoint.h"
// Member `lane`
#include "common_msgs_humble/msg/detail/roadpoint__rosidl_typesupport_introspection_c.h"

#ifdef __cplusplus
extern "C"
{
#endif

void common_msgs_humble__msg__Intersectionroad__rosidl_typesupport_introspection_c__Intersectionroad_init_function(
  void * message_memory, enum rosidl_runtime_c__message_initialization _init)
{
  // TODO(karsten1987): initializers are not yet implemented for typesupport c
  // see https://bgithub.xyz/ros2/ros2/issues/397
  (void) _init;
  common_msgs_humble__msg__Intersectionroad__init(message_memory);
}

void common_msgs_humble__msg__Intersectionroad__rosidl_typesupport_introspection_c__Intersectionroad_fini_function(void * message_memory)
{
  common_msgs_humble__msg__Intersectionroad__fini(message_memory);
}

size_t common_msgs_humble__msg__Intersectionroad__rosidl_typesupport_introspection_c__size_function__Intersectionroad__lane(
  const void * untyped_member)
{
  const common_msgs_humble__msg__Roadpoint__Sequence * member =
    (const common_msgs_humble__msg__Roadpoint__Sequence *)(untyped_member);
  return member->size;
}

const void * common_msgs_humble__msg__Intersectionroad__rosidl_typesupport_introspection_c__get_const_function__Intersectionroad__lane(
  const void * untyped_member, size_t index)
{
  const common_msgs_humble__msg__Roadpoint__Sequence * member =
    (const common_msgs_humble__msg__Roadpoint__Sequence *)(untyped_member);
  return &member->data[index];
}

void * common_msgs_humble__msg__Intersectionroad__rosidl_typesupport_introspection_c__get_function__Intersectionroad__lane(
  void * untyped_member, size_t index)
{
  common_msgs_humble__msg__Roadpoint__Sequence * member =
    (common_msgs_humble__msg__Roadpoint__Sequence *)(untyped_member);
  return &member->data[index];
}

void common_msgs_humble__msg__Intersectionroad__rosidl_typesupport_introspection_c__fetch_function__Intersectionroad__lane(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const common_msgs_humble__msg__Roadpoint * item =
    ((const common_msgs_humble__msg__Roadpoint *)
    common_msgs_humble__msg__Intersectionroad__rosidl_typesupport_introspection_c__get_const_function__Intersectionroad__lane(untyped_member, index));
  common_msgs_humble__msg__Roadpoint * value =
    (common_msgs_humble__msg__Roadpoint *)(untyped_value);
  *value = *item;
}

void common_msgs_humble__msg__Intersectionroad__rosidl_typesupport_introspection_c__assign_function__Intersectionroad__lane(
  void * untyped_member, size_t index, const void * untyped_value)
{
  common_msgs_humble__msg__Roadpoint * item =
    ((common_msgs_humble__msg__Roadpoint *)
    common_msgs_humble__msg__Intersectionroad__rosidl_typesupport_introspection_c__get_function__Intersectionroad__lane(untyped_member, index));
  const common_msgs_humble__msg__Roadpoint * value =
    (const common_msgs_humble__msg__Roadpoint *)(untyped_value);
  *item = *value;
}

bool common_msgs_humble__msg__Intersectionroad__rosidl_typesupport_introspection_c__resize_function__Intersectionroad__lane(
  void * untyped_member, size_t size)
{
  common_msgs_humble__msg__Roadpoint__Sequence * member =
    (common_msgs_humble__msg__Roadpoint__Sequence *)(untyped_member);
  common_msgs_humble__msg__Roadpoint__Sequence__fini(member);
  return common_msgs_humble__msg__Roadpoint__Sequence__init(member, size);
}

static rosidl_typesupport_introspection_c__MessageMember common_msgs_humble__msg__Intersectionroad__rosidl_typesupport_introspection_c__Intersectionroad_message_member_array[1] = {
  {
    "lane",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message (initialized later)
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Intersectionroad, lane),  // bytes offset in struct
    NULL,  // default value
    common_msgs_humble__msg__Intersectionroad__rosidl_typesupport_introspection_c__size_function__Intersectionroad__lane,  // size() function pointer
    common_msgs_humble__msg__Intersectionroad__rosidl_typesupport_introspection_c__get_const_function__Intersectionroad__lane,  // get_const(index) function pointer
    common_msgs_humble__msg__Intersectionroad__rosidl_typesupport_introspection_c__get_function__Intersectionroad__lane,  // get(index) function pointer
    common_msgs_humble__msg__Intersectionroad__rosidl_typesupport_introspection_c__fetch_function__Intersectionroad__lane,  // fetch(index, &value) function pointer
    common_msgs_humble__msg__Intersectionroad__rosidl_typesupport_introspection_c__assign_function__Intersectionroad__lane,  // assign(index, value) function pointer
    common_msgs_humble__msg__Intersectionroad__rosidl_typesupport_introspection_c__resize_function__Intersectionroad__lane  // resize(index) function pointer
  }
};

static const rosidl_typesupport_introspection_c__MessageMembers common_msgs_humble__msg__Intersectionroad__rosidl_typesupport_introspection_c__Intersectionroad_message_members = {
  "common_msgs_humble__msg",  // message namespace
  "Intersectionroad",  // message name
  1,  // number of fields
  sizeof(common_msgs_humble__msg__Intersectionroad),
  common_msgs_humble__msg__Intersectionroad__rosidl_typesupport_introspection_c__Intersectionroad_message_member_array,  // message members
  common_msgs_humble__msg__Intersectionroad__rosidl_typesupport_introspection_c__Intersectionroad_init_function,  // function to initialize message memory (memory has to be allocated)
  common_msgs_humble__msg__Intersectionroad__rosidl_typesupport_introspection_c__Intersectionroad_fini_function  // function to terminate message instance (will not free memory)
};

// this is not const since it must be initialized on first access
// since C does not allow non-integral compile-time constants
static rosidl_message_type_support_t common_msgs_humble__msg__Intersectionroad__rosidl_typesupport_introspection_c__Intersectionroad_message_type_support_handle = {
  0,
  &common_msgs_humble__msg__Intersectionroad__rosidl_typesupport_introspection_c__Intersectionroad_message_members,
  get_message_typesupport_handle_function,
};

ROSIDL_TYPESUPPORT_INTROSPECTION_C_EXPORT_common_msgs_humble
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, common_msgs_humble, msg, Intersectionroad)() {
  common_msgs_humble__msg__Intersectionroad__rosidl_typesupport_introspection_c__Intersectionroad_message_member_array[0].members_ =
    ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, common_msgs_humble, msg, Roadpoint)();
  if (!common_msgs_humble__msg__Intersectionroad__rosidl_typesupport_introspection_c__Intersectionroad_message_type_support_handle.typesupport_identifier) {
    common_msgs_humble__msg__Intersectionroad__rosidl_typesupport_introspection_c__Intersectionroad_message_type_support_handle.typesupport_identifier =
      rosidl_typesupport_introspection_c__identifier;
  }
  return &common_msgs_humble__msg__Intersectionroad__rosidl_typesupport_introspection_c__Intersectionroad_message_type_support_handle;
}
#ifdef __cplusplus
}
#endif
