// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from common_msgs_humble:msg/Fusiontrackingobject.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__FUSIONTRACKINGOBJECT__TRAITS_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__FUSIONTRACKINGOBJECT__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "common_msgs_humble/msg/detail/fusiontrackingobject__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

// Include directives for member types
// Member 'lidarobject'
// Member 'radarobject'
// Member 'obuobject'
#include "common_msgs_humble/msg/detail/sensorobject__traits.hpp"
// Member 'obupantobject'
#include "common_msgs_humble/msg/detail/obupant__traits.hpp"

namespace common_msgs_humble
{

namespace msg
{

inline void to_flow_style_yaml(
  const Fusiontrackingobject & msg,
  std::ostream & out)
{
  out << "{";
  // member: objectsource
  {
    out << "objectsource: ";
    rosidl_generator_traits::value_to_yaml(msg.objectsource, out);
    out << ", ";
  }

  // member: lidartimestamp
  {
    out << "lidartimestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.lidartimestamp, out);
    out << ", ";
  }

  // member: lidargpstime
  {
    out << "lidargpstime: ";
    rosidl_generator_traits::value_to_yaml(msg.lidargpstime, out);
    out << ", ";
  }

  // member: lidarobject
  {
    out << "lidarobject: ";
    to_flow_style_yaml(msg.lidarobject, out);
    out << ", ";
  }

  // member: radartimestamp
  {
    out << "radartimestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.radartimestamp, out);
    out << ", ";
  }

  // member: radargpstime
  {
    out << "radargpstime: ";
    rosidl_generator_traits::value_to_yaml(msg.radargpstime, out);
    out << ", ";
  }

  // member: radarobject
  {
    out << "radarobject: ";
    to_flow_style_yaml(msg.radarobject, out);
    out << ", ";
  }

  // member: obutimestamp
  {
    out << "obutimestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.obutimestamp, out);
    out << ", ";
  }

  // member: obugpstime
  {
    out << "obugpstime: ";
    rosidl_generator_traits::value_to_yaml(msg.obugpstime, out);
    out << ", ";
  }

  // member: obuobject
  {
    out << "obuobject: ";
    to_flow_style_yaml(msg.obuobject, out);
    out << ", ";
  }

  // member: obupantobject
  {
    out << "obupantobject: ";
    to_flow_style_yaml(msg.obupantobject, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const Fusiontrackingobject & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: objectsource
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "objectsource: ";
    rosidl_generator_traits::value_to_yaml(msg.objectsource, out);
    out << "\n";
  }

  // member: lidartimestamp
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lidartimestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.lidartimestamp, out);
    out << "\n";
  }

  // member: lidargpstime
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lidargpstime: ";
    rosidl_generator_traits::value_to_yaml(msg.lidargpstime, out);
    out << "\n";
  }

  // member: lidarobject
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lidarobject:\n";
    to_block_style_yaml(msg.lidarobject, out, indentation + 2);
  }

  // member: radartimestamp
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "radartimestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.radartimestamp, out);
    out << "\n";
  }

  // member: radargpstime
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "radargpstime: ";
    rosidl_generator_traits::value_to_yaml(msg.radargpstime, out);
    out << "\n";
  }

  // member: radarobject
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "radarobject:\n";
    to_block_style_yaml(msg.radarobject, out, indentation + 2);
  }

  // member: obutimestamp
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "obutimestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.obutimestamp, out);
    out << "\n";
  }

  // member: obugpstime
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "obugpstime: ";
    rosidl_generator_traits::value_to_yaml(msg.obugpstime, out);
    out << "\n";
  }

  // member: obuobject
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "obuobject:\n";
    to_block_style_yaml(msg.obuobject, out, indentation + 2);
  }

  // member: obupantobject
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "obupantobject:\n";
    to_block_style_yaml(msg.obupantobject, out, indentation + 2);
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const Fusiontrackingobject & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace common_msgs_humble

namespace rosidl_generator_traits
{

[[deprecated("use common_msgs_humble::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const common_msgs_humble::msg::Fusiontrackingobject & msg,
  std::ostream & out, size_t indentation = 0)
{
  common_msgs_humble::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use common_msgs_humble::msg::to_yaml() instead")]]
inline std::string to_yaml(const common_msgs_humble::msg::Fusiontrackingobject & msg)
{
  return common_msgs_humble::msg::to_yaml(msg);
}

template<>
inline const char * data_type<common_msgs_humble::msg::Fusiontrackingobject>()
{
  return "common_msgs_humble::msg::Fusiontrackingobject";
}

template<>
inline const char * name<common_msgs_humble::msg::Fusiontrackingobject>()
{
  return "common_msgs_humble/msg/Fusiontrackingobject";
}

template<>
struct has_fixed_size<common_msgs_humble::msg::Fusiontrackingobject>
  : std::integral_constant<bool, has_fixed_size<common_msgs_humble::msg::Obupant>::value && has_fixed_size<common_msgs_humble::msg::Sensorobject>::value> {};

template<>
struct has_bounded_size<common_msgs_humble::msg::Fusiontrackingobject>
  : std::integral_constant<bool, has_bounded_size<common_msgs_humble::msg::Obupant>::value && has_bounded_size<common_msgs_humble::msg::Sensorobject>::value> {};

template<>
struct is_message<common_msgs_humble::msg::Fusiontrackingobject>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__FUSIONTRACKINGOBJECT__TRAITS_HPP_
