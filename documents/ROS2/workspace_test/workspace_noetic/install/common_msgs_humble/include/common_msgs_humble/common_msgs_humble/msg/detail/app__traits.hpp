// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from common_msgs_humble:msg/App.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__APP__TRAITS_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__APP__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "common_msgs_humble/msg/detail/app__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

namespace common_msgs_humble
{

namespace msg
{

inline void to_flow_style_yaml(
  const App & msg,
  std::ostream & out)
{
  out << "{";
  // member: stopgo
  {
    out << "stopgo: ";
    rosidl_generator_traits::value_to_yaml(msg.stopgo, out);
    out << ", ";
  }

  // member: zonename
  {
    out << "zonename: ";
    rosidl_generator_traits::value_to_yaml(msg.zonename, out);
    out << ", ";
  }

  // member: apsnum
  {
    out << "apsnum: ";
    rosidl_generator_traits::value_to_yaml(msg.apsnum, out);
    out << ", ";
  }

  // member: estop
  {
    out << "estop: ";
    rosidl_generator_traits::value_to_yaml(msg.estop, out);
    out << ", ";
  }

  // member: park
  {
    out << "park: ";
    rosidl_generator_traits::value_to_yaml(msg.park, out);
    out << ", ";
  }

  // member: timestamp
  {
    out << "timestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.timestamp, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const App & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: stopgo
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "stopgo: ";
    rosidl_generator_traits::value_to_yaml(msg.stopgo, out);
    out << "\n";
  }

  // member: zonename
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "zonename: ";
    rosidl_generator_traits::value_to_yaml(msg.zonename, out);
    out << "\n";
  }

  // member: apsnum
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "apsnum: ";
    rosidl_generator_traits::value_to_yaml(msg.apsnum, out);
    out << "\n";
  }

  // member: estop
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "estop: ";
    rosidl_generator_traits::value_to_yaml(msg.estop, out);
    out << "\n";
  }

  // member: park
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "park: ";
    rosidl_generator_traits::value_to_yaml(msg.park, out);
    out << "\n";
  }

  // member: timestamp
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "timestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.timestamp, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const App & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace common_msgs_humble

namespace rosidl_generator_traits
{

[[deprecated("use common_msgs_humble::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const common_msgs_humble::msg::App & msg,
  std::ostream & out, size_t indentation = 0)
{
  common_msgs_humble::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use common_msgs_humble::msg::to_yaml() instead")]]
inline std::string to_yaml(const common_msgs_humble::msg::App & msg)
{
  return common_msgs_humble::msg::to_yaml(msg);
}

template<>
inline const char * data_type<common_msgs_humble::msg::App>()
{
  return "common_msgs_humble::msg::App";
}

template<>
inline const char * name<common_msgs_humble::msg::App>()
{
  return "common_msgs_humble/msg/App";
}

template<>
struct has_fixed_size<common_msgs_humble::msg::App>
  : std::integral_constant<bool, true> {};

template<>
struct has_bounded_size<common_msgs_humble::msg::App>
  : std::integral_constant<bool, true> {};

template<>
struct is_message<common_msgs_humble::msg::App>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__APP__TRAITS_HPP_
