// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from common_msgs_humble:msg/Elapsedtime.idl
// generated code does not contain a copyright notice
#include "common_msgs_humble/msg/detail/elapsedtime__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


bool
common_msgs_humble__msg__Elapsedtime__init(common_msgs_humble__msg__Elapsedtime * msg)
{
  if (!msg) {
    return false;
  }
  // time
  return true;
}

void
common_msgs_humble__msg__Elapsedtime__fini(common_msgs_humble__msg__Elapsedtime * msg)
{
  if (!msg) {
    return;
  }
  // time
}

bool
common_msgs_humble__msg__Elapsedtime__are_equal(const common_msgs_humble__msg__Elapsedtime * lhs, const common_msgs_humble__msg__Elapsedtime * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // time
  if (lhs->time != rhs->time) {
    return false;
  }
  return true;
}

bool
common_msgs_humble__msg__Elapsedtime__copy(
  const common_msgs_humble__msg__Elapsedtime * input,
  common_msgs_humble__msg__Elapsedtime * output)
{
  if (!input || !output) {
    return false;
  }
  // time
  output->time = input->time;
  return true;
}

common_msgs_humble__msg__Elapsedtime *
common_msgs_humble__msg__Elapsedtime__create()
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Elapsedtime * msg = (common_msgs_humble__msg__Elapsedtime *)allocator.allocate(sizeof(common_msgs_humble__msg__Elapsedtime), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(common_msgs_humble__msg__Elapsedtime));
  bool success = common_msgs_humble__msg__Elapsedtime__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
common_msgs_humble__msg__Elapsedtime__destroy(common_msgs_humble__msg__Elapsedtime * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    common_msgs_humble__msg__Elapsedtime__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
common_msgs_humble__msg__Elapsedtime__Sequence__init(common_msgs_humble__msg__Elapsedtime__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Elapsedtime * data = NULL;

  if (size) {
    data = (common_msgs_humble__msg__Elapsedtime *)allocator.zero_allocate(size, sizeof(common_msgs_humble__msg__Elapsedtime), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = common_msgs_humble__msg__Elapsedtime__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        common_msgs_humble__msg__Elapsedtime__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
common_msgs_humble__msg__Elapsedtime__Sequence__fini(common_msgs_humble__msg__Elapsedtime__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      common_msgs_humble__msg__Elapsedtime__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

common_msgs_humble__msg__Elapsedtime__Sequence *
common_msgs_humble__msg__Elapsedtime__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Elapsedtime__Sequence * array = (common_msgs_humble__msg__Elapsedtime__Sequence *)allocator.allocate(sizeof(common_msgs_humble__msg__Elapsedtime__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = common_msgs_humble__msg__Elapsedtime__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
common_msgs_humble__msg__Elapsedtime__Sequence__destroy(common_msgs_humble__msg__Elapsedtime__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    common_msgs_humble__msg__Elapsedtime__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
common_msgs_humble__msg__Elapsedtime__Sequence__are_equal(const common_msgs_humble__msg__Elapsedtime__Sequence * lhs, const common_msgs_humble__msg__Elapsedtime__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!common_msgs_humble__msg__Elapsedtime__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
common_msgs_humble__msg__Elapsedtime__Sequence__copy(
  const common_msgs_humble__msg__Elapsedtime__Sequence * input,
  common_msgs_humble__msg__Elapsedtime__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(common_msgs_humble__msg__Elapsedtime);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    common_msgs_humble__msg__Elapsedtime * data =
      (common_msgs_humble__msg__Elapsedtime *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!common_msgs_humble__msg__Elapsedtime__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          common_msgs_humble__msg__Elapsedtime__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!common_msgs_humble__msg__Elapsedtime__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
