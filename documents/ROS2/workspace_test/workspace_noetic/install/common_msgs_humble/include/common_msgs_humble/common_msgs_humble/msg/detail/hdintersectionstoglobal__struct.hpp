// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Hdintersectionstoglobal.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__HDINTERSECTIONSTOGLOBAL__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__HDINTERSECTIONSTOGLOBAL__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'intersectionmaps'
#include "common_msgs_humble/msg/detail/hdintersectiontoglobal__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Hdintersectionstoglobal __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Hdintersectionstoglobal __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Hdintersectionstoglobal_
{
  using Type = Hdintersectionstoglobal_<ContainerAllocator>;

  explicit Hdintersectionstoglobal_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->isvalid = 0;
      this->timestamp = 0ll;
    }
  }

  explicit Hdintersectionstoglobal_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->isvalid = 0;
      this->timestamp = 0ll;
    }
  }

  // field types and members
  using _intersectionmaps_type =
    std::vector<common_msgs_humble::msg::Hdintersectiontoglobal_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<common_msgs_humble::msg::Hdintersectiontoglobal_<ContainerAllocator>>>;
  _intersectionmaps_type intersectionmaps;
  using _isvalid_type =
    uint8_t;
  _isvalid_type isvalid;
  using _timestamp_type =
    int64_t;
  _timestamp_type timestamp;

  // setters for named parameter idiom
  Type & set__intersectionmaps(
    const std::vector<common_msgs_humble::msg::Hdintersectiontoglobal_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<common_msgs_humble::msg::Hdintersectiontoglobal_<ContainerAllocator>>> & _arg)
  {
    this->intersectionmaps = _arg;
    return *this;
  }
  Type & set__isvalid(
    const uint8_t & _arg)
  {
    this->isvalid = _arg;
    return *this;
  }
  Type & set__timestamp(
    const int64_t & _arg)
  {
    this->timestamp = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Hdintersectionstoglobal_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Hdintersectionstoglobal_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Hdintersectionstoglobal_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Hdintersectionstoglobal_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Hdintersectionstoglobal_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Hdintersectionstoglobal_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Hdintersectionstoglobal_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Hdintersectionstoglobal_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Hdintersectionstoglobal_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Hdintersectionstoglobal_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Hdintersectionstoglobal
    std::shared_ptr<common_msgs_humble::msg::Hdintersectionstoglobal_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Hdintersectionstoglobal
    std::shared_ptr<common_msgs_humble::msg::Hdintersectionstoglobal_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Hdintersectionstoglobal_ & other) const
  {
    if (this->intersectionmaps != other.intersectionmaps) {
      return false;
    }
    if (this->isvalid != other.isvalid) {
      return false;
    }
    if (this->timestamp != other.timestamp) {
      return false;
    }
    return true;
  }
  bool operator!=(const Hdintersectionstoglobal_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Hdintersectionstoglobal_

// alias to use template instance with default allocator
using Hdintersectionstoglobal =
  common_msgs_humble::msg::Hdintersectionstoglobal_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__HDINTERSECTIONSTOGLOBAL__STRUCT_HPP_
