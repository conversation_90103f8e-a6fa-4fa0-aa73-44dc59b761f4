// generated from rosidl_generator_cpp/resource/idl__type_support.hpp.em
// with input from common_msgs_humble:msg/Sensorstatus.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSORSTATUS__TYPE_SUPPORT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSORSTATUS__TYPE_SUPPORT_HPP_

#include "rosidl_typesupport_interface/macros.h"

#include "common_msgs_humble/msg/rosidl_generator_cpp__visibility_control.hpp"

#include "rosidl_typesupport_cpp/message_type_support.hpp"

#ifdef __cplusplus
extern "C"
{
#endif
// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_CPP_PUBLIC_common_msgs_humble
const rosidl_message_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_cpp,
  common_msgs_humble,
  msg,
  Sensorstatus
)();
#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSORSTATUS__TYPE_SUPPORT_HPP_
