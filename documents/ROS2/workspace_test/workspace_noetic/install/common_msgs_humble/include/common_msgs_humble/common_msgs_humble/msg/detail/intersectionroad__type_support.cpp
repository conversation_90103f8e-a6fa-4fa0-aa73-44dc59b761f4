// generated from rosidl_typesupport_introspection_cpp/resource/idl__type_support.cpp.em
// with input from common_msgs_humble:msg/Intersectionroad.idl
// generated code does not contain a copyright notice

#include "array"
#include "cstddef"
#include "string"
#include "vector"
#include "rosidl_runtime_c/message_type_support_struct.h"
#include "rosidl_typesupport_cpp/message_type_support.hpp"
#include "rosidl_typesupport_interface/macros.h"
#include "common_msgs_humble/msg/detail/intersectionroad__struct.hpp"
#include "rosidl_typesupport_introspection_cpp/field_types.hpp"
#include "rosidl_typesupport_introspection_cpp/identifier.hpp"
#include "rosidl_typesupport_introspection_cpp/message_introspection.hpp"
#include "rosidl_typesupport_introspection_cpp/message_type_support_decl.hpp"
#include "rosidl_typesupport_introspection_cpp/visibility_control.h"

namespace common_msgs_humble
{

namespace msg
{

namespace rosidl_typesupport_introspection_cpp
{

void Intersectionroad_init_function(
  void * message_memory, rosidl_runtime_cpp::MessageInitialization _init)
{
  new (message_memory) common_msgs_humble::msg::Intersectionroad(_init);
}

void Intersectionroad_fini_function(void * message_memory)
{
  auto typed_message = static_cast<common_msgs_humble::msg::Intersectionroad *>(message_memory);
  typed_message->~Intersectionroad();
}

size_t size_function__Intersectionroad__lane(const void * untyped_member)
{
  const auto * member = reinterpret_cast<const std::vector<common_msgs_humble::msg::Roadpoint> *>(untyped_member);
  return member->size();
}

const void * get_const_function__Intersectionroad__lane(const void * untyped_member, size_t index)
{
  const auto & member =
    *reinterpret_cast<const std::vector<common_msgs_humble::msg::Roadpoint> *>(untyped_member);
  return &member[index];
}

void * get_function__Intersectionroad__lane(void * untyped_member, size_t index)
{
  auto & member =
    *reinterpret_cast<std::vector<common_msgs_humble::msg::Roadpoint> *>(untyped_member);
  return &member[index];
}

void fetch_function__Intersectionroad__lane(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const auto & item = *reinterpret_cast<const common_msgs_humble::msg::Roadpoint *>(
    get_const_function__Intersectionroad__lane(untyped_member, index));
  auto & value = *reinterpret_cast<common_msgs_humble::msg::Roadpoint *>(untyped_value);
  value = item;
}

void assign_function__Intersectionroad__lane(
  void * untyped_member, size_t index, const void * untyped_value)
{
  auto & item = *reinterpret_cast<common_msgs_humble::msg::Roadpoint *>(
    get_function__Intersectionroad__lane(untyped_member, index));
  const auto & value = *reinterpret_cast<const common_msgs_humble::msg::Roadpoint *>(untyped_value);
  item = value;
}

void resize_function__Intersectionroad__lane(void * untyped_member, size_t size)
{
  auto * member =
    reinterpret_cast<std::vector<common_msgs_humble::msg::Roadpoint> *>(untyped_member);
  member->resize(size);
}

static const ::rosidl_typesupport_introspection_cpp::MessageMember Intersectionroad_message_member_array[1] = {
  {
    "lane",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    ::rosidl_typesupport_introspection_cpp::get_message_type_support_handle<common_msgs_humble::msg::Roadpoint>(),  // members of sub message
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble::msg::Intersectionroad, lane),  // bytes offset in struct
    nullptr,  // default value
    size_function__Intersectionroad__lane,  // size() function pointer
    get_const_function__Intersectionroad__lane,  // get_const(index) function pointer
    get_function__Intersectionroad__lane,  // get(index) function pointer
    fetch_function__Intersectionroad__lane,  // fetch(index, &value) function pointer
    assign_function__Intersectionroad__lane,  // assign(index, value) function pointer
    resize_function__Intersectionroad__lane  // resize(index) function pointer
  }
};

static const ::rosidl_typesupport_introspection_cpp::MessageMembers Intersectionroad_message_members = {
  "common_msgs_humble::msg",  // message namespace
  "Intersectionroad",  // message name
  1,  // number of fields
  sizeof(common_msgs_humble::msg::Intersectionroad),
  Intersectionroad_message_member_array,  // message members
  Intersectionroad_init_function,  // function to initialize message memory (memory has to be allocated)
  Intersectionroad_fini_function  // function to terminate message instance (will not free memory)
};

static const rosidl_message_type_support_t Intersectionroad_message_type_support_handle = {
  ::rosidl_typesupport_introspection_cpp::typesupport_identifier,
  &Intersectionroad_message_members,
  get_message_typesupport_handle_function,
};

}  // namespace rosidl_typesupport_introspection_cpp

}  // namespace msg

}  // namespace common_msgs_humble


namespace rosidl_typesupport_introspection_cpp
{

template<>
ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_PUBLIC
const rosidl_message_type_support_t *
get_message_type_support_handle<common_msgs_humble::msg::Intersectionroad>()
{
  return &::common_msgs_humble::msg::rosidl_typesupport_introspection_cpp::Intersectionroad_message_type_support_handle;
}

}  // namespace rosidl_typesupport_introspection_cpp

#ifdef __cplusplus
extern "C"
{
#endif

ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_PUBLIC
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_cpp, common_msgs_humble, msg, Intersectionroad)() {
  return &::common_msgs_humble::msg::rosidl_typesupport_introspection_cpp::Intersectionroad_message_type_support_handle;
}

#ifdef __cplusplus
}
#endif
