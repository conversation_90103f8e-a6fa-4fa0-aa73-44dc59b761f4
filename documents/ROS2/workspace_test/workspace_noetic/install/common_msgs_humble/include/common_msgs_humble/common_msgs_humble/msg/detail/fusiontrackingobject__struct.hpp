// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Fusiontrackingobject.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__FUSIONTRACKINGOBJECT__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__FUSIONTRACKINGOBJECT__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'lidarobject'
// Member 'radarobject'
// Member 'obuobject'
#include "common_msgs_humble/msg/detail/sensorobject__struct.hpp"
// Member 'obupantobject'
#include "common_msgs_humble/msg/detail/obupant__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Fusiontrackingobject __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Fusiontrackingobject __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Fusiontrackingobject_
{
  using Type = Fusiontrackingobject_<ContainerAllocator>;

  explicit Fusiontrackingobject_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : lidarobject(_init),
    radarobject(_init),
    obuobject(_init),
    obupantobject(_init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->objectsource = 0;
      this->lidartimestamp = 0ll;
      this->lidargpstime = 0ll;
      this->radartimestamp = 0ll;
      this->radargpstime = 0ll;
      this->obutimestamp = 0ll;
      this->obugpstime = 0ll;
    }
  }

  explicit Fusiontrackingobject_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : lidarobject(_alloc, _init),
    radarobject(_alloc, _init),
    obuobject(_alloc, _init),
    obupantobject(_alloc, _init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->objectsource = 0;
      this->lidartimestamp = 0ll;
      this->lidargpstime = 0ll;
      this->radartimestamp = 0ll;
      this->radargpstime = 0ll;
      this->obutimestamp = 0ll;
      this->obugpstime = 0ll;
    }
  }

  // field types and members
  using _objectsource_type =
    uint8_t;
  _objectsource_type objectsource;
  using _lidartimestamp_type =
    int64_t;
  _lidartimestamp_type lidartimestamp;
  using _lidargpstime_type =
    int64_t;
  _lidargpstime_type lidargpstime;
  using _lidarobject_type =
    common_msgs_humble::msg::Sensorobject_<ContainerAllocator>;
  _lidarobject_type lidarobject;
  using _radartimestamp_type =
    int64_t;
  _radartimestamp_type radartimestamp;
  using _radargpstime_type =
    int64_t;
  _radargpstime_type radargpstime;
  using _radarobject_type =
    common_msgs_humble::msg::Sensorobject_<ContainerAllocator>;
  _radarobject_type radarobject;
  using _obutimestamp_type =
    int64_t;
  _obutimestamp_type obutimestamp;
  using _obugpstime_type =
    int64_t;
  _obugpstime_type obugpstime;
  using _obuobject_type =
    common_msgs_humble::msg::Sensorobject_<ContainerAllocator>;
  _obuobject_type obuobject;
  using _obupantobject_type =
    common_msgs_humble::msg::Obupant_<ContainerAllocator>;
  _obupantobject_type obupantobject;

  // setters for named parameter idiom
  Type & set__objectsource(
    const uint8_t & _arg)
  {
    this->objectsource = _arg;
    return *this;
  }
  Type & set__lidartimestamp(
    const int64_t & _arg)
  {
    this->lidartimestamp = _arg;
    return *this;
  }
  Type & set__lidargpstime(
    const int64_t & _arg)
  {
    this->lidargpstime = _arg;
    return *this;
  }
  Type & set__lidarobject(
    const common_msgs_humble::msg::Sensorobject_<ContainerAllocator> & _arg)
  {
    this->lidarobject = _arg;
    return *this;
  }
  Type & set__radartimestamp(
    const int64_t & _arg)
  {
    this->radartimestamp = _arg;
    return *this;
  }
  Type & set__radargpstime(
    const int64_t & _arg)
  {
    this->radargpstime = _arg;
    return *this;
  }
  Type & set__radarobject(
    const common_msgs_humble::msg::Sensorobject_<ContainerAllocator> & _arg)
  {
    this->radarobject = _arg;
    return *this;
  }
  Type & set__obutimestamp(
    const int64_t & _arg)
  {
    this->obutimestamp = _arg;
    return *this;
  }
  Type & set__obugpstime(
    const int64_t & _arg)
  {
    this->obugpstime = _arg;
    return *this;
  }
  Type & set__obuobject(
    const common_msgs_humble::msg::Sensorobject_<ContainerAllocator> & _arg)
  {
    this->obuobject = _arg;
    return *this;
  }
  Type & set__obupantobject(
    const common_msgs_humble::msg::Obupant_<ContainerAllocator> & _arg)
  {
    this->obupantobject = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Fusiontrackingobject_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Fusiontrackingobject_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Fusiontrackingobject_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Fusiontrackingobject_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Fusiontrackingobject_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Fusiontrackingobject_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Fusiontrackingobject_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Fusiontrackingobject_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Fusiontrackingobject_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Fusiontrackingobject_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Fusiontrackingobject
    std::shared_ptr<common_msgs_humble::msg::Fusiontrackingobject_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Fusiontrackingobject
    std::shared_ptr<common_msgs_humble::msg::Fusiontrackingobject_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Fusiontrackingobject_ & other) const
  {
    if (this->objectsource != other.objectsource) {
      return false;
    }
    if (this->lidartimestamp != other.lidartimestamp) {
      return false;
    }
    if (this->lidargpstime != other.lidargpstime) {
      return false;
    }
    if (this->lidarobject != other.lidarobject) {
      return false;
    }
    if (this->radartimestamp != other.radartimestamp) {
      return false;
    }
    if (this->radargpstime != other.radargpstime) {
      return false;
    }
    if (this->radarobject != other.radarobject) {
      return false;
    }
    if (this->obutimestamp != other.obutimestamp) {
      return false;
    }
    if (this->obugpstime != other.obugpstime) {
      return false;
    }
    if (this->obuobject != other.obuobject) {
      return false;
    }
    if (this->obupantobject != other.obupantobject) {
      return false;
    }
    return true;
  }
  bool operator!=(const Fusiontrackingobject_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Fusiontrackingobject_

// alias to use template instance with default allocator
using Fusiontrackingobject =
  common_msgs_humble::msg::Fusiontrackingobject_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__FUSIONTRACKINGOBJECT__STRUCT_HPP_
