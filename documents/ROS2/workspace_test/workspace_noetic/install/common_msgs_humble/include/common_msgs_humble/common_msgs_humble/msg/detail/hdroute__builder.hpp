// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Hdroute.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__HDROUTE__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__HDROUTE__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/hdroute__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Hdroute_index
{
public:
  explicit Init_Hdroute_index(::common_msgs_humble::msg::Hdroute & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Hdroute index(::common_msgs_humble::msg::Hdroute::_index_type arg)
  {
    msg_.index = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Hdroute msg_;
};

class Init_Hdroute_timestamp
{
public:
  explicit Init_Hdroute_timestamp(::common_msgs_humble::msg::Hdroute & msg)
  : msg_(msg)
  {}
  Init_Hdroute_index timestamp(::common_msgs_humble::msg::Hdroute::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return Init_Hdroute_index(msg_);
  }

private:
  ::common_msgs_humble::msg::Hdroute msg_;
};

class Init_Hdroute_isvalid
{
public:
  explicit Init_Hdroute_isvalid(::common_msgs_humble::msg::Hdroute & msg)
  : msg_(msg)
  {}
  Init_Hdroute_timestamp isvalid(::common_msgs_humble::msg::Hdroute::_isvalid_type arg)
  {
    msg_.isvalid = std::move(arg);
    return Init_Hdroute_timestamp(msg_);
  }

private:
  ::common_msgs_humble::msg::Hdroute msg_;
};

class Init_Hdroute_map
{
public:
  Init_Hdroute_map()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Hdroute_isvalid map(::common_msgs_humble::msg::Hdroute::_map_type arg)
  {
    msg_.map = std::move(arg);
    return Init_Hdroute_isvalid(msg_);
  }

private:
  ::common_msgs_humble::msg::Hdroute msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Hdroute>()
{
  return common_msgs_humble::msg::builder::Init_Hdroute_map();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__HDROUTE__BUILDER_HPP_
