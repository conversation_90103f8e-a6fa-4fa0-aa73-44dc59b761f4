// generated from rosidl_typesupport_introspection_c/resource/idl__type_support.c.em
// with input from common_msgs_humble:msg/Lonlatmappoints.idl
// generated code does not contain a copyright notice

#include <stddef.h>
#include "common_msgs_humble/msg/detail/lonlatmappoints__rosidl_typesupport_introspection_c.h"
#include "common_msgs_humble/msg/rosidl_typesupport_introspection_c__visibility_control.h"
#include "rosidl_typesupport_introspection_c/field_types.h"
#include "rosidl_typesupport_introspection_c/identifier.h"
#include "rosidl_typesupport_introspection_c/message_introspection.h"
#include "common_msgs_humble/msg/detail/lonlatmappoints__functions.h"
#include "common_msgs_humble/msg/detail/lonlatmappoints__struct.h"


// Include directives for member types
// Member `mapname`
// Member `zonename`
#include "rosidl_runtime_c/string_functions.h"
// Member `points`
#include "common_msgs_humble/msg/lonlat.h"
// Member `points`
#include "common_msgs_humble/msg/detail/lonlat__rosidl_typesupport_introspection_c.h"

#ifdef __cplusplus
extern "C"
{
#endif

void common_msgs_humble__msg__Lonlatmappoints__rosidl_typesupport_introspection_c__Lonlatmappoints_init_function(
  void * message_memory, enum rosidl_runtime_c__message_initialization _init)
{
  // TODO(karsten1987): initializers are not yet implemented for typesupport c
  // see https://bgithub.xyz/ros2/ros2/issues/397
  (void) _init;
  common_msgs_humble__msg__Lonlatmappoints__init(message_memory);
}

void common_msgs_humble__msg__Lonlatmappoints__rosidl_typesupport_introspection_c__Lonlatmappoints_fini_function(void * message_memory)
{
  common_msgs_humble__msg__Lonlatmappoints__fini(message_memory);
}

size_t common_msgs_humble__msg__Lonlatmappoints__rosidl_typesupport_introspection_c__size_function__Lonlatmappoints__points(
  const void * untyped_member)
{
  const common_msgs_humble__msg__Lonlat__Sequence * member =
    (const common_msgs_humble__msg__Lonlat__Sequence *)(untyped_member);
  return member->size;
}

const void * common_msgs_humble__msg__Lonlatmappoints__rosidl_typesupport_introspection_c__get_const_function__Lonlatmappoints__points(
  const void * untyped_member, size_t index)
{
  const common_msgs_humble__msg__Lonlat__Sequence * member =
    (const common_msgs_humble__msg__Lonlat__Sequence *)(untyped_member);
  return &member->data[index];
}

void * common_msgs_humble__msg__Lonlatmappoints__rosidl_typesupport_introspection_c__get_function__Lonlatmappoints__points(
  void * untyped_member, size_t index)
{
  common_msgs_humble__msg__Lonlat__Sequence * member =
    (common_msgs_humble__msg__Lonlat__Sequence *)(untyped_member);
  return &member->data[index];
}

void common_msgs_humble__msg__Lonlatmappoints__rosidl_typesupport_introspection_c__fetch_function__Lonlatmappoints__points(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const common_msgs_humble__msg__Lonlat * item =
    ((const common_msgs_humble__msg__Lonlat *)
    common_msgs_humble__msg__Lonlatmappoints__rosidl_typesupport_introspection_c__get_const_function__Lonlatmappoints__points(untyped_member, index));
  common_msgs_humble__msg__Lonlat * value =
    (common_msgs_humble__msg__Lonlat *)(untyped_value);
  *value = *item;
}

void common_msgs_humble__msg__Lonlatmappoints__rosidl_typesupport_introspection_c__assign_function__Lonlatmappoints__points(
  void * untyped_member, size_t index, const void * untyped_value)
{
  common_msgs_humble__msg__Lonlat * item =
    ((common_msgs_humble__msg__Lonlat *)
    common_msgs_humble__msg__Lonlatmappoints__rosidl_typesupport_introspection_c__get_function__Lonlatmappoints__points(untyped_member, index));
  const common_msgs_humble__msg__Lonlat * value =
    (const common_msgs_humble__msg__Lonlat *)(untyped_value);
  *item = *value;
}

bool common_msgs_humble__msg__Lonlatmappoints__rosidl_typesupport_introspection_c__resize_function__Lonlatmappoints__points(
  void * untyped_member, size_t size)
{
  common_msgs_humble__msg__Lonlat__Sequence * member =
    (common_msgs_humble__msg__Lonlat__Sequence *)(untyped_member);
  common_msgs_humble__msg__Lonlat__Sequence__fini(member);
  return common_msgs_humble__msg__Lonlat__Sequence__init(member, size);
}

static rosidl_typesupport_introspection_c__MessageMember common_msgs_humble__msg__Lonlatmappoints__rosidl_typesupport_introspection_c__Lonlatmappoints_message_member_array[4] = {
  {
    "mapname",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_STRING,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Lonlatmappoints, mapname),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "zonename",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_STRING,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Lonlatmappoints, zonename),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "points",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message (initialized later)
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Lonlatmappoints, points),  // bytes offset in struct
    NULL,  // default value
    common_msgs_humble__msg__Lonlatmappoints__rosidl_typesupport_introspection_c__size_function__Lonlatmappoints__points,  // size() function pointer
    common_msgs_humble__msg__Lonlatmappoints__rosidl_typesupport_introspection_c__get_const_function__Lonlatmappoints__points,  // get_const(index) function pointer
    common_msgs_humble__msg__Lonlatmappoints__rosidl_typesupport_introspection_c__get_function__Lonlatmappoints__points,  // get(index) function pointer
    common_msgs_humble__msg__Lonlatmappoints__rosidl_typesupport_introspection_c__fetch_function__Lonlatmappoints__points,  // fetch(index, &value) function pointer
    common_msgs_humble__msg__Lonlatmappoints__rosidl_typesupport_introspection_c__assign_function__Lonlatmappoints__points,  // assign(index, value) function pointer
    common_msgs_humble__msg__Lonlatmappoints__rosidl_typesupport_introspection_c__resize_function__Lonlatmappoints__points  // resize(index) function pointer
  },
  {
    "timestamp",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_INT64,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Lonlatmappoints, timestamp),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  }
};

static const rosidl_typesupport_introspection_c__MessageMembers common_msgs_humble__msg__Lonlatmappoints__rosidl_typesupport_introspection_c__Lonlatmappoints_message_members = {
  "common_msgs_humble__msg",  // message namespace
  "Lonlatmappoints",  // message name
  4,  // number of fields
  sizeof(common_msgs_humble__msg__Lonlatmappoints),
  common_msgs_humble__msg__Lonlatmappoints__rosidl_typesupport_introspection_c__Lonlatmappoints_message_member_array,  // message members
  common_msgs_humble__msg__Lonlatmappoints__rosidl_typesupport_introspection_c__Lonlatmappoints_init_function,  // function to initialize message memory (memory has to be allocated)
  common_msgs_humble__msg__Lonlatmappoints__rosidl_typesupport_introspection_c__Lonlatmappoints_fini_function  // function to terminate message instance (will not free memory)
};

// this is not const since it must be initialized on first access
// since C does not allow non-integral compile-time constants
static rosidl_message_type_support_t common_msgs_humble__msg__Lonlatmappoints__rosidl_typesupport_introspection_c__Lonlatmappoints_message_type_support_handle = {
  0,
  &common_msgs_humble__msg__Lonlatmappoints__rosidl_typesupport_introspection_c__Lonlatmappoints_message_members,
  get_message_typesupport_handle_function,
};

ROSIDL_TYPESUPPORT_INTROSPECTION_C_EXPORT_common_msgs_humble
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, common_msgs_humble, msg, Lonlatmappoints)() {
  common_msgs_humble__msg__Lonlatmappoints__rosidl_typesupport_introspection_c__Lonlatmappoints_message_member_array[2].members_ =
    ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, common_msgs_humble, msg, Lonlat)();
  if (!common_msgs_humble__msg__Lonlatmappoints__rosidl_typesupport_introspection_c__Lonlatmappoints_message_type_support_handle.typesupport_identifier) {
    common_msgs_humble__msg__Lonlatmappoints__rosidl_typesupport_introspection_c__Lonlatmappoints_message_type_support_handle.typesupport_identifier =
      rosidl_typesupport_introspection_c__identifier;
  }
  return &common_msgs_humble__msg__Lonlatmappoints__rosidl_typesupport_introspection_c__Lonlatmappoints_message_type_support_handle;
}
#ifdef __cplusplus
}
#endif
