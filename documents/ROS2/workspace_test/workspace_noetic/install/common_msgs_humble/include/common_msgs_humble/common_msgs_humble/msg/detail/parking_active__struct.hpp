// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/ParkingActive.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__PARKING_ACTIVE__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__PARKING_ACTIVE__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'iekulist'
#include "common_msgs_humble/msg/detail/ieku__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__ParkingActive __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__ParkingActive __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct ParkingActive_
{
  using Type = ParkingActive_<ContainerAllocator>;

  explicit ParkingActive_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->timestamp = 0ll;
      this->stage = 0;
      this->tips = 0;
      this->answer = 0;
      this->iekutargetid = 0;
      this->driveout = 0;
      this->stop_parking = 0;
    }
  }

  explicit ParkingActive_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->timestamp = 0ll;
      this->stage = 0;
      this->tips = 0;
      this->answer = 0;
      this->iekutargetid = 0;
      this->driveout = 0;
      this->stop_parking = 0;
    }
  }

  // field types and members
  using _timestamp_type =
    int64_t;
  _timestamp_type timestamp;
  using _stage_type =
    uint8_t;
  _stage_type stage;
  using _tips_type =
    uint8_t;
  _tips_type tips;
  using _answer_type =
    uint8_t;
  _answer_type answer;
  using _iekulist_type =
    std::vector<common_msgs_humble::msg::Ieku_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<common_msgs_humble::msg::Ieku_<ContainerAllocator>>>;
  _iekulist_type iekulist;
  using _iekutargetid_type =
    uint8_t;
  _iekutargetid_type iekutargetid;
  using _driveout_type =
    uint8_t;
  _driveout_type driveout;
  using _stop_parking_type =
    uint8_t;
  _stop_parking_type stop_parking;

  // setters for named parameter idiom
  Type & set__timestamp(
    const int64_t & _arg)
  {
    this->timestamp = _arg;
    return *this;
  }
  Type & set__stage(
    const uint8_t & _arg)
  {
    this->stage = _arg;
    return *this;
  }
  Type & set__tips(
    const uint8_t & _arg)
  {
    this->tips = _arg;
    return *this;
  }
  Type & set__answer(
    const uint8_t & _arg)
  {
    this->answer = _arg;
    return *this;
  }
  Type & set__iekulist(
    const std::vector<common_msgs_humble::msg::Ieku_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<common_msgs_humble::msg::Ieku_<ContainerAllocator>>> & _arg)
  {
    this->iekulist = _arg;
    return *this;
  }
  Type & set__iekutargetid(
    const uint8_t & _arg)
  {
    this->iekutargetid = _arg;
    return *this;
  }
  Type & set__driveout(
    const uint8_t & _arg)
  {
    this->driveout = _arg;
    return *this;
  }
  Type & set__stop_parking(
    const uint8_t & _arg)
  {
    this->stop_parking = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::ParkingActive_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::ParkingActive_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::ParkingActive_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::ParkingActive_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::ParkingActive_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::ParkingActive_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::ParkingActive_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::ParkingActive_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::ParkingActive_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::ParkingActive_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__ParkingActive
    std::shared_ptr<common_msgs_humble::msg::ParkingActive_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__ParkingActive
    std::shared_ptr<common_msgs_humble::msg::ParkingActive_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const ParkingActive_ & other) const
  {
    if (this->timestamp != other.timestamp) {
      return false;
    }
    if (this->stage != other.stage) {
      return false;
    }
    if (this->tips != other.tips) {
      return false;
    }
    if (this->answer != other.answer) {
      return false;
    }
    if (this->iekulist != other.iekulist) {
      return false;
    }
    if (this->iekutargetid != other.iekutargetid) {
      return false;
    }
    if (this->driveout != other.driveout) {
      return false;
    }
    if (this->stop_parking != other.stop_parking) {
      return false;
    }
    return true;
  }
  bool operator!=(const ParkingActive_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct ParkingActive_

// alias to use template instance with default allocator
using ParkingActive =
  common_msgs_humble::msg::ParkingActive_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__PARKING_ACTIVE__STRUCT_HPP_
