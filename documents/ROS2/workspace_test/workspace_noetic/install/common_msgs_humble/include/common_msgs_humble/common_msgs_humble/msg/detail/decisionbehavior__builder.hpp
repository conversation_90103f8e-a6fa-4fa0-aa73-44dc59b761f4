// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Decisionbehavior.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__DECISIONBEHAVIOR__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__DECISIONBEHAVIOR__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/decisionbehavior__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Decisionbehavior_virtualpointtype
{
public:
  explicit Init_Decisionbehavior_virtualpointtype(::common_msgs_humble::msg::Decisionbehavior & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Decisionbehavior virtualpointtype(::common_msgs_humble::msg::Decisionbehavior::_virtualpointtype_type arg)
  {
    msg_.virtualpointtype = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Decisionbehavior msg_;
};

class Init_Decisionbehavior_needreplan
{
public:
  explicit Init_Decisionbehavior_needreplan(::common_msgs_humble::msg::Decisionbehavior & msg)
  : msg_(msg)
  {}
  Init_Decisionbehavior_virtualpointtype needreplan(::common_msgs_humble::msg::Decisionbehavior::_needreplan_type arg)
  {
    msg_.needreplan = std::move(arg);
    return Init_Decisionbehavior_virtualpointtype(msg_);
  }

private:
  ::common_msgs_humble::msg::Decisionbehavior msg_;
};

class Init_Decisionbehavior_stationblock
{
public:
  explicit Init_Decisionbehavior_stationblock(::common_msgs_humble::msg::Decisionbehavior & msg)
  : msg_(msg)
  {}
  Init_Decisionbehavior_needreplan stationblock(::common_msgs_humble::msg::Decisionbehavior::_stationblock_type arg)
  {
    msg_.stationblock = std::move(arg);
    return Init_Decisionbehavior_needreplan(msg_);
  }

private:
  ::common_msgs_humble::msg::Decisionbehavior msg_;
};

class Init_Decisionbehavior_carworkstatus
{
public:
  explicit Init_Decisionbehavior_carworkstatus(::common_msgs_humble::msg::Decisionbehavior & msg)
  : msg_(msg)
  {}
  Init_Decisionbehavior_stationblock carworkstatus(::common_msgs_humble::msg::Decisionbehavior::_carworkstatus_type arg)
  {
    msg_.carworkstatus = std::move(arg);
    return Init_Decisionbehavior_stationblock(msg_);
  }

private:
  ::common_msgs_humble::msg::Decisionbehavior msg_;
};

class Init_Decisionbehavior_endtime
{
public:
  explicit Init_Decisionbehavior_endtime(::common_msgs_humble::msg::Decisionbehavior & msg)
  : msg_(msg)
  {}
  Init_Decisionbehavior_carworkstatus endtime(::common_msgs_humble::msg::Decisionbehavior::_endtime_type arg)
  {
    msg_.endtime = std::move(arg);
    return Init_Decisionbehavior_carworkstatus(msg_);
  }

private:
  ::common_msgs_humble::msg::Decisionbehavior msg_;
};

class Init_Decisionbehavior_starttime
{
public:
  explicit Init_Decisionbehavior_starttime(::common_msgs_humble::msg::Decisionbehavior & msg)
  : msg_(msg)
  {}
  Init_Decisionbehavior_endtime starttime(::common_msgs_humble::msg::Decisionbehavior::_starttime_type arg)
  {
    msg_.starttime = std::move(arg);
    return Init_Decisionbehavior_endtime(msg_);
  }

private:
  ::common_msgs_humble::msg::Decisionbehavior msg_;
};

class Init_Decisionbehavior_deviation
{
public:
  explicit Init_Decisionbehavior_deviation(::common_msgs_humble::msg::Decisionbehavior & msg)
  : msg_(msg)
  {}
  Init_Decisionbehavior_starttime deviation(::common_msgs_humble::msg::Decisionbehavior::_deviation_type arg)
  {
    msg_.deviation = std::move(arg);
    return Init_Decisionbehavior_starttime(msg_);
  }

private:
  ::common_msgs_humble::msg::Decisionbehavior msg_;
};

class Init_Decisionbehavior_alert
{
public:
  explicit Init_Decisionbehavior_alert(::common_msgs_humble::msg::Decisionbehavior & msg)
  : msg_(msg)
  {}
  Init_Decisionbehavior_deviation alert(::common_msgs_humble::msg::Decisionbehavior::_alert_type arg)
  {
    msg_.alert = std::move(arg);
    return Init_Decisionbehavior_deviation(msg_);
  }

private:
  ::common_msgs_humble::msg::Decisionbehavior msg_;
};

class Init_Decisionbehavior_avoidsituation
{
public:
  explicit Init_Decisionbehavior_avoidsituation(::common_msgs_humble::msg::Decisionbehavior & msg)
  : msg_(msg)
  {}
  Init_Decisionbehavior_alert avoidsituation(::common_msgs_humble::msg::Decisionbehavior::_avoidsituation_type arg)
  {
    msg_.avoidsituation = std::move(arg);
    return Init_Decisionbehavior_alert(msg_);
  }

private:
  ::common_msgs_humble::msg::Decisionbehavior msg_;
};

class Init_Decisionbehavior_guidespeed
{
public:
  explicit Init_Decisionbehavior_guidespeed(::common_msgs_humble::msg::Decisionbehavior & msg)
  : msg_(msg)
  {}
  Init_Decisionbehavior_avoidsituation guidespeed(::common_msgs_humble::msg::Decisionbehavior::_guidespeed_type arg)
  {
    msg_.guidespeed = std::move(arg);
    return Init_Decisionbehavior_avoidsituation(msg_);
  }

private:
  ::common_msgs_humble::msg::Decisionbehavior msg_;
};

class Init_Decisionbehavior_mergetrigger
{
public:
  explicit Init_Decisionbehavior_mergetrigger(::common_msgs_humble::msg::Decisionbehavior & msg)
  : msg_(msg)
  {}
  Init_Decisionbehavior_guidespeed mergetrigger(::common_msgs_humble::msg::Decisionbehavior::_mergetrigger_type arg)
  {
    msg_.mergetrigger = std::move(arg);
    return Init_Decisionbehavior_guidespeed(msg_);
  }

private:
  ::common_msgs_humble::msg::Decisionbehavior msg_;
};

class Init_Decisionbehavior_timestamp
{
public:
  explicit Init_Decisionbehavior_timestamp(::common_msgs_humble::msg::Decisionbehavior & msg)
  : msg_(msg)
  {}
  Init_Decisionbehavior_mergetrigger timestamp(::common_msgs_humble::msg::Decisionbehavior::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return Init_Decisionbehavior_mergetrigger(msg_);
  }

private:
  ::common_msgs_humble::msg::Decisionbehavior msg_;
};

class Init_Decisionbehavior_door
{
public:
  explicit Init_Decisionbehavior_door(::common_msgs_humble::msg::Decisionbehavior & msg)
  : msg_(msg)
  {}
  Init_Decisionbehavior_timestamp door(::common_msgs_humble::msg::Decisionbehavior::_door_type arg)
  {
    msg_.door = std::move(arg);
    return Init_Decisionbehavior_timestamp(msg_);
  }

private:
  ::common_msgs_humble::msg::Decisionbehavior msg_;
};

class Init_Decisionbehavior_laneblock
{
public:
  explicit Init_Decisionbehavior_laneblock(::common_msgs_humble::msg::Decisionbehavior & msg)
  : msg_(msg)
  {}
  Init_Decisionbehavior_door laneblock(::common_msgs_humble::msg::Decisionbehavior::_laneblock_type arg)
  {
    msg_.laneblock = std::move(arg);
    return Init_Decisionbehavior_door(msg_);
  }

private:
  ::common_msgs_humble::msg::Decisionbehavior msg_;
};

class Init_Decisionbehavior_turnlights
{
public:
  explicit Init_Decisionbehavior_turnlights(::common_msgs_humble::msg::Decisionbehavior & msg)
  : msg_(msg)
  {}
  Init_Decisionbehavior_laneblock turnlights(::common_msgs_humble::msg::Decisionbehavior::_turnlights_type arg)
  {
    msg_.turnlights = std::move(arg);
    return Init_Decisionbehavior_laneblock(msg_);
  }

private:
  ::common_msgs_humble::msg::Decisionbehavior msg_;
};

class Init_Decisionbehavior_isvalid
{
public:
  explicit Init_Decisionbehavior_isvalid(::common_msgs_humble::msg::Decisionbehavior & msg)
  : msg_(msg)
  {}
  Init_Decisionbehavior_turnlights isvalid(::common_msgs_humble::msg::Decisionbehavior::_isvalid_type arg)
  {
    msg_.isvalid = std::move(arg);
    return Init_Decisionbehavior_turnlights(msg_);
  }

private:
  ::common_msgs_humble::msg::Decisionbehavior msg_;
};

class Init_Decisionbehavior_obs
{
public:
  explicit Init_Decisionbehavior_obs(::common_msgs_humble::msg::Decisionbehavior & msg)
  : msg_(msg)
  {}
  Init_Decisionbehavior_isvalid obs(::common_msgs_humble::msg::Decisionbehavior::_obs_type arg)
  {
    msg_.obs = std::move(arg);
    return Init_Decisionbehavior_isvalid(msg_);
  }

private:
  ::common_msgs_humble::msg::Decisionbehavior msg_;
};

class Init_Decisionbehavior_drivebehavior
{
public:
  Init_Decisionbehavior_drivebehavior()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Decisionbehavior_obs drivebehavior(::common_msgs_humble::msg::Decisionbehavior::_drivebehavior_type arg)
  {
    msg_.drivebehavior = std::move(arg);
    return Init_Decisionbehavior_obs(msg_);
  }

private:
  ::common_msgs_humble::msg::Decisionbehavior msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Decisionbehavior>()
{
  return common_msgs_humble::msg::builder::Init_Decisionbehavior_drivebehavior();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__DECISIONBEHAVIOR__BUILDER_HPP_
