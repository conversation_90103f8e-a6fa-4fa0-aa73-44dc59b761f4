// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from common_msgs_humble:msg/Obutrafficlights.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUTRAFFICLIGHTS__TRAITS_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUTRAFFICLIGHTS__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "common_msgs_humble/msg/detail/obutrafficlights__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

// Include directives for member types
// Member 'light'
#include "common_msgs_humble/msg/detail/obulight__traits.hpp"

namespace common_msgs_humble
{

namespace msg
{

inline void to_flow_style_yaml(
  const Obutrafficlights & msg,
  std::ostream & out)
{
  out << "{";
  // member: timestamp
  {
    out << "timestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.timestamp, out);
    out << ", ";
  }

  // member: region_id
  {
    out << "region_id: ";
    rosidl_generator_traits::value_to_yaml(msg.region_id, out);
    out << ", ";
  }

  // member: node_id
  {
    out << "node_id: ";
    rosidl_generator_traits::value_to_yaml(msg.node_id, out);
    out << ", ";
  }

  // member: light_status
  {
    out << "light_status: ";
    rosidl_generator_traits::value_to_yaml(msg.light_status, out);
    out << ", ";
  }

  // member: phase_cnt
  {
    out << "phase_cnt: ";
    rosidl_generator_traits::value_to_yaml(msg.phase_cnt, out);
    out << ", ";
  }

  // member: light
  {
    if (msg.light.size() == 0) {
      out << "light: []";
    } else {
      out << "light: [";
      size_t pending_items = msg.light.size();
      for (auto item : msg.light) {
        to_flow_style_yaml(item, out);
        if (--pending_items > 0) {
          out << ", ";
        }
      }
      out << "]";
    }
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const Obutrafficlights & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: timestamp
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "timestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.timestamp, out);
    out << "\n";
  }

  // member: region_id
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "region_id: ";
    rosidl_generator_traits::value_to_yaml(msg.region_id, out);
    out << "\n";
  }

  // member: node_id
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "node_id: ";
    rosidl_generator_traits::value_to_yaml(msg.node_id, out);
    out << "\n";
  }

  // member: light_status
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "light_status: ";
    rosidl_generator_traits::value_to_yaml(msg.light_status, out);
    out << "\n";
  }

  // member: phase_cnt
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "phase_cnt: ";
    rosidl_generator_traits::value_to_yaml(msg.phase_cnt, out);
    out << "\n";
  }

  // member: light
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    if (msg.light.size() == 0) {
      out << "light: []\n";
    } else {
      out << "light:\n";
      for (auto item : msg.light) {
        if (indentation > 0) {
          out << std::string(indentation, ' ');
        }
        out << "-\n";
        to_block_style_yaml(item, out, indentation + 2);
      }
    }
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const Obutrafficlights & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace common_msgs_humble

namespace rosidl_generator_traits
{

[[deprecated("use common_msgs_humble::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const common_msgs_humble::msg::Obutrafficlights & msg,
  std::ostream & out, size_t indentation = 0)
{
  common_msgs_humble::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use common_msgs_humble::msg::to_yaml() instead")]]
inline std::string to_yaml(const common_msgs_humble::msg::Obutrafficlights & msg)
{
  return common_msgs_humble::msg::to_yaml(msg);
}

template<>
inline const char * data_type<common_msgs_humble::msg::Obutrafficlights>()
{
  return "common_msgs_humble::msg::Obutrafficlights";
}

template<>
inline const char * name<common_msgs_humble::msg::Obutrafficlights>()
{
  return "common_msgs_humble/msg/Obutrafficlights";
}

template<>
struct has_fixed_size<common_msgs_humble::msg::Obutrafficlights>
  : std::integral_constant<bool, false> {};

template<>
struct has_bounded_size<common_msgs_humble::msg::Obutrafficlights>
  : std::integral_constant<bool, false> {};

template<>
struct is_message<common_msgs_humble::msg::Obutrafficlights>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUTRAFFICLIGHTS__TRAITS_HPP_
