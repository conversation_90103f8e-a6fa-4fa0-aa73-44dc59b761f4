// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Hdintersectionstoglobal.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__HDINTERSECTIONSTOGLOBAL__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__HDINTERSECTIONSTOGLOBAL__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/hdintersectionstoglobal__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Hdintersectionstoglobal_timestamp
{
public:
  explicit Init_Hdintersectionstoglobal_timestamp(::common_msgs_humble::msg::Hdintersectionstoglobal & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Hdintersectionstoglobal timestamp(::common_msgs_humble::msg::Hdintersectionstoglobal::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Hdintersectionstoglobal msg_;
};

class Init_Hdintersectionstoglobal_isvalid
{
public:
  explicit Init_Hdintersectionstoglobal_isvalid(::common_msgs_humble::msg::Hdintersectionstoglobal & msg)
  : msg_(msg)
  {}
  Init_Hdintersectionstoglobal_timestamp isvalid(::common_msgs_humble::msg::Hdintersectionstoglobal::_isvalid_type arg)
  {
    msg_.isvalid = std::move(arg);
    return Init_Hdintersectionstoglobal_timestamp(msg_);
  }

private:
  ::common_msgs_humble::msg::Hdintersectionstoglobal msg_;
};

class Init_Hdintersectionstoglobal_intersectionmaps
{
public:
  Init_Hdintersectionstoglobal_intersectionmaps()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Hdintersectionstoglobal_isvalid intersectionmaps(::common_msgs_humble::msg::Hdintersectionstoglobal::_intersectionmaps_type arg)
  {
    msg_.intersectionmaps = std::move(arg);
    return Init_Hdintersectionstoglobal_isvalid(msg_);
  }

private:
  ::common_msgs_humble::msg::Hdintersectionstoglobal msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Hdintersectionstoglobal>()
{
  return common_msgs_humble::msg::builder::Init_Hdintersectionstoglobal_intersectionmaps();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__HDINTERSECTIONSTOGLOBAL__BUILDER_HPP_
