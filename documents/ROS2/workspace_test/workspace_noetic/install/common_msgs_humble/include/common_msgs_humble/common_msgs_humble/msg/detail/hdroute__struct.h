// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Hdroute.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__HDROUTE__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__HDROUTE__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'map'
#include "common_msgs_humble/msg/detail/hdmap__struct.h"

/// Struct defined in msg/Hdroute in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Hdroute
{
  common_msgs_humble__msg__Hdmap__Sequence map;
  uint8_t isvalid;
  int64_t timestamp;
  int64_t index;
} common_msgs_humble__msg__Hdroute;

// Struct for a sequence of common_msgs_humble__msg__Hdroute.
typedef struct common_msgs_humble__msg__Hdroute__Sequence
{
  common_msgs_humble__msg__Hdroute * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Hdroute__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__HDROUTE__STRUCT_H_
