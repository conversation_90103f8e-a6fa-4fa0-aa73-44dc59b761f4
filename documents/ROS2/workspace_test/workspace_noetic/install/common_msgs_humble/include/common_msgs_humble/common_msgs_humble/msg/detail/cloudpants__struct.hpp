// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Cloudpants.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__CLOUDPANTS__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__CLOUDPANTS__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'pants'
#include "common_msgs_humble/msg/detail/cloudpant__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Cloudpants __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Cloudpants __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Cloudpants_
{
  using Type = Cloudpants_<ContainerAllocator>;

  explicit Cloudpants_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->timestamp = 0ll;
      this->frameid = 0l;
      this->count = 0l;
    }
  }

  explicit Cloudpants_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->timestamp = 0ll;
      this->frameid = 0l;
      this->count = 0l;
    }
  }

  // field types and members
  using _timestamp_type =
    int64_t;
  _timestamp_type timestamp;
  using _frameid_type =
    int32_t;
  _frameid_type frameid;
  using _count_type =
    int32_t;
  _count_type count;
  using _pants_type =
    std::vector<common_msgs_humble::msg::Cloudpant_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<common_msgs_humble::msg::Cloudpant_<ContainerAllocator>>>;
  _pants_type pants;

  // setters for named parameter idiom
  Type & set__timestamp(
    const int64_t & _arg)
  {
    this->timestamp = _arg;
    return *this;
  }
  Type & set__frameid(
    const int32_t & _arg)
  {
    this->frameid = _arg;
    return *this;
  }
  Type & set__count(
    const int32_t & _arg)
  {
    this->count = _arg;
    return *this;
  }
  Type & set__pants(
    const std::vector<common_msgs_humble::msg::Cloudpant_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<common_msgs_humble::msg::Cloudpant_<ContainerAllocator>>> & _arg)
  {
    this->pants = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Cloudpants_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Cloudpants_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Cloudpants_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Cloudpants_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Cloudpants_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Cloudpants_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Cloudpants_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Cloudpants_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Cloudpants_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Cloudpants_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Cloudpants
    std::shared_ptr<common_msgs_humble::msg::Cloudpants_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Cloudpants
    std::shared_ptr<common_msgs_humble::msg::Cloudpants_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Cloudpants_ & other) const
  {
    if (this->timestamp != other.timestamp) {
      return false;
    }
    if (this->frameid != other.frameid) {
      return false;
    }
    if (this->count != other.count) {
      return false;
    }
    if (this->pants != other.pants) {
      return false;
    }
    return true;
  }
  bool operator!=(const Cloudpants_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Cloudpants_

// alias to use template instance with default allocator
using Cloudpants =
  common_msgs_humble::msg::Cloudpants_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__CLOUDPANTS__STRUCT_HPP_
