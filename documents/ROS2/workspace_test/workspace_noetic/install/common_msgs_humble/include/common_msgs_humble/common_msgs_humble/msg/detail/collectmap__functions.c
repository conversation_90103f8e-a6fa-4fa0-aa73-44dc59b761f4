// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from common_msgs_humble:msg/Collectmap.idl
// generated code does not contain a copyright notice
#include "common_msgs_humble/msg/detail/collectmap__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


bool
common_msgs_humble__msg__Collectmap__init(common_msgs_humble__msg__Collectmap * msg)
{
  if (!msg) {
    return false;
  }
  // mapname
  // zonename
  // property
  // laneattr
  // speed
  // sideroadwidth
  // mergelanetype
  // sensorlanetype
  // leftsearchdis
  // rightsearchdis
  // timestamp
  // lanewidth
  // leftlanewidth
  // rightlanewidth
  // laneswitch
  // sidepass
  // lanenum
  // lanesite
  return true;
}

void
common_msgs_humble__msg__Collectmap__fini(common_msgs_humble__msg__Collectmap * msg)
{
  if (!msg) {
    return;
  }
  // mapname
  // zonename
  // property
  // laneattr
  // speed
  // sideroadwidth
  // mergelanetype
  // sensorlanetype
  // leftsearchdis
  // rightsearchdis
  // timestamp
  // lanewidth
  // leftlanewidth
  // rightlanewidth
  // laneswitch
  // sidepass
  // lanenum
  // lanesite
}

bool
common_msgs_humble__msg__Collectmap__are_equal(const common_msgs_humble__msg__Collectmap * lhs, const common_msgs_humble__msg__Collectmap * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // mapname
  if (lhs->mapname != rhs->mapname) {
    return false;
  }
  // zonename
  if (lhs->zonename != rhs->zonename) {
    return false;
  }
  // property
  if (lhs->property != rhs->property) {
    return false;
  }
  // laneattr
  if (lhs->laneattr != rhs->laneattr) {
    return false;
  }
  // speed
  if (lhs->speed != rhs->speed) {
    return false;
  }
  // sideroadwidth
  if (lhs->sideroadwidth != rhs->sideroadwidth) {
    return false;
  }
  // mergelanetype
  if (lhs->mergelanetype != rhs->mergelanetype) {
    return false;
  }
  // sensorlanetype
  if (lhs->sensorlanetype != rhs->sensorlanetype) {
    return false;
  }
  // leftsearchdis
  if (lhs->leftsearchdis != rhs->leftsearchdis) {
    return false;
  }
  // rightsearchdis
  if (lhs->rightsearchdis != rhs->rightsearchdis) {
    return false;
  }
  // timestamp
  if (lhs->timestamp != rhs->timestamp) {
    return false;
  }
  // lanewidth
  if (lhs->lanewidth != rhs->lanewidth) {
    return false;
  }
  // leftlanewidth
  if (lhs->leftlanewidth != rhs->leftlanewidth) {
    return false;
  }
  // rightlanewidth
  if (lhs->rightlanewidth != rhs->rightlanewidth) {
    return false;
  }
  // laneswitch
  if (lhs->laneswitch != rhs->laneswitch) {
    return false;
  }
  // sidepass
  if (lhs->sidepass != rhs->sidepass) {
    return false;
  }
  // lanenum
  if (lhs->lanenum != rhs->lanenum) {
    return false;
  }
  // lanesite
  if (lhs->lanesite != rhs->lanesite) {
    return false;
  }
  return true;
}

bool
common_msgs_humble__msg__Collectmap__copy(
  const common_msgs_humble__msg__Collectmap * input,
  common_msgs_humble__msg__Collectmap * output)
{
  if (!input || !output) {
    return false;
  }
  // mapname
  output->mapname = input->mapname;
  // zonename
  output->zonename = input->zonename;
  // property
  output->property = input->property;
  // laneattr
  output->laneattr = input->laneattr;
  // speed
  output->speed = input->speed;
  // sideroadwidth
  output->sideroadwidth = input->sideroadwidth;
  // mergelanetype
  output->mergelanetype = input->mergelanetype;
  // sensorlanetype
  output->sensorlanetype = input->sensorlanetype;
  // leftsearchdis
  output->leftsearchdis = input->leftsearchdis;
  // rightsearchdis
  output->rightsearchdis = input->rightsearchdis;
  // timestamp
  output->timestamp = input->timestamp;
  // lanewidth
  output->lanewidth = input->lanewidth;
  // leftlanewidth
  output->leftlanewidth = input->leftlanewidth;
  // rightlanewidth
  output->rightlanewidth = input->rightlanewidth;
  // laneswitch
  output->laneswitch = input->laneswitch;
  // sidepass
  output->sidepass = input->sidepass;
  // lanenum
  output->lanenum = input->lanenum;
  // lanesite
  output->lanesite = input->lanesite;
  return true;
}

common_msgs_humble__msg__Collectmap *
common_msgs_humble__msg__Collectmap__create()
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Collectmap * msg = (common_msgs_humble__msg__Collectmap *)allocator.allocate(sizeof(common_msgs_humble__msg__Collectmap), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(common_msgs_humble__msg__Collectmap));
  bool success = common_msgs_humble__msg__Collectmap__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
common_msgs_humble__msg__Collectmap__destroy(common_msgs_humble__msg__Collectmap * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    common_msgs_humble__msg__Collectmap__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
common_msgs_humble__msg__Collectmap__Sequence__init(common_msgs_humble__msg__Collectmap__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Collectmap * data = NULL;

  if (size) {
    data = (common_msgs_humble__msg__Collectmap *)allocator.zero_allocate(size, sizeof(common_msgs_humble__msg__Collectmap), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = common_msgs_humble__msg__Collectmap__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        common_msgs_humble__msg__Collectmap__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
common_msgs_humble__msg__Collectmap__Sequence__fini(common_msgs_humble__msg__Collectmap__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      common_msgs_humble__msg__Collectmap__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

common_msgs_humble__msg__Collectmap__Sequence *
common_msgs_humble__msg__Collectmap__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Collectmap__Sequence * array = (common_msgs_humble__msg__Collectmap__Sequence *)allocator.allocate(sizeof(common_msgs_humble__msg__Collectmap__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = common_msgs_humble__msg__Collectmap__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
common_msgs_humble__msg__Collectmap__Sequence__destroy(common_msgs_humble__msg__Collectmap__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    common_msgs_humble__msg__Collectmap__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
common_msgs_humble__msg__Collectmap__Sequence__are_equal(const common_msgs_humble__msg__Collectmap__Sequence * lhs, const common_msgs_humble__msg__Collectmap__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!common_msgs_humble__msg__Collectmap__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
common_msgs_humble__msg__Collectmap__Sequence__copy(
  const common_msgs_humble__msg__Collectmap__Sequence * input,
  common_msgs_humble__msg__Collectmap__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(common_msgs_humble__msg__Collectmap);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    common_msgs_humble__msg__Collectmap * data =
      (common_msgs_humble__msg__Collectmap *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!common_msgs_humble__msg__Collectmap__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          common_msgs_humble__msg__Collectmap__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!common_msgs_humble__msg__Collectmap__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
