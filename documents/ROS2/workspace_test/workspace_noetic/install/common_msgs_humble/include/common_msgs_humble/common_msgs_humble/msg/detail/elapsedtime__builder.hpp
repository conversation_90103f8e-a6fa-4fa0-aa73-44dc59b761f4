// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Elapsedtime.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__ELAPSEDTIME__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__ELAPSEDTIME__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/elapsedtime__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Elapsedtime_time
{
public:
  Init_Elapsedtime_time()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  ::common_msgs_humble::msg::Elapsedtime time(::common_msgs_humble::msg::Elapsedtime::_time_type arg)
  {
    msg_.time = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Elapsedtime msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Elapsedtime>()
{
  return common_msgs_humble::msg::builder::Init_Elapsedtime_time();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__ELAPSEDTIME__BUILDER_HPP_
