// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from common_msgs_humble:msg/Objecthistory.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__OBJECTHISTORY__TRAITS_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__OBJECTHISTORY__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "common_msgs_humble/msg/detail/objecthistory__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

// Include directives for member types
// Member 'trajectorypoint'
#include "common_msgs_humble/msg/detail/point3d__traits.hpp"

namespace common_msgs_humble
{

namespace msg
{

inline void to_flow_style_yaml(
  const Objecthistory & msg,
  std::ostream & out)
{
  out << "{";
  // member: timestamp
  {
    out << "timestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.timestamp, out);
    out << ", ";
  }

  // member: trajectorypoint
  {
    out << "trajectorypoint: ";
    to_flow_style_yaml(msg.trajectorypoint, out);
    out << ", ";
  }

  // member: lon
  {
    out << "lon: ";
    rosidl_generator_traits::value_to_yaml(msg.lon, out);
    out << ", ";
  }

  // member: lat
  {
    out << "lat: ";
    rosidl_generator_traits::value_to_yaml(msg.lat, out);
    out << ", ";
  }

  // member: alt
  {
    out << "alt: ";
    rosidl_generator_traits::value_to_yaml(msg.alt, out);
    out << ", ";
  }

  // member: roll
  {
    out << "roll: ";
    rosidl_generator_traits::value_to_yaml(msg.roll, out);
    out << ", ";
  }

  // member: pitch
  {
    out << "pitch: ";
    rosidl_generator_traits::value_to_yaml(msg.pitch, out);
    out << ", ";
  }

  // member: heading
  {
    out << "heading: ";
    rosidl_generator_traits::value_to_yaml(msg.heading, out);
    out << ", ";
  }

  // member: relavx
  {
    out << "relavx: ";
    rosidl_generator_traits::value_to_yaml(msg.relavx, out);
    out << ", ";
  }

  // member: relavy
  {
    out << "relavy: ";
    rosidl_generator_traits::value_to_yaml(msg.relavy, out);
    out << ", ";
  }

  // member: absvx
  {
    out << "absvx: ";
    rosidl_generator_traits::value_to_yaml(msg.absvx, out);
    out << ", ";
  }

  // member: absvy
  {
    out << "absvy: ";
    rosidl_generator_traits::value_to_yaml(msg.absvy, out);
    out << ", ";
  }

  // member: s
  {
    out << "s: ";
    rosidl_generator_traits::value_to_yaml(msg.s, out);
    out << ", ";
  }

  // member: l
  {
    out << "l: ";
    rosidl_generator_traits::value_to_yaml(msg.l, out);
    out << ", ";
  }

  // member: speeds
  {
    out << "speeds: ";
    rosidl_generator_traits::value_to_yaml(msg.speeds, out);
    out << ", ";
  }

  // member: speedl
  {
    out << "speedl: ";
    rosidl_generator_traits::value_to_yaml(msg.speedl, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const Objecthistory & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: timestamp
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "timestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.timestamp, out);
    out << "\n";
  }

  // member: trajectorypoint
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "trajectorypoint:\n";
    to_block_style_yaml(msg.trajectorypoint, out, indentation + 2);
  }

  // member: lon
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lon: ";
    rosidl_generator_traits::value_to_yaml(msg.lon, out);
    out << "\n";
  }

  // member: lat
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lat: ";
    rosidl_generator_traits::value_to_yaml(msg.lat, out);
    out << "\n";
  }

  // member: alt
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "alt: ";
    rosidl_generator_traits::value_to_yaml(msg.alt, out);
    out << "\n";
  }

  // member: roll
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "roll: ";
    rosidl_generator_traits::value_to_yaml(msg.roll, out);
    out << "\n";
  }

  // member: pitch
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "pitch: ";
    rosidl_generator_traits::value_to_yaml(msg.pitch, out);
    out << "\n";
  }

  // member: heading
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "heading: ";
    rosidl_generator_traits::value_to_yaml(msg.heading, out);
    out << "\n";
  }

  // member: relavx
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "relavx: ";
    rosidl_generator_traits::value_to_yaml(msg.relavx, out);
    out << "\n";
  }

  // member: relavy
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "relavy: ";
    rosidl_generator_traits::value_to_yaml(msg.relavy, out);
    out << "\n";
  }

  // member: absvx
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "absvx: ";
    rosidl_generator_traits::value_to_yaml(msg.absvx, out);
    out << "\n";
  }

  // member: absvy
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "absvy: ";
    rosidl_generator_traits::value_to_yaml(msg.absvy, out);
    out << "\n";
  }

  // member: s
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "s: ";
    rosidl_generator_traits::value_to_yaml(msg.s, out);
    out << "\n";
  }

  // member: l
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "l: ";
    rosidl_generator_traits::value_to_yaml(msg.l, out);
    out << "\n";
  }

  // member: speeds
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "speeds: ";
    rosidl_generator_traits::value_to_yaml(msg.speeds, out);
    out << "\n";
  }

  // member: speedl
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "speedl: ";
    rosidl_generator_traits::value_to_yaml(msg.speedl, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const Objecthistory & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace common_msgs_humble

namespace rosidl_generator_traits
{

[[deprecated("use common_msgs_humble::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const common_msgs_humble::msg::Objecthistory & msg,
  std::ostream & out, size_t indentation = 0)
{
  common_msgs_humble::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use common_msgs_humble::msg::to_yaml() instead")]]
inline std::string to_yaml(const common_msgs_humble::msg::Objecthistory & msg)
{
  return common_msgs_humble::msg::to_yaml(msg);
}

template<>
inline const char * data_type<common_msgs_humble::msg::Objecthistory>()
{
  return "common_msgs_humble::msg::Objecthistory";
}

template<>
inline const char * name<common_msgs_humble::msg::Objecthistory>()
{
  return "common_msgs_humble/msg/Objecthistory";
}

template<>
struct has_fixed_size<common_msgs_humble::msg::Objecthistory>
  : std::integral_constant<bool, has_fixed_size<common_msgs_humble::msg::Point3d>::value> {};

template<>
struct has_bounded_size<common_msgs_humble::msg::Objecthistory>
  : std::integral_constant<bool, has_bounded_size<common_msgs_humble::msg::Point3d>::value> {};

template<>
struct is_message<common_msgs_humble::msg::Objecthistory>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__OBJECTHISTORY__TRAITS_HPP_
