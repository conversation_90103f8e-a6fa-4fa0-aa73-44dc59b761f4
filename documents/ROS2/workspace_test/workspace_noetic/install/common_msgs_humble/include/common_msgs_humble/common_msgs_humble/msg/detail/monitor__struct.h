// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Monitor.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__MONITOR__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__MONITOR__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'valuelight'
// Member 'valuetext'
// Member 'dotcnt'
#include "rosidl_runtime_c/primitives_sequence.h"
// Member 'deslight'
// Member 'destext'
#include "rosidl_runtime_c/string.h"

/// Struct defined in msg/Monitor in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Monitor
{
  rosidl_runtime_c__octet__Sequence valuelight;
  rosidl_runtime_c__String__Sequence deslight;
  rosidl_runtime_c__double__Sequence valuetext;
  rosidl_runtime_c__String__Sequence destext;
  rosidl_runtime_c__octet__Sequence dotcnt;
  int64_t timestamp;
  int64_t status;
  int64_t sensorstate;
} common_msgs_humble__msg__Monitor;

// Struct for a sequence of common_msgs_humble__msg__Monitor.
typedef struct common_msgs_humble__msg__Monitor__Sequence
{
  common_msgs_humble__msg__Monitor * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Monitor__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__MONITOR__STRUCT_H_
