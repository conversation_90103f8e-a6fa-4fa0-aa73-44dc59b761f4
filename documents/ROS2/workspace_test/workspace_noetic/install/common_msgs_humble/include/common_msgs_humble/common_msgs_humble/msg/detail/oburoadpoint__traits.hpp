// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from common_msgs_humble:msg/Oburoadpoint.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUROADPOINT__TRAITS_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUROADPOINT__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "common_msgs_humble/msg/detail/oburoadpoint__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

namespace common_msgs_humble
{

namespace msg
{

inline void to_flow_style_yaml(
  const Oburoadpoint & msg,
  std::ostream & out)
{
  out << "{";
  // member: lon
  {
    out << "lon: ";
    rosidl_generator_traits::value_to_yaml(msg.lon, out);
    out << ", ";
  }

  // member: lat
  {
    out << "lat: ";
    rosidl_generator_traits::value_to_yaml(msg.lat, out);
    out << ", ";
  }

  // member: speed
  {
    out << "speed: ";
    rosidl_generator_traits::value_to_yaml(msg.speed, out);
    out << ", ";
  }

  // member: accel
  {
    out << "accel: ";
    rosidl_generator_traits::value_to_yaml(msg.accel, out);
    out << ", ";
  }

  // member: heading
  {
    out << "heading: ";
    rosidl_generator_traits::value_to_yaml(msg.heading, out);
    out << ", ";
  }

  // member: availability
  {
    out << "availability: ";
    rosidl_generator_traits::value_to_yaml(msg.availability, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const Oburoadpoint & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: lon
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lon: ";
    rosidl_generator_traits::value_to_yaml(msg.lon, out);
    out << "\n";
  }

  // member: lat
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lat: ";
    rosidl_generator_traits::value_to_yaml(msg.lat, out);
    out << "\n";
  }

  // member: speed
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "speed: ";
    rosidl_generator_traits::value_to_yaml(msg.speed, out);
    out << "\n";
  }

  // member: accel
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "accel: ";
    rosidl_generator_traits::value_to_yaml(msg.accel, out);
    out << "\n";
  }

  // member: heading
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "heading: ";
    rosidl_generator_traits::value_to_yaml(msg.heading, out);
    out << "\n";
  }

  // member: availability
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "availability: ";
    rosidl_generator_traits::value_to_yaml(msg.availability, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const Oburoadpoint & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace common_msgs_humble

namespace rosidl_generator_traits
{

[[deprecated("use common_msgs_humble::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const common_msgs_humble::msg::Oburoadpoint & msg,
  std::ostream & out, size_t indentation = 0)
{
  common_msgs_humble::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use common_msgs_humble::msg::to_yaml() instead")]]
inline std::string to_yaml(const common_msgs_humble::msg::Oburoadpoint & msg)
{
  return common_msgs_humble::msg::to_yaml(msg);
}

template<>
inline const char * data_type<common_msgs_humble::msg::Oburoadpoint>()
{
  return "common_msgs_humble::msg::Oburoadpoint";
}

template<>
inline const char * name<common_msgs_humble::msg::Oburoadpoint>()
{
  return "common_msgs_humble/msg/Oburoadpoint";
}

template<>
struct has_fixed_size<common_msgs_humble::msg::Oburoadpoint>
  : std::integral_constant<bool, true> {};

template<>
struct has_bounded_size<common_msgs_humble::msg::Oburoadpoint>
  : std::integral_constant<bool, true> {};

template<>
struct is_message<common_msgs_humble::msg::Oburoadpoint>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUROADPOINT__TRAITS_HPP_
