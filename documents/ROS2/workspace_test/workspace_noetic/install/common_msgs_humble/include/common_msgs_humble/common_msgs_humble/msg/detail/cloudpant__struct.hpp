// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Cloudpant.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__CLOUDPANT__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__CLOUDPANT__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Cloudpant __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Cloudpant __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Cloudpant_
{
  using Type = Cloudpant_<ContainerAllocator>;

  explicit Cloudpant_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->id = "";
      this->vehicletype = 0l;
      this->length = 0.0f;
      this->width = 0.0f;
      this->height = 0.0f;
      this->longitude = 0.0;
      this->latitude = 0.0;
      this->locationconfidence = 0l;
      this->speed = 0.0f;
      this->courseangle = 0.0f;
      this->sportconfidence = 0l;
    }
  }

  explicit Cloudpant_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : id(_alloc)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->id = "";
      this->vehicletype = 0l;
      this->length = 0.0f;
      this->width = 0.0f;
      this->height = 0.0f;
      this->longitude = 0.0;
      this->latitude = 0.0;
      this->locationconfidence = 0l;
      this->speed = 0.0f;
      this->courseangle = 0.0f;
      this->sportconfidence = 0l;
    }
  }

  // field types and members
  using _id_type =
    std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>;
  _id_type id;
  using _vehicletype_type =
    int32_t;
  _vehicletype_type vehicletype;
  using _length_type =
    float;
  _length_type length;
  using _width_type =
    float;
  _width_type width;
  using _height_type =
    float;
  _height_type height;
  using _longitude_type =
    double;
  _longitude_type longitude;
  using _latitude_type =
    double;
  _latitude_type latitude;
  using _locationconfidence_type =
    int32_t;
  _locationconfidence_type locationconfidence;
  using _speed_type =
    float;
  _speed_type speed;
  using _courseangle_type =
    float;
  _courseangle_type courseangle;
  using _sportconfidence_type =
    int32_t;
  _sportconfidence_type sportconfidence;

  // setters for named parameter idiom
  Type & set__id(
    const std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> & _arg)
  {
    this->id = _arg;
    return *this;
  }
  Type & set__vehicletype(
    const int32_t & _arg)
  {
    this->vehicletype = _arg;
    return *this;
  }
  Type & set__length(
    const float & _arg)
  {
    this->length = _arg;
    return *this;
  }
  Type & set__width(
    const float & _arg)
  {
    this->width = _arg;
    return *this;
  }
  Type & set__height(
    const float & _arg)
  {
    this->height = _arg;
    return *this;
  }
  Type & set__longitude(
    const double & _arg)
  {
    this->longitude = _arg;
    return *this;
  }
  Type & set__latitude(
    const double & _arg)
  {
    this->latitude = _arg;
    return *this;
  }
  Type & set__locationconfidence(
    const int32_t & _arg)
  {
    this->locationconfidence = _arg;
    return *this;
  }
  Type & set__speed(
    const float & _arg)
  {
    this->speed = _arg;
    return *this;
  }
  Type & set__courseangle(
    const float & _arg)
  {
    this->courseangle = _arg;
    return *this;
  }
  Type & set__sportconfidence(
    const int32_t & _arg)
  {
    this->sportconfidence = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Cloudpant_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Cloudpant_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Cloudpant_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Cloudpant_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Cloudpant_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Cloudpant_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Cloudpant_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Cloudpant_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Cloudpant_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Cloudpant_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Cloudpant
    std::shared_ptr<common_msgs_humble::msg::Cloudpant_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Cloudpant
    std::shared_ptr<common_msgs_humble::msg::Cloudpant_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Cloudpant_ & other) const
  {
    if (this->id != other.id) {
      return false;
    }
    if (this->vehicletype != other.vehicletype) {
      return false;
    }
    if (this->length != other.length) {
      return false;
    }
    if (this->width != other.width) {
      return false;
    }
    if (this->height != other.height) {
      return false;
    }
    if (this->longitude != other.longitude) {
      return false;
    }
    if (this->latitude != other.latitude) {
      return false;
    }
    if (this->locationconfidence != other.locationconfidence) {
      return false;
    }
    if (this->speed != other.speed) {
      return false;
    }
    if (this->courseangle != other.courseangle) {
      return false;
    }
    if (this->sportconfidence != other.sportconfidence) {
      return false;
    }
    return true;
  }
  bool operator!=(const Cloudpant_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Cloudpant_

// alias to use template instance with default allocator
using Cloudpant =
  common_msgs_humble::msg::Cloudpant_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__CLOUDPANT__STRUCT_HPP_
