// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from common_msgs_humble:msg/Objectprediction.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__OBJECTPREDICTION__TRAITS_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__OBJECTPREDICTION__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "common_msgs_humble/msg/detail/objectprediction__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

namespace common_msgs_humble
{

namespace msg
{

inline void to_flow_style_yaml(
  const Objectprediction & msg,
  std::ostream & out)
{
  out << "{";
  // member: timestep
  {
    out << "timestep: ";
    rosidl_generator_traits::value_to_yaml(msg.timestep, out);
    out << ", ";
  }

  // member: x
  {
    out << "x: ";
    rosidl_generator_traits::value_to_yaml(msg.x, out);
    out << ", ";
  }

  // member: y
  {
    out << "y: ";
    rosidl_generator_traits::value_to_yaml(msg.y, out);
    out << ", ";
  }

  // member: z
  {
    out << "z: ";
    rosidl_generator_traits::value_to_yaml(msg.z, out);
    out << ", ";
  }

  // member: longtitude
  {
    out << "longtitude: ";
    rosidl_generator_traits::value_to_yaml(msg.longtitude, out);
    out << ", ";
  }

  // member: latitude
  {
    out << "latitude: ";
    rosidl_generator_traits::value_to_yaml(msg.latitude, out);
    out << ", ";
  }

  // member: altitude
  {
    out << "altitude: ";
    rosidl_generator_traits::value_to_yaml(msg.altitude, out);
    out << ", ";
  }

  // member: rollrad
  {
    out << "rollrad: ";
    rosidl_generator_traits::value_to_yaml(msg.rollrad, out);
    out << ", ";
  }

  // member: pitchrad
  {
    out << "pitchrad: ";
    rosidl_generator_traits::value_to_yaml(msg.pitchrad, out);
    out << ", ";
  }

  // member: azimuth
  {
    out << "azimuth: ";
    rosidl_generator_traits::value_to_yaml(msg.azimuth, out);
    out << ", ";
  }

  // member: relavx
  {
    out << "relavx: ";
    rosidl_generator_traits::value_to_yaml(msg.relavx, out);
    out << ", ";
  }

  // member: relavy
  {
    out << "relavy: ";
    rosidl_generator_traits::value_to_yaml(msg.relavy, out);
    out << ", ";
  }

  // member: absvx
  {
    out << "absvx: ";
    rosidl_generator_traits::value_to_yaml(msg.absvx, out);
    out << ", ";
  }

  // member: absvy
  {
    out << "absvy: ";
    rosidl_generator_traits::value_to_yaml(msg.absvy, out);
    out << ", ";
  }

  // member: heading
  {
    out << "heading: ";
    rosidl_generator_traits::value_to_yaml(msg.heading, out);
    out << ", ";
  }

  // member: s
  {
    out << "s: ";
    rosidl_generator_traits::value_to_yaml(msg.s, out);
    out << ", ";
  }

  // member: l
  {
    out << "l: ";
    rosidl_generator_traits::value_to_yaml(msg.l, out);
    out << ", ";
  }

  // member: speeds
  {
    out << "speeds: ";
    rosidl_generator_traits::value_to_yaml(msg.speeds, out);
    out << ", ";
  }

  // member: speedl
  {
    out << "speedl: ";
    rosidl_generator_traits::value_to_yaml(msg.speedl, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const Objectprediction & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: timestep
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "timestep: ";
    rosidl_generator_traits::value_to_yaml(msg.timestep, out);
    out << "\n";
  }

  // member: x
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "x: ";
    rosidl_generator_traits::value_to_yaml(msg.x, out);
    out << "\n";
  }

  // member: y
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "y: ";
    rosidl_generator_traits::value_to_yaml(msg.y, out);
    out << "\n";
  }

  // member: z
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "z: ";
    rosidl_generator_traits::value_to_yaml(msg.z, out);
    out << "\n";
  }

  // member: longtitude
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "longtitude: ";
    rosidl_generator_traits::value_to_yaml(msg.longtitude, out);
    out << "\n";
  }

  // member: latitude
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "latitude: ";
    rosidl_generator_traits::value_to_yaml(msg.latitude, out);
    out << "\n";
  }

  // member: altitude
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "altitude: ";
    rosidl_generator_traits::value_to_yaml(msg.altitude, out);
    out << "\n";
  }

  // member: rollrad
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "rollrad: ";
    rosidl_generator_traits::value_to_yaml(msg.rollrad, out);
    out << "\n";
  }

  // member: pitchrad
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "pitchrad: ";
    rosidl_generator_traits::value_to_yaml(msg.pitchrad, out);
    out << "\n";
  }

  // member: azimuth
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "azimuth: ";
    rosidl_generator_traits::value_to_yaml(msg.azimuth, out);
    out << "\n";
  }

  // member: relavx
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "relavx: ";
    rosidl_generator_traits::value_to_yaml(msg.relavx, out);
    out << "\n";
  }

  // member: relavy
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "relavy: ";
    rosidl_generator_traits::value_to_yaml(msg.relavy, out);
    out << "\n";
  }

  // member: absvx
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "absvx: ";
    rosidl_generator_traits::value_to_yaml(msg.absvx, out);
    out << "\n";
  }

  // member: absvy
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "absvy: ";
    rosidl_generator_traits::value_to_yaml(msg.absvy, out);
    out << "\n";
  }

  // member: heading
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "heading: ";
    rosidl_generator_traits::value_to_yaml(msg.heading, out);
    out << "\n";
  }

  // member: s
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "s: ";
    rosidl_generator_traits::value_to_yaml(msg.s, out);
    out << "\n";
  }

  // member: l
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "l: ";
    rosidl_generator_traits::value_to_yaml(msg.l, out);
    out << "\n";
  }

  // member: speeds
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "speeds: ";
    rosidl_generator_traits::value_to_yaml(msg.speeds, out);
    out << "\n";
  }

  // member: speedl
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "speedl: ";
    rosidl_generator_traits::value_to_yaml(msg.speedl, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const Objectprediction & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace common_msgs_humble

namespace rosidl_generator_traits
{

[[deprecated("use common_msgs_humble::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const common_msgs_humble::msg::Objectprediction & msg,
  std::ostream & out, size_t indentation = 0)
{
  common_msgs_humble::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use common_msgs_humble::msg::to_yaml() instead")]]
inline std::string to_yaml(const common_msgs_humble::msg::Objectprediction & msg)
{
  return common_msgs_humble::msg::to_yaml(msg);
}

template<>
inline const char * data_type<common_msgs_humble::msg::Objectprediction>()
{
  return "common_msgs_humble::msg::Objectprediction";
}

template<>
inline const char * name<common_msgs_humble::msg::Objectprediction>()
{
  return "common_msgs_humble/msg/Objectprediction";
}

template<>
struct has_fixed_size<common_msgs_humble::msg::Objectprediction>
  : std::integral_constant<bool, true> {};

template<>
struct has_bounded_size<common_msgs_humble::msg::Objectprediction>
  : std::integral_constant<bool, true> {};

template<>
struct is_message<common_msgs_humble::msg::Objectprediction>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__OBJECTPREDICTION__TRAITS_HPP_
