// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Controllon.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__CONTROLLON__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__CONTROLLON__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Controllon __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Controllon __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Controllon_
{
  using Type = Controllon_<ContainerAllocator>;

  explicit Controllon_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->espmethod = 0;
      this->gpsdis = 0.0f;
      this->epbmethod = 0;
      this->epb = 0;
      this->geermethod = 0;
      this->gear = 0;
      this->brakemethod = 0;
      this->brakepedal = 0.0f;
      this->gaspedal = 0.0f;
      this->station = 0;
      this->light = 0;
      this->pcmethod = 0;
      this->objdis = 0.0f;
      this->objrel = 0.0f;
      this->mode = 0;
      this->isvalid = 0;
      this->timestamp = 0ll;
      this->targetspeed = 0.0f;
      this->apadis = 0.0f;
      this->objtype = 0;
    }
  }

  explicit Controllon_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->espmethod = 0;
      this->gpsdis = 0.0f;
      this->epbmethod = 0;
      this->epb = 0;
      this->geermethod = 0;
      this->gear = 0;
      this->brakemethod = 0;
      this->brakepedal = 0.0f;
      this->gaspedal = 0.0f;
      this->station = 0;
      this->light = 0;
      this->pcmethod = 0;
      this->objdis = 0.0f;
      this->objrel = 0.0f;
      this->mode = 0;
      this->isvalid = 0;
      this->timestamp = 0ll;
      this->targetspeed = 0.0f;
      this->apadis = 0.0f;
      this->objtype = 0;
    }
  }

  // field types and members
  using _espmethod_type =
    uint8_t;
  _espmethod_type espmethod;
  using _gpsdis_type =
    float;
  _gpsdis_type gpsdis;
  using _epbmethod_type =
    uint8_t;
  _epbmethod_type epbmethod;
  using _epb_type =
    uint8_t;
  _epb_type epb;
  using _geermethod_type =
    uint8_t;
  _geermethod_type geermethod;
  using _gear_type =
    uint8_t;
  _gear_type gear;
  using _brakemethod_type =
    uint8_t;
  _brakemethod_type brakemethod;
  using _brakepedal_type =
    float;
  _brakepedal_type brakepedal;
  using _gaspedal_type =
    float;
  _gaspedal_type gaspedal;
  using _station_type =
    uint8_t;
  _station_type station;
  using _light_type =
    uint8_t;
  _light_type light;
  using _pcmethod_type =
    uint8_t;
  _pcmethod_type pcmethod;
  using _objdis_type =
    float;
  _objdis_type objdis;
  using _objrel_type =
    float;
  _objrel_type objrel;
  using _mode_type =
    uint8_t;
  _mode_type mode;
  using _isvalid_type =
    uint8_t;
  _isvalid_type isvalid;
  using _timestamp_type =
    int64_t;
  _timestamp_type timestamp;
  using _targetspeed_type =
    float;
  _targetspeed_type targetspeed;
  using _apadis_type =
    float;
  _apadis_type apadis;
  using _objtype_type =
    uint8_t;
  _objtype_type objtype;

  // setters for named parameter idiom
  Type & set__espmethod(
    const uint8_t & _arg)
  {
    this->espmethod = _arg;
    return *this;
  }
  Type & set__gpsdis(
    const float & _arg)
  {
    this->gpsdis = _arg;
    return *this;
  }
  Type & set__epbmethod(
    const uint8_t & _arg)
  {
    this->epbmethod = _arg;
    return *this;
  }
  Type & set__epb(
    const uint8_t & _arg)
  {
    this->epb = _arg;
    return *this;
  }
  Type & set__geermethod(
    const uint8_t & _arg)
  {
    this->geermethod = _arg;
    return *this;
  }
  Type & set__gear(
    const uint8_t & _arg)
  {
    this->gear = _arg;
    return *this;
  }
  Type & set__brakemethod(
    const uint8_t & _arg)
  {
    this->brakemethod = _arg;
    return *this;
  }
  Type & set__brakepedal(
    const float & _arg)
  {
    this->brakepedal = _arg;
    return *this;
  }
  Type & set__gaspedal(
    const float & _arg)
  {
    this->gaspedal = _arg;
    return *this;
  }
  Type & set__station(
    const uint8_t & _arg)
  {
    this->station = _arg;
    return *this;
  }
  Type & set__light(
    const uint8_t & _arg)
  {
    this->light = _arg;
    return *this;
  }
  Type & set__pcmethod(
    const uint8_t & _arg)
  {
    this->pcmethod = _arg;
    return *this;
  }
  Type & set__objdis(
    const float & _arg)
  {
    this->objdis = _arg;
    return *this;
  }
  Type & set__objrel(
    const float & _arg)
  {
    this->objrel = _arg;
    return *this;
  }
  Type & set__mode(
    const uint8_t & _arg)
  {
    this->mode = _arg;
    return *this;
  }
  Type & set__isvalid(
    const uint8_t & _arg)
  {
    this->isvalid = _arg;
    return *this;
  }
  Type & set__timestamp(
    const int64_t & _arg)
  {
    this->timestamp = _arg;
    return *this;
  }
  Type & set__targetspeed(
    const float & _arg)
  {
    this->targetspeed = _arg;
    return *this;
  }
  Type & set__apadis(
    const float & _arg)
  {
    this->apadis = _arg;
    return *this;
  }
  Type & set__objtype(
    const uint8_t & _arg)
  {
    this->objtype = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Controllon_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Controllon_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Controllon_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Controllon_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Controllon_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Controllon_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Controllon_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Controllon_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Controllon_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Controllon_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Controllon
    std::shared_ptr<common_msgs_humble::msg::Controllon_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Controllon
    std::shared_ptr<common_msgs_humble::msg::Controllon_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Controllon_ & other) const
  {
    if (this->espmethod != other.espmethod) {
      return false;
    }
    if (this->gpsdis != other.gpsdis) {
      return false;
    }
    if (this->epbmethod != other.epbmethod) {
      return false;
    }
    if (this->epb != other.epb) {
      return false;
    }
    if (this->geermethod != other.geermethod) {
      return false;
    }
    if (this->gear != other.gear) {
      return false;
    }
    if (this->brakemethod != other.brakemethod) {
      return false;
    }
    if (this->brakepedal != other.brakepedal) {
      return false;
    }
    if (this->gaspedal != other.gaspedal) {
      return false;
    }
    if (this->station != other.station) {
      return false;
    }
    if (this->light != other.light) {
      return false;
    }
    if (this->pcmethod != other.pcmethod) {
      return false;
    }
    if (this->objdis != other.objdis) {
      return false;
    }
    if (this->objrel != other.objrel) {
      return false;
    }
    if (this->mode != other.mode) {
      return false;
    }
    if (this->isvalid != other.isvalid) {
      return false;
    }
    if (this->timestamp != other.timestamp) {
      return false;
    }
    if (this->targetspeed != other.targetspeed) {
      return false;
    }
    if (this->apadis != other.apadis) {
      return false;
    }
    if (this->objtype != other.objtype) {
      return false;
    }
    return true;
  }
  bool operator!=(const Controllon_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Controllon_

// alias to use template instance with default allocator
using Controllon =
  common_msgs_humble::msg::Controllon_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__CONTROLLON__STRUCT_HPP_
