// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from common_msgs_humble:msg/Sensorgps.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSORGPS__TRAITS_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSORGPS__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "common_msgs_humble/msg/detail/sensorgps__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

namespace common_msgs_humble
{

namespace msg
{

inline void to_flow_style_yaml(
  const Sensorgps & msg,
  std::ostream & out)
{
  out << "{";
  // member: lon
  {
    out << "lon: ";
    rosidl_generator_traits::value_to_yaml(msg.lon, out);
    out << ", ";
  }

  // member: lat
  {
    out << "lat: ";
    rosidl_generator_traits::value_to_yaml(msg.lat, out);
    out << ", ";
  }

  // member: alt
  {
    out << "alt: ";
    rosidl_generator_traits::value_to_yaml(msg.alt, out);
    out << ", ";
  }

  // member: roadtype
  {
    out << "roadtype: ";
    rosidl_generator_traits::value_to_yaml(msg.roadtype, out);
    out << ", ";
  }

  // member: lanetype
  {
    out << "lanetype: ";
    rosidl_generator_traits::value_to_yaml(msg.lanetype, out);
    out << ", ";
  }

  // member: heading
  {
    out << "heading: ";
    rosidl_generator_traits::value_to_yaml(msg.heading, out);
    out << ", ";
  }

  // member: pitch
  {
    out << "pitch: ";
    rosidl_generator_traits::value_to_yaml(msg.pitch, out);
    out << ", ";
  }

  // member: roll
  {
    out << "roll: ";
    rosidl_generator_traits::value_to_yaml(msg.roll, out);
    out << ", ";
  }

  // member: pitchrate
  {
    out << "pitchrate: ";
    rosidl_generator_traits::value_to_yaml(msg.pitchrate, out);
    out << ", ";
  }

  // member: rollrate
  {
    out << "rollrate: ";
    rosidl_generator_traits::value_to_yaml(msg.rollrate, out);
    out << ", ";
  }

  // member: yawrate
  {
    out << "yawrate: ";
    rosidl_generator_traits::value_to_yaml(msg.yawrate, out);
    out << ", ";
  }

  // member: accx
  {
    out << "accx: ";
    rosidl_generator_traits::value_to_yaml(msg.accx, out);
    out << ", ";
  }

  // member: accy
  {
    out << "accy: ";
    rosidl_generator_traits::value_to_yaml(msg.accy, out);
    out << ", ";
  }

  // member: accz
  {
    out << "accz: ";
    rosidl_generator_traits::value_to_yaml(msg.accz, out);
    out << ", ";
  }

  // member: mile
  {
    out << "mile: ";
    rosidl_generator_traits::value_to_yaml(msg.mile, out);
    out << ", ";
  }

  // member: velocity
  {
    out << "velocity: ";
    rosidl_generator_traits::value_to_yaml(msg.velocity, out);
    out << ", ";
  }

  // member: status
  {
    out << "status: ";
    rosidl_generator_traits::value_to_yaml(msg.status, out);
    out << ", ";
  }

  // member: rawstatus
  {
    out << "rawstatus: ";
    rosidl_generator_traits::value_to_yaml(msg.rawstatus, out);
    out << ", ";
  }

  // member: satenum
  {
    out << "satenum: ";
    rosidl_generator_traits::value_to_yaml(msg.satenum, out);
    out << ", ";
  }

  // member: gpstime
  {
    out << "gpstime: ";
    rosidl_generator_traits::value_to_yaml(msg.gpstime, out);
    out << ", ";
  }

  // member: isvalid
  {
    out << "isvalid: ";
    rosidl_generator_traits::value_to_yaml(msg.isvalid, out);
    out << ", ";
  }

  // member: timestamp
  {
    out << "timestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.timestamp, out);
    out << ", ";
  }

  // member: speed_n
  {
    out << "speed_n: ";
    rosidl_generator_traits::value_to_yaml(msg.speed_n, out);
    out << ", ";
  }

  // member: speed_e
  {
    out << "speed_e: ";
    rosidl_generator_traits::value_to_yaml(msg.speed_e, out);
    out << ", ";
  }

  // member: speed_d
  {
    out << "speed_d: ";
    rosidl_generator_traits::value_to_yaml(msg.speed_d, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const Sensorgps & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: lon
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lon: ";
    rosidl_generator_traits::value_to_yaml(msg.lon, out);
    out << "\n";
  }

  // member: lat
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lat: ";
    rosidl_generator_traits::value_to_yaml(msg.lat, out);
    out << "\n";
  }

  // member: alt
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "alt: ";
    rosidl_generator_traits::value_to_yaml(msg.alt, out);
    out << "\n";
  }

  // member: roadtype
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "roadtype: ";
    rosidl_generator_traits::value_to_yaml(msg.roadtype, out);
    out << "\n";
  }

  // member: lanetype
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lanetype: ";
    rosidl_generator_traits::value_to_yaml(msg.lanetype, out);
    out << "\n";
  }

  // member: heading
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "heading: ";
    rosidl_generator_traits::value_to_yaml(msg.heading, out);
    out << "\n";
  }

  // member: pitch
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "pitch: ";
    rosidl_generator_traits::value_to_yaml(msg.pitch, out);
    out << "\n";
  }

  // member: roll
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "roll: ";
    rosidl_generator_traits::value_to_yaml(msg.roll, out);
    out << "\n";
  }

  // member: pitchrate
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "pitchrate: ";
    rosidl_generator_traits::value_to_yaml(msg.pitchrate, out);
    out << "\n";
  }

  // member: rollrate
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "rollrate: ";
    rosidl_generator_traits::value_to_yaml(msg.rollrate, out);
    out << "\n";
  }

  // member: yawrate
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "yawrate: ";
    rosidl_generator_traits::value_to_yaml(msg.yawrate, out);
    out << "\n";
  }

  // member: accx
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "accx: ";
    rosidl_generator_traits::value_to_yaml(msg.accx, out);
    out << "\n";
  }

  // member: accy
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "accy: ";
    rosidl_generator_traits::value_to_yaml(msg.accy, out);
    out << "\n";
  }

  // member: accz
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "accz: ";
    rosidl_generator_traits::value_to_yaml(msg.accz, out);
    out << "\n";
  }

  // member: mile
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "mile: ";
    rosidl_generator_traits::value_to_yaml(msg.mile, out);
    out << "\n";
  }

  // member: velocity
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "velocity: ";
    rosidl_generator_traits::value_to_yaml(msg.velocity, out);
    out << "\n";
  }

  // member: status
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "status: ";
    rosidl_generator_traits::value_to_yaml(msg.status, out);
    out << "\n";
  }

  // member: rawstatus
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "rawstatus: ";
    rosidl_generator_traits::value_to_yaml(msg.rawstatus, out);
    out << "\n";
  }

  // member: satenum
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "satenum: ";
    rosidl_generator_traits::value_to_yaml(msg.satenum, out);
    out << "\n";
  }

  // member: gpstime
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "gpstime: ";
    rosidl_generator_traits::value_to_yaml(msg.gpstime, out);
    out << "\n";
  }

  // member: isvalid
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "isvalid: ";
    rosidl_generator_traits::value_to_yaml(msg.isvalid, out);
    out << "\n";
  }

  // member: timestamp
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "timestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.timestamp, out);
    out << "\n";
  }

  // member: speed_n
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "speed_n: ";
    rosidl_generator_traits::value_to_yaml(msg.speed_n, out);
    out << "\n";
  }

  // member: speed_e
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "speed_e: ";
    rosidl_generator_traits::value_to_yaml(msg.speed_e, out);
    out << "\n";
  }

  // member: speed_d
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "speed_d: ";
    rosidl_generator_traits::value_to_yaml(msg.speed_d, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const Sensorgps & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace common_msgs_humble

namespace rosidl_generator_traits
{

[[deprecated("use common_msgs_humble::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const common_msgs_humble::msg::Sensorgps & msg,
  std::ostream & out, size_t indentation = 0)
{
  common_msgs_humble::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use common_msgs_humble::msg::to_yaml() instead")]]
inline std::string to_yaml(const common_msgs_humble::msg::Sensorgps & msg)
{
  return common_msgs_humble::msg::to_yaml(msg);
}

template<>
inline const char * data_type<common_msgs_humble::msg::Sensorgps>()
{
  return "common_msgs_humble::msg::Sensorgps";
}

template<>
inline const char * name<common_msgs_humble::msg::Sensorgps>()
{
  return "common_msgs_humble/msg/Sensorgps";
}

template<>
struct has_fixed_size<common_msgs_humble::msg::Sensorgps>
  : std::integral_constant<bool, true> {};

template<>
struct has_bounded_size<common_msgs_humble::msg::Sensorgps>
  : std::integral_constant<bool, true> {};

template<>
struct is_message<common_msgs_humble::msg::Sensorgps>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSORGPS__TRAITS_HPP_
