// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Mapformat.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__MAPFORMAT__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__MAPFORMAT__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

/// Struct defined in msg/Mapformat in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Mapformat
{
  double lon;
  double lat;
  uint8_t roadtype;
  float speed;
  uint8_t lanetype;
  uint8_t mergelanetype;
  uint8_t sensorlanetype;
  uint8_t turnlight;
  float sideroadwidth;
  float lanewidth;
  float leftlanewidth;
  float rightlanewidth;
  float leftsearchdis;
  float rightsearchdis;
  float heading;
  float curvature;
  double gpstime;
  uint8_t switchflag;
  uint8_t borrowflag;
  uint8_t lanesum;
  uint8_t lanenum;
  uint8_t backup1;
  uint8_t backup2;
  uint8_t backup3;
  float backup4;
  float backup5;
  float backup6;
  double backup7;
  double backup8;
  double backup9;
} common_msgs_humble__msg__Mapformat;

// Struct for a sequence of common_msgs_humble__msg__Mapformat.
typedef struct common_msgs_humble__msg__Mapformat__Sequence
{
  common_msgs_humble__msg__Mapformat * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Mapformat__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__MAPFORMAT__STRUCT_H_
