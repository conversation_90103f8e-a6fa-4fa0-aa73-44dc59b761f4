// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Lonlatmappoints.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__LONLATMAPPOINTS__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__LONLATMAPPOINTS__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/lonlatmappoints__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Lonlatmappoints_timestamp
{
public:
  explicit Init_Lonlatmappoints_timestamp(::common_msgs_humble::msg::Lonlatmappoints & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Lonlatmappoints timestamp(::common_msgs_humble::msg::Lonlatmappoints::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Lonlatmappoints msg_;
};

class Init_Lonlatmappoints_points
{
public:
  explicit Init_Lonlatmappoints_points(::common_msgs_humble::msg::Lonlatmappoints & msg)
  : msg_(msg)
  {}
  Init_Lonlatmappoints_timestamp points(::common_msgs_humble::msg::Lonlatmappoints::_points_type arg)
  {
    msg_.points = std::move(arg);
    return Init_Lonlatmappoints_timestamp(msg_);
  }

private:
  ::common_msgs_humble::msg::Lonlatmappoints msg_;
};

class Init_Lonlatmappoints_zonename
{
public:
  explicit Init_Lonlatmappoints_zonename(::common_msgs_humble::msg::Lonlatmappoints & msg)
  : msg_(msg)
  {}
  Init_Lonlatmappoints_points zonename(::common_msgs_humble::msg::Lonlatmappoints::_zonename_type arg)
  {
    msg_.zonename = std::move(arg);
    return Init_Lonlatmappoints_points(msg_);
  }

private:
  ::common_msgs_humble::msg::Lonlatmappoints msg_;
};

class Init_Lonlatmappoints_mapname
{
public:
  Init_Lonlatmappoints_mapname()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Lonlatmappoints_zonename mapname(::common_msgs_humble::msg::Lonlatmappoints::_mapname_type arg)
  {
    msg_.mapname = std::move(arg);
    return Init_Lonlatmappoints_zonename(msg_);
  }

private:
  ::common_msgs_humble::msg::Lonlatmappoints msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Lonlatmappoints>()
{
  return common_msgs_humble::msg::builder::Init_Lonlatmappoints_mapname();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__LONLATMAPPOINTS__BUILDER_HPP_
