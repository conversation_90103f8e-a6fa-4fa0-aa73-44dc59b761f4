// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Hdintersectiontoglobal.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__HDINTERSECTIONTOGLOBAL__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__HDINTERSECTIONTOGLOBAL__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/hdintersectiontoglobal__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Hdintersectiontoglobal_id
{
public:
  explicit Init_Hdintersectiontoglobal_id(::common_msgs_humble::msg::Hdintersectiontoglobal & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Hdintersectiontoglobal id(::common_msgs_humble::msg::Hdintersectiontoglobal::_id_type arg)
  {
    msg_.id = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Hdintersectiontoglobal msg_;
};

class Init_Hdintersectiontoglobal_timestamp
{
public:
  explicit Init_Hdintersectiontoglobal_timestamp(::common_msgs_humble::msg::Hdintersectiontoglobal & msg)
  : msg_(msg)
  {}
  Init_Hdintersectiontoglobal_id timestamp(::common_msgs_humble::msg::Hdintersectiontoglobal::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return Init_Hdintersectiontoglobal_id(msg_);
  }

private:
  ::common_msgs_humble::msg::Hdintersectiontoglobal msg_;
};

class Init_Hdintersectiontoglobal_isvalid
{
public:
  explicit Init_Hdintersectiontoglobal_isvalid(::common_msgs_humble::msg::Hdintersectiontoglobal & msg)
  : msg_(msg)
  {}
  Init_Hdintersectiontoglobal_timestamp isvalid(::common_msgs_humble::msg::Hdintersectiontoglobal::_isvalid_type arg)
  {
    msg_.isvalid = std::move(arg);
    return Init_Hdintersectiontoglobal_timestamp(msg_);
  }

private:
  ::common_msgs_humble::msg::Hdintersectiontoglobal msg_;
};

class Init_Hdintersectiontoglobal_map
{
public:
  Init_Hdintersectiontoglobal_map()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Hdintersectiontoglobal_isvalid map(::common_msgs_humble::msg::Hdintersectiontoglobal::_map_type arg)
  {
    msg_.map = std::move(arg);
    return Init_Hdintersectiontoglobal_isvalid(msg_);
  }

private:
  ::common_msgs_humble::msg::Hdintersectiontoglobal msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Hdintersectiontoglobal>()
{
  return common_msgs_humble::msg::builder::Init_Hdintersectiontoglobal_map();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__HDINTERSECTIONTOGLOBAL__BUILDER_HPP_
