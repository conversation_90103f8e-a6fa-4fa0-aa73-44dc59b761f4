// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Intersectionroad.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__INTERSECTIONROAD__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__INTERSECTIONROAD__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'lane'
#include "common_msgs_humble/msg/detail/roadpoint__struct.h"

/// Struct defined in msg/Intersectionroad in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Intersectionroad
{
  common_msgs_humble__msg__Roadpoint__Sequence lane;
} common_msgs_humble__msg__Intersectionroad;

// Struct for a sequence of common_msgs_humble__msg__Intersectionroad.
typedef struct common_msgs_humble__msg__Intersectionroad__Sequence
{
  common_msgs_humble__msg__Intersectionroad * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Intersectionroad__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__INTERSECTIONROAD__STRUCT_H_
