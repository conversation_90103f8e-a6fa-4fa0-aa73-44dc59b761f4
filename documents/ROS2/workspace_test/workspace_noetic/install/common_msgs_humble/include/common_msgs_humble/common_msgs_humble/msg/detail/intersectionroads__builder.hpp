// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Intersectionroads.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__INTERSECTIONROADS__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__INTERSECTIONROADS__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/intersectionroads__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Intersectionroads_timestamp
{
public:
  explicit Init_Intersectionroads_timestamp(::common_msgs_humble::msg::Intersectionroads & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Intersectionroads timestamp(::common_msgs_humble::msg::Intersectionroads::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Intersectionroads msg_;
};

class Init_Intersectionroads_intersection
{
public:
  Init_Intersectionroads_intersection()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Intersectionroads_timestamp intersection(::common_msgs_humble::msg::Intersectionroads::_intersection_type arg)
  {
    msg_.intersection = std::move(arg);
    return Init_Intersectionroads_timestamp(msg_);
  }

private:
  ::common_msgs_humble::msg::Intersectionroads msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Intersectionroads>()
{
  return common_msgs_humble::msg::builder::Init_Intersectionroads_intersection();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__INTERSECTIONROADS__BUILDER_HPP_
