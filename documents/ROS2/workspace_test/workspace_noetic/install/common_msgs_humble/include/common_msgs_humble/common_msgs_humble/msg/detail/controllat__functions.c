// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from common_msgs_humble:msg/Controllat.idl
// generated code does not contain a copyright notice
#include "common_msgs_humble/msg/detail/controllat__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


bool
common_msgs_humble__msg__Controllat__init(common_msgs_humble__msg__Controllat * msg)
{
  if (!msg) {
    return false;
  }
  // epsmethod
  // epsangle
  // limitspeed
  // epstorque
  // lightmethod
  // lights
  // isvalid
  // deviation
  // timestamp
  // apavstatus
  // apsstate
  // curve
  return true;
}

void
common_msgs_humble__msg__Controllat__fini(common_msgs_humble__msg__Controllat * msg)
{
  if (!msg) {
    return;
  }
  // epsmethod
  // epsangle
  // limitspeed
  // epstorque
  // lightmethod
  // lights
  // isvalid
  // deviation
  // timestamp
  // apavstatus
  // apsstate
  // curve
}

bool
common_msgs_humble__msg__Controllat__are_equal(const common_msgs_humble__msg__Controllat * lhs, const common_msgs_humble__msg__Controllat * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // epsmethod
  if (lhs->epsmethod != rhs->epsmethod) {
    return false;
  }
  // epsangle
  if (lhs->epsangle != rhs->epsangle) {
    return false;
  }
  // limitspeed
  if (lhs->limitspeed != rhs->limitspeed) {
    return false;
  }
  // epstorque
  if (lhs->epstorque != rhs->epstorque) {
    return false;
  }
  // lightmethod
  if (lhs->lightmethod != rhs->lightmethod) {
    return false;
  }
  // lights
  if (lhs->lights != rhs->lights) {
    return false;
  }
  // isvalid
  if (lhs->isvalid != rhs->isvalid) {
    return false;
  }
  // deviation
  if (lhs->deviation != rhs->deviation) {
    return false;
  }
  // timestamp
  if (lhs->timestamp != rhs->timestamp) {
    return false;
  }
  // apavstatus
  if (lhs->apavstatus != rhs->apavstatus) {
    return false;
  }
  // apsstate
  if (lhs->apsstate != rhs->apsstate) {
    return false;
  }
  // curve
  if (lhs->curve != rhs->curve) {
    return false;
  }
  return true;
}

bool
common_msgs_humble__msg__Controllat__copy(
  const common_msgs_humble__msg__Controllat * input,
  common_msgs_humble__msg__Controllat * output)
{
  if (!input || !output) {
    return false;
  }
  // epsmethod
  output->epsmethod = input->epsmethod;
  // epsangle
  output->epsangle = input->epsangle;
  // limitspeed
  output->limitspeed = input->limitspeed;
  // epstorque
  output->epstorque = input->epstorque;
  // lightmethod
  output->lightmethod = input->lightmethod;
  // lights
  output->lights = input->lights;
  // isvalid
  output->isvalid = input->isvalid;
  // deviation
  output->deviation = input->deviation;
  // timestamp
  output->timestamp = input->timestamp;
  // apavstatus
  output->apavstatus = input->apavstatus;
  // apsstate
  output->apsstate = input->apsstate;
  // curve
  output->curve = input->curve;
  return true;
}

common_msgs_humble__msg__Controllat *
common_msgs_humble__msg__Controllat__create()
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Controllat * msg = (common_msgs_humble__msg__Controllat *)allocator.allocate(sizeof(common_msgs_humble__msg__Controllat), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(common_msgs_humble__msg__Controllat));
  bool success = common_msgs_humble__msg__Controllat__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
common_msgs_humble__msg__Controllat__destroy(common_msgs_humble__msg__Controllat * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    common_msgs_humble__msg__Controllat__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
common_msgs_humble__msg__Controllat__Sequence__init(common_msgs_humble__msg__Controllat__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Controllat * data = NULL;

  if (size) {
    data = (common_msgs_humble__msg__Controllat *)allocator.zero_allocate(size, sizeof(common_msgs_humble__msg__Controllat), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = common_msgs_humble__msg__Controllat__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        common_msgs_humble__msg__Controllat__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
common_msgs_humble__msg__Controllat__Sequence__fini(common_msgs_humble__msg__Controllat__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      common_msgs_humble__msg__Controllat__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

common_msgs_humble__msg__Controllat__Sequence *
common_msgs_humble__msg__Controllat__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Controllat__Sequence * array = (common_msgs_humble__msg__Controllat__Sequence *)allocator.allocate(sizeof(common_msgs_humble__msg__Controllat__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = common_msgs_humble__msg__Controllat__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
common_msgs_humble__msg__Controllat__Sequence__destroy(common_msgs_humble__msg__Controllat__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    common_msgs_humble__msg__Controllat__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
common_msgs_humble__msg__Controllat__Sequence__are_equal(const common_msgs_humble__msg__Controllat__Sequence * lhs, const common_msgs_humble__msg__Controllat__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!common_msgs_humble__msg__Controllat__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
common_msgs_humble__msg__Controllat__Sequence__copy(
  const common_msgs_humble__msg__Controllat__Sequence * input,
  common_msgs_humble__msg__Controllat__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(common_msgs_humble__msg__Controllat);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    common_msgs_humble__msg__Controllat * data =
      (common_msgs_humble__msg__Controllat *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!common_msgs_humble__msg__Controllat__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          common_msgs_humble__msg__Controllat__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!common_msgs_humble__msg__Controllat__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
