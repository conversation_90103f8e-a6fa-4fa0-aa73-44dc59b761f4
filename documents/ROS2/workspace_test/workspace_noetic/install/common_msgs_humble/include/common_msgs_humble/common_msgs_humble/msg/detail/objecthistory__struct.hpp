// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Objecthistory.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__OBJECTHISTORY__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__OBJECTHISTORY__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'trajectorypoint'
#include "common_msgs_humble/msg/detail/point3d__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Objecthistory __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Objecthistory __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Objecthistory_
{
  using Type = Objecthistory_<ContainerAllocator>;

  explicit Objecthistory_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : trajectorypoint(_init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->timestamp = 0ll;
      this->lon = 0.0;
      this->lat = 0.0;
      this->alt = 0.0;
      this->roll = 0.0f;
      this->pitch = 0.0f;
      this->heading = 0.0f;
      this->relavx = 0.0f;
      this->relavy = 0.0f;
      this->absvx = 0.0f;
      this->absvy = 0.0f;
      this->s = 0.0f;
      this->l = 0.0f;
      this->speeds = 0.0f;
      this->speedl = 0.0f;
    }
  }

  explicit Objecthistory_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : trajectorypoint(_alloc, _init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->timestamp = 0ll;
      this->lon = 0.0;
      this->lat = 0.0;
      this->alt = 0.0;
      this->roll = 0.0f;
      this->pitch = 0.0f;
      this->heading = 0.0f;
      this->relavx = 0.0f;
      this->relavy = 0.0f;
      this->absvx = 0.0f;
      this->absvy = 0.0f;
      this->s = 0.0f;
      this->l = 0.0f;
      this->speeds = 0.0f;
      this->speedl = 0.0f;
    }
  }

  // field types and members
  using _timestamp_type =
    int64_t;
  _timestamp_type timestamp;
  using _trajectorypoint_type =
    common_msgs_humble::msg::Point3d_<ContainerAllocator>;
  _trajectorypoint_type trajectorypoint;
  using _lon_type =
    double;
  _lon_type lon;
  using _lat_type =
    double;
  _lat_type lat;
  using _alt_type =
    double;
  _alt_type alt;
  using _roll_type =
    float;
  _roll_type roll;
  using _pitch_type =
    float;
  _pitch_type pitch;
  using _heading_type =
    float;
  _heading_type heading;
  using _relavx_type =
    float;
  _relavx_type relavx;
  using _relavy_type =
    float;
  _relavy_type relavy;
  using _absvx_type =
    float;
  _absvx_type absvx;
  using _absvy_type =
    float;
  _absvy_type absvy;
  using _s_type =
    float;
  _s_type s;
  using _l_type =
    float;
  _l_type l;
  using _speeds_type =
    float;
  _speeds_type speeds;
  using _speedl_type =
    float;
  _speedl_type speedl;

  // setters for named parameter idiom
  Type & set__timestamp(
    const int64_t & _arg)
  {
    this->timestamp = _arg;
    return *this;
  }
  Type & set__trajectorypoint(
    const common_msgs_humble::msg::Point3d_<ContainerAllocator> & _arg)
  {
    this->trajectorypoint = _arg;
    return *this;
  }
  Type & set__lon(
    const double & _arg)
  {
    this->lon = _arg;
    return *this;
  }
  Type & set__lat(
    const double & _arg)
  {
    this->lat = _arg;
    return *this;
  }
  Type & set__alt(
    const double & _arg)
  {
    this->alt = _arg;
    return *this;
  }
  Type & set__roll(
    const float & _arg)
  {
    this->roll = _arg;
    return *this;
  }
  Type & set__pitch(
    const float & _arg)
  {
    this->pitch = _arg;
    return *this;
  }
  Type & set__heading(
    const float & _arg)
  {
    this->heading = _arg;
    return *this;
  }
  Type & set__relavx(
    const float & _arg)
  {
    this->relavx = _arg;
    return *this;
  }
  Type & set__relavy(
    const float & _arg)
  {
    this->relavy = _arg;
    return *this;
  }
  Type & set__absvx(
    const float & _arg)
  {
    this->absvx = _arg;
    return *this;
  }
  Type & set__absvy(
    const float & _arg)
  {
    this->absvy = _arg;
    return *this;
  }
  Type & set__s(
    const float & _arg)
  {
    this->s = _arg;
    return *this;
  }
  Type & set__l(
    const float & _arg)
  {
    this->l = _arg;
    return *this;
  }
  Type & set__speeds(
    const float & _arg)
  {
    this->speeds = _arg;
    return *this;
  }
  Type & set__speedl(
    const float & _arg)
  {
    this->speedl = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Objecthistory_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Objecthistory_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Objecthistory_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Objecthistory_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Objecthistory_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Objecthistory_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Objecthistory_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Objecthistory_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Objecthistory_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Objecthistory_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Objecthistory
    std::shared_ptr<common_msgs_humble::msg::Objecthistory_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Objecthistory
    std::shared_ptr<common_msgs_humble::msg::Objecthistory_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Objecthistory_ & other) const
  {
    if (this->timestamp != other.timestamp) {
      return false;
    }
    if (this->trajectorypoint != other.trajectorypoint) {
      return false;
    }
    if (this->lon != other.lon) {
      return false;
    }
    if (this->lat != other.lat) {
      return false;
    }
    if (this->alt != other.alt) {
      return false;
    }
    if (this->roll != other.roll) {
      return false;
    }
    if (this->pitch != other.pitch) {
      return false;
    }
    if (this->heading != other.heading) {
      return false;
    }
    if (this->relavx != other.relavx) {
      return false;
    }
    if (this->relavy != other.relavy) {
      return false;
    }
    if (this->absvx != other.absvx) {
      return false;
    }
    if (this->absvy != other.absvy) {
      return false;
    }
    if (this->s != other.s) {
      return false;
    }
    if (this->l != other.l) {
      return false;
    }
    if (this->speeds != other.speeds) {
      return false;
    }
    if (this->speedl != other.speedl) {
      return false;
    }
    return true;
  }
  bool operator!=(const Objecthistory_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Objecthistory_

// alias to use template instance with default allocator
using Objecthistory =
  common_msgs_humble::msg::Objecthistory_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__OBJECTHISTORY__STRUCT_HPP_
