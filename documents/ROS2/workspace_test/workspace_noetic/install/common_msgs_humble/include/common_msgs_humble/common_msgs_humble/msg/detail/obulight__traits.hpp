// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from common_msgs_humble:msg/Obulight.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__OBULIGHT__TRAITS_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__OBULIGHT__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "common_msgs_humble/msg/detail/obulight__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

namespace common_msgs_humble
{

namespace msg
{

inline void to_flow_style_yaml(
  const Obulight & msg,
  std::ostream & out)
{
  out << "{";
  // member: phase_id
  {
    out << "phase_id: ";
    rosidl_generator_traits::value_to_yaml(msg.phase_id, out);
    out << ", ";
  }

  // member: status
  {
    out << "status: ";
    rosidl_generator_traits::value_to_yaml(msg.status, out);
    out << ", ";
  }

  // member: start_time
  {
    out << "start_time: ";
    rosidl_generator_traits::value_to_yaml(msg.start_time, out);
    out << ", ";
  }

  // member: end_time
  {
    out << "end_time: ";
    rosidl_generator_traits::value_to_yaml(msg.end_time, out);
    out << ", ";
  }

  // member: next_start_time
  {
    out << "next_start_time: ";
    rosidl_generator_traits::value_to_yaml(msg.next_start_time, out);
    out << ", ";
  }

  // member: lane_speed_lower
  {
    out << "lane_speed_lower: ";
    rosidl_generator_traits::value_to_yaml(msg.lane_speed_lower, out);
    out << ", ";
  }

  // member: lane_speed_upper
  {
    out << "lane_speed_upper: ";
    rosidl_generator_traits::value_to_yaml(msg.lane_speed_upper, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const Obulight & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: phase_id
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "phase_id: ";
    rosidl_generator_traits::value_to_yaml(msg.phase_id, out);
    out << "\n";
  }

  // member: status
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "status: ";
    rosidl_generator_traits::value_to_yaml(msg.status, out);
    out << "\n";
  }

  // member: start_time
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "start_time: ";
    rosidl_generator_traits::value_to_yaml(msg.start_time, out);
    out << "\n";
  }

  // member: end_time
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "end_time: ";
    rosidl_generator_traits::value_to_yaml(msg.end_time, out);
    out << "\n";
  }

  // member: next_start_time
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "next_start_time: ";
    rosidl_generator_traits::value_to_yaml(msg.next_start_time, out);
    out << "\n";
  }

  // member: lane_speed_lower
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lane_speed_lower: ";
    rosidl_generator_traits::value_to_yaml(msg.lane_speed_lower, out);
    out << "\n";
  }

  // member: lane_speed_upper
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lane_speed_upper: ";
    rosidl_generator_traits::value_to_yaml(msg.lane_speed_upper, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const Obulight & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace common_msgs_humble

namespace rosidl_generator_traits
{

[[deprecated("use common_msgs_humble::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const common_msgs_humble::msg::Obulight & msg,
  std::ostream & out, size_t indentation = 0)
{
  common_msgs_humble::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use common_msgs_humble::msg::to_yaml() instead")]]
inline std::string to_yaml(const common_msgs_humble::msg::Obulight & msg)
{
  return common_msgs_humble::msg::to_yaml(msg);
}

template<>
inline const char * data_type<common_msgs_humble::msg::Obulight>()
{
  return "common_msgs_humble::msg::Obulight";
}

template<>
inline const char * name<common_msgs_humble::msg::Obulight>()
{
  return "common_msgs_humble/msg/Obulight";
}

template<>
struct has_fixed_size<common_msgs_humble::msg::Obulight>
  : std::integral_constant<bool, true> {};

template<>
struct has_bounded_size<common_msgs_humble::msg::Obulight>
  : std::integral_constant<bool, true> {};

template<>
struct is_message<common_msgs_humble::msg::Obulight>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__OBULIGHT__TRAITS_HPP_
