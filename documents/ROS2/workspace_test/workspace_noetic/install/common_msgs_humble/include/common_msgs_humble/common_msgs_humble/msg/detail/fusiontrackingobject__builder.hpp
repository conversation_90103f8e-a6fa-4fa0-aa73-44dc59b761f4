// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Fusiontrackingobject.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__FUSIONTRACKINGOBJECT__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__FUSIONTRACKINGOBJECT__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/fusiontrackingobject__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Fusiontrackingobject_obupantobject
{
public:
  explicit Init_Fusiontrackingobject_obupantobject(::common_msgs_humble::msg::Fusiontrackingobject & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Fusiontrackingobject obupantobject(::common_msgs_humble::msg::Fusiontrackingobject::_obupantobject_type arg)
  {
    msg_.obupantobject = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Fusiontrackingobject msg_;
};

class Init_Fusiontrackingobject_obuobject
{
public:
  explicit Init_Fusiontrackingobject_obuobject(::common_msgs_humble::msg::Fusiontrackingobject & msg)
  : msg_(msg)
  {}
  Init_Fusiontrackingobject_obupantobject obuobject(::common_msgs_humble::msg::Fusiontrackingobject::_obuobject_type arg)
  {
    msg_.obuobject = std::move(arg);
    return Init_Fusiontrackingobject_obupantobject(msg_);
  }

private:
  ::common_msgs_humble::msg::Fusiontrackingobject msg_;
};

class Init_Fusiontrackingobject_obugpstime
{
public:
  explicit Init_Fusiontrackingobject_obugpstime(::common_msgs_humble::msg::Fusiontrackingobject & msg)
  : msg_(msg)
  {}
  Init_Fusiontrackingobject_obuobject obugpstime(::common_msgs_humble::msg::Fusiontrackingobject::_obugpstime_type arg)
  {
    msg_.obugpstime = std::move(arg);
    return Init_Fusiontrackingobject_obuobject(msg_);
  }

private:
  ::common_msgs_humble::msg::Fusiontrackingobject msg_;
};

class Init_Fusiontrackingobject_obutimestamp
{
public:
  explicit Init_Fusiontrackingobject_obutimestamp(::common_msgs_humble::msg::Fusiontrackingobject & msg)
  : msg_(msg)
  {}
  Init_Fusiontrackingobject_obugpstime obutimestamp(::common_msgs_humble::msg::Fusiontrackingobject::_obutimestamp_type arg)
  {
    msg_.obutimestamp = std::move(arg);
    return Init_Fusiontrackingobject_obugpstime(msg_);
  }

private:
  ::common_msgs_humble::msg::Fusiontrackingobject msg_;
};

class Init_Fusiontrackingobject_radarobject
{
public:
  explicit Init_Fusiontrackingobject_radarobject(::common_msgs_humble::msg::Fusiontrackingobject & msg)
  : msg_(msg)
  {}
  Init_Fusiontrackingobject_obutimestamp radarobject(::common_msgs_humble::msg::Fusiontrackingobject::_radarobject_type arg)
  {
    msg_.radarobject = std::move(arg);
    return Init_Fusiontrackingobject_obutimestamp(msg_);
  }

private:
  ::common_msgs_humble::msg::Fusiontrackingobject msg_;
};

class Init_Fusiontrackingobject_radargpstime
{
public:
  explicit Init_Fusiontrackingobject_radargpstime(::common_msgs_humble::msg::Fusiontrackingobject & msg)
  : msg_(msg)
  {}
  Init_Fusiontrackingobject_radarobject radargpstime(::common_msgs_humble::msg::Fusiontrackingobject::_radargpstime_type arg)
  {
    msg_.radargpstime = std::move(arg);
    return Init_Fusiontrackingobject_radarobject(msg_);
  }

private:
  ::common_msgs_humble::msg::Fusiontrackingobject msg_;
};

class Init_Fusiontrackingobject_radartimestamp
{
public:
  explicit Init_Fusiontrackingobject_radartimestamp(::common_msgs_humble::msg::Fusiontrackingobject & msg)
  : msg_(msg)
  {}
  Init_Fusiontrackingobject_radargpstime radartimestamp(::common_msgs_humble::msg::Fusiontrackingobject::_radartimestamp_type arg)
  {
    msg_.radartimestamp = std::move(arg);
    return Init_Fusiontrackingobject_radargpstime(msg_);
  }

private:
  ::common_msgs_humble::msg::Fusiontrackingobject msg_;
};

class Init_Fusiontrackingobject_lidarobject
{
public:
  explicit Init_Fusiontrackingobject_lidarobject(::common_msgs_humble::msg::Fusiontrackingobject & msg)
  : msg_(msg)
  {}
  Init_Fusiontrackingobject_radartimestamp lidarobject(::common_msgs_humble::msg::Fusiontrackingobject::_lidarobject_type arg)
  {
    msg_.lidarobject = std::move(arg);
    return Init_Fusiontrackingobject_radartimestamp(msg_);
  }

private:
  ::common_msgs_humble::msg::Fusiontrackingobject msg_;
};

class Init_Fusiontrackingobject_lidargpstime
{
public:
  explicit Init_Fusiontrackingobject_lidargpstime(::common_msgs_humble::msg::Fusiontrackingobject & msg)
  : msg_(msg)
  {}
  Init_Fusiontrackingobject_lidarobject lidargpstime(::common_msgs_humble::msg::Fusiontrackingobject::_lidargpstime_type arg)
  {
    msg_.lidargpstime = std::move(arg);
    return Init_Fusiontrackingobject_lidarobject(msg_);
  }

private:
  ::common_msgs_humble::msg::Fusiontrackingobject msg_;
};

class Init_Fusiontrackingobject_lidartimestamp
{
public:
  explicit Init_Fusiontrackingobject_lidartimestamp(::common_msgs_humble::msg::Fusiontrackingobject & msg)
  : msg_(msg)
  {}
  Init_Fusiontrackingobject_lidargpstime lidartimestamp(::common_msgs_humble::msg::Fusiontrackingobject::_lidartimestamp_type arg)
  {
    msg_.lidartimestamp = std::move(arg);
    return Init_Fusiontrackingobject_lidargpstime(msg_);
  }

private:
  ::common_msgs_humble::msg::Fusiontrackingobject msg_;
};

class Init_Fusiontrackingobject_objectsource
{
public:
  Init_Fusiontrackingobject_objectsource()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Fusiontrackingobject_lidartimestamp objectsource(::common_msgs_humble::msg::Fusiontrackingobject::_objectsource_type arg)
  {
    msg_.objectsource = std::move(arg);
    return Init_Fusiontrackingobject_lidartimestamp(msg_);
  }

private:
  ::common_msgs_humble::msg::Fusiontrackingobject msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Fusiontrackingobject>()
{
  return common_msgs_humble::msg::builder::Init_Fusiontrackingobject_objectsource();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__FUSIONTRACKINGOBJECT__BUILDER_HPP_
