// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Lane.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__LANE__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__LANE__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'points'
#include "common_msgs_humble/msg/detail/point3d__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Lane __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Lane __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Lane_
{
  using Type = Lane_<ContainerAllocator>;

  explicit Lane_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->id = 0;
      this->classification = 0;
      this->value = 0;
      this->confidence = 0.0f;
    }
  }

  explicit Lane_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->id = 0;
      this->classification = 0;
      this->value = 0;
      this->confidence = 0.0f;
    }
  }

  // field types and members
  using _id_type =
    uint8_t;
  _id_type id;
  using _classification_type =
    uint8_t;
  _classification_type classification;
  using _value_type =
    uint8_t;
  _value_type value;
  using _confidence_type =
    float;
  _confidence_type confidence;
  using _points_type =
    std::vector<common_msgs_humble::msg::Point3d_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<common_msgs_humble::msg::Point3d_<ContainerAllocator>>>;
  _points_type points;

  // setters for named parameter idiom
  Type & set__id(
    const uint8_t & _arg)
  {
    this->id = _arg;
    return *this;
  }
  Type & set__classification(
    const uint8_t & _arg)
  {
    this->classification = _arg;
    return *this;
  }
  Type & set__value(
    const uint8_t & _arg)
  {
    this->value = _arg;
    return *this;
  }
  Type & set__confidence(
    const float & _arg)
  {
    this->confidence = _arg;
    return *this;
  }
  Type & set__points(
    const std::vector<common_msgs_humble::msg::Point3d_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<common_msgs_humble::msg::Point3d_<ContainerAllocator>>> & _arg)
  {
    this->points = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Lane_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Lane_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Lane_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Lane_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Lane_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Lane_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Lane_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Lane_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Lane_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Lane_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Lane
    std::shared_ptr<common_msgs_humble::msg::Lane_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Lane
    std::shared_ptr<common_msgs_humble::msg::Lane_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Lane_ & other) const
  {
    if (this->id != other.id) {
      return false;
    }
    if (this->classification != other.classification) {
      return false;
    }
    if (this->value != other.value) {
      return false;
    }
    if (this->confidence != other.confidence) {
      return false;
    }
    if (this->points != other.points) {
      return false;
    }
    return true;
  }
  bool operator!=(const Lane_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Lane_

// alias to use template instance with default allocator
using Lane =
  common_msgs_humble::msg::Lane_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__LANE__STRUCT_HPP_
