// generated from rosidl_generator_c/resource/idl__type_support.h.em
// with input from common_msgs_humble:msg/Hdintersectiontoglobal.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__HDINTERSECTIONTOGLOBAL__TYPE_SUPPORT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__HDINTERSECTIONTOGLOBAL__TYPE_SUPPORT_H_

#include "rosidl_typesupport_interface/macros.h"

#include "common_msgs_humble/msg/rosidl_generator_c__visibility_control.h"

#ifdef __cplusplus
extern "C"
{
#endif

#include "rosidl_runtime_c/message_type_support_struct.h"

// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_C_PUBLIC_common_msgs_humble
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_c,
  common_msgs_humble,
  msg,
  Hdintersectiontoglobal
)();

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__HDINTERSECTIONTOGLOBAL__TYPE_SUPPORT_H_
