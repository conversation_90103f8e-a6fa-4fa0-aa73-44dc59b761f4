// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Obutrafficlights.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUTRAFFICLIGHTS__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUTRAFFICLIGHTS__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/obutrafficlights__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Obutrafficlights_light
{
public:
  explicit Init_Obutrafficlights_light(::common_msgs_humble::msg::Obutrafficlights & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Obutrafficlights light(::common_msgs_humble::msg::Obutrafficlights::_light_type arg)
  {
    msg_.light = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Obutrafficlights msg_;
};

class Init_Obutrafficlights_phase_cnt
{
public:
  explicit Init_Obutrafficlights_phase_cnt(::common_msgs_humble::msg::Obutrafficlights & msg)
  : msg_(msg)
  {}
  Init_Obutrafficlights_light phase_cnt(::common_msgs_humble::msg::Obutrafficlights::_phase_cnt_type arg)
  {
    msg_.phase_cnt = std::move(arg);
    return Init_Obutrafficlights_light(msg_);
  }

private:
  ::common_msgs_humble::msg::Obutrafficlights msg_;
};

class Init_Obutrafficlights_light_status
{
public:
  explicit Init_Obutrafficlights_light_status(::common_msgs_humble::msg::Obutrafficlights & msg)
  : msg_(msg)
  {}
  Init_Obutrafficlights_phase_cnt light_status(::common_msgs_humble::msg::Obutrafficlights::_light_status_type arg)
  {
    msg_.light_status = std::move(arg);
    return Init_Obutrafficlights_phase_cnt(msg_);
  }

private:
  ::common_msgs_humble::msg::Obutrafficlights msg_;
};

class Init_Obutrafficlights_node_id
{
public:
  explicit Init_Obutrafficlights_node_id(::common_msgs_humble::msg::Obutrafficlights & msg)
  : msg_(msg)
  {}
  Init_Obutrafficlights_light_status node_id(::common_msgs_humble::msg::Obutrafficlights::_node_id_type arg)
  {
    msg_.node_id = std::move(arg);
    return Init_Obutrafficlights_light_status(msg_);
  }

private:
  ::common_msgs_humble::msg::Obutrafficlights msg_;
};

class Init_Obutrafficlights_region_id
{
public:
  explicit Init_Obutrafficlights_region_id(::common_msgs_humble::msg::Obutrafficlights & msg)
  : msg_(msg)
  {}
  Init_Obutrafficlights_node_id region_id(::common_msgs_humble::msg::Obutrafficlights::_region_id_type arg)
  {
    msg_.region_id = std::move(arg);
    return Init_Obutrafficlights_node_id(msg_);
  }

private:
  ::common_msgs_humble::msg::Obutrafficlights msg_;
};

class Init_Obutrafficlights_timestamp
{
public:
  Init_Obutrafficlights_timestamp()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Obutrafficlights_region_id timestamp(::common_msgs_humble::msg::Obutrafficlights::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return Init_Obutrafficlights_region_id(msg_);
  }

private:
  ::common_msgs_humble::msg::Obutrafficlights msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Obutrafficlights>()
{
  return common_msgs_humble::msg::builder::Init_Obutrafficlights_timestamp();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUTRAFFICLIGHTS__BUILDER_HPP_
