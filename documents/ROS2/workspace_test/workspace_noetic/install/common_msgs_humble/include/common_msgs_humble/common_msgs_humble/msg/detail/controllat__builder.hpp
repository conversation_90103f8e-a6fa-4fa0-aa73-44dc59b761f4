// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Controllat.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__CONTROLLAT__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__CONTROLLAT__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/controllat__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Controllat_curve
{
public:
  explicit Init_Controllat_curve(::common_msgs_humble::msg::Controllat & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Controllat curve(::common_msgs_humble::msg::Controllat::_curve_type arg)
  {
    msg_.curve = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Controllat msg_;
};

class Init_Controllat_apsstate
{
public:
  explicit Init_Controllat_apsstate(::common_msgs_humble::msg::Controllat & msg)
  : msg_(msg)
  {}
  Init_Controllat_curve apsstate(::common_msgs_humble::msg::Controllat::_apsstate_type arg)
  {
    msg_.apsstate = std::move(arg);
    return Init_Controllat_curve(msg_);
  }

private:
  ::common_msgs_humble::msg::Controllat msg_;
};

class Init_Controllat_apavstatus
{
public:
  explicit Init_Controllat_apavstatus(::common_msgs_humble::msg::Controllat & msg)
  : msg_(msg)
  {}
  Init_Controllat_apsstate apavstatus(::common_msgs_humble::msg::Controllat::_apavstatus_type arg)
  {
    msg_.apavstatus = std::move(arg);
    return Init_Controllat_apsstate(msg_);
  }

private:
  ::common_msgs_humble::msg::Controllat msg_;
};

class Init_Controllat_timestamp
{
public:
  explicit Init_Controllat_timestamp(::common_msgs_humble::msg::Controllat & msg)
  : msg_(msg)
  {}
  Init_Controllat_apavstatus timestamp(::common_msgs_humble::msg::Controllat::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return Init_Controllat_apavstatus(msg_);
  }

private:
  ::common_msgs_humble::msg::Controllat msg_;
};

class Init_Controllat_deviation
{
public:
  explicit Init_Controllat_deviation(::common_msgs_humble::msg::Controllat & msg)
  : msg_(msg)
  {}
  Init_Controllat_timestamp deviation(::common_msgs_humble::msg::Controllat::_deviation_type arg)
  {
    msg_.deviation = std::move(arg);
    return Init_Controllat_timestamp(msg_);
  }

private:
  ::common_msgs_humble::msg::Controllat msg_;
};

class Init_Controllat_isvalid
{
public:
  explicit Init_Controllat_isvalid(::common_msgs_humble::msg::Controllat & msg)
  : msg_(msg)
  {}
  Init_Controllat_deviation isvalid(::common_msgs_humble::msg::Controllat::_isvalid_type arg)
  {
    msg_.isvalid = std::move(arg);
    return Init_Controllat_deviation(msg_);
  }

private:
  ::common_msgs_humble::msg::Controllat msg_;
};

class Init_Controllat_lights
{
public:
  explicit Init_Controllat_lights(::common_msgs_humble::msg::Controllat & msg)
  : msg_(msg)
  {}
  Init_Controllat_isvalid lights(::common_msgs_humble::msg::Controllat::_lights_type arg)
  {
    msg_.lights = std::move(arg);
    return Init_Controllat_isvalid(msg_);
  }

private:
  ::common_msgs_humble::msg::Controllat msg_;
};

class Init_Controllat_lightmethod
{
public:
  explicit Init_Controllat_lightmethod(::common_msgs_humble::msg::Controllat & msg)
  : msg_(msg)
  {}
  Init_Controllat_lights lightmethod(::common_msgs_humble::msg::Controllat::_lightmethod_type arg)
  {
    msg_.lightmethod = std::move(arg);
    return Init_Controllat_lights(msg_);
  }

private:
  ::common_msgs_humble::msg::Controllat msg_;
};

class Init_Controllat_epstorque
{
public:
  explicit Init_Controllat_epstorque(::common_msgs_humble::msg::Controllat & msg)
  : msg_(msg)
  {}
  Init_Controllat_lightmethod epstorque(::common_msgs_humble::msg::Controllat::_epstorque_type arg)
  {
    msg_.epstorque = std::move(arg);
    return Init_Controllat_lightmethod(msg_);
  }

private:
  ::common_msgs_humble::msg::Controllat msg_;
};

class Init_Controllat_limitspeed
{
public:
  explicit Init_Controllat_limitspeed(::common_msgs_humble::msg::Controllat & msg)
  : msg_(msg)
  {}
  Init_Controllat_epstorque limitspeed(::common_msgs_humble::msg::Controllat::_limitspeed_type arg)
  {
    msg_.limitspeed = std::move(arg);
    return Init_Controllat_epstorque(msg_);
  }

private:
  ::common_msgs_humble::msg::Controllat msg_;
};

class Init_Controllat_epsangle
{
public:
  explicit Init_Controllat_epsangle(::common_msgs_humble::msg::Controllat & msg)
  : msg_(msg)
  {}
  Init_Controllat_limitspeed epsangle(::common_msgs_humble::msg::Controllat::_epsangle_type arg)
  {
    msg_.epsangle = std::move(arg);
    return Init_Controllat_limitspeed(msg_);
  }

private:
  ::common_msgs_humble::msg::Controllat msg_;
};

class Init_Controllat_epsmethod
{
public:
  Init_Controllat_epsmethod()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Controllat_epsangle epsmethod(::common_msgs_humble::msg::Controllat::_epsmethod_type arg)
  {
    msg_.epsmethod = std::move(arg);
    return Init_Controllat_epsangle(msg_);
  }

private:
  ::common_msgs_humble::msg::Controllat msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Controllat>()
{
  return common_msgs_humble::msg::builder::Init_Controllat_epsmethod();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__CONTROLLAT__BUILDER_HPP_
