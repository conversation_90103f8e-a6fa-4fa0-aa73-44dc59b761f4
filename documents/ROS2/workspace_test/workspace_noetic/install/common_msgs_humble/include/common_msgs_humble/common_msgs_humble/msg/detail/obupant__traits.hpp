// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from common_msgs_humble:msg/Obupant.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUPANT__TRAITS_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUPANT__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "common_msgs_humble/msg/detail/obupant__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

// Include directives for member types
// Member 'roadlist'
#include "common_msgs_humble/msg/detail/oburoadlist__traits.hpp"

namespace common_msgs_humble
{

namespace msg
{

inline void to_flow_style_yaml(
  const Obupant & msg,
  std::ostream & out)
{
  out << "{";
  // member: ptc_type
  {
    out << "ptc_type: ";
    rosidl_generator_traits::value_to_yaml(msg.ptc_type, out);
    out << ", ";
  }

  // member: ptc_id
  {
    out << "ptc_id: ";
    rosidl_generator_traits::value_to_yaml(msg.ptc_id, out);
    out << ", ";
  }

  // member: source
  {
    out << "source: ";
    rosidl_generator_traits::value_to_yaml(msg.source, out);
    out << ", ";
  }

  // member: source_id
  {
    out << "source_id: ";
    rosidl_generator_traits::value_to_yaml(msg.source_id, out);
    out << ", ";
  }

  // member: sec_mark
  {
    out << "sec_mark: ";
    rosidl_generator_traits::value_to_yaml(msg.sec_mark, out);
    out << ", ";
  }

  // member: pos_lon
  {
    out << "pos_lon: ";
    rosidl_generator_traits::value_to_yaml(msg.pos_lon, out);
    out << ", ";
  }

  // member: pos_lat
  {
    out << "pos_lat: ";
    rosidl_generator_traits::value_to_yaml(msg.pos_lat, out);
    out << ", ";
  }

  // member: pos_latitude
  {
    out << "pos_latitude: ";
    rosidl_generator_traits::value_to_yaml(msg.pos_latitude, out);
    out << ", ";
  }

  // member: speed
  {
    out << "speed: ";
    rosidl_generator_traits::value_to_yaml(msg.speed, out);
    out << ", ";
  }

  // member: heading
  {
    out << "heading: ";
    rosidl_generator_traits::value_to_yaml(msg.heading, out);
    out << ", ";
  }

  // member: accel
  {
    out << "accel: ";
    rosidl_generator_traits::value_to_yaml(msg.accel, out);
    out << ", ";
  }

  // member: accel_angle
  {
    out << "accel_angle: ";
    rosidl_generator_traits::value_to_yaml(msg.accel_angle, out);
    out << ", ";
  }

  // member: acc4way_lon
  {
    out << "acc4way_lon: ";
    rosidl_generator_traits::value_to_yaml(msg.acc4way_lon, out);
    out << ", ";
  }

  // member: acc4way_lat
  {
    out << "acc4way_lat: ";
    rosidl_generator_traits::value_to_yaml(msg.acc4way_lat, out);
    out << ", ";
  }

  // member: acc4way_vert
  {
    out << "acc4way_vert: ";
    rosidl_generator_traits::value_to_yaml(msg.acc4way_vert, out);
    out << ", ";
  }

  // member: acc4way_yaw
  {
    out << "acc4way_yaw: ";
    rosidl_generator_traits::value_to_yaml(msg.acc4way_yaw, out);
    out << ", ";
  }

  // member: width
  {
    out << "width: ";
    rosidl_generator_traits::value_to_yaml(msg.width, out);
    out << ", ";
  }

  // member: length
  {
    out << "length: ";
    rosidl_generator_traits::value_to_yaml(msg.length, out);
    out << ", ";
  }

  // member: height
  {
    out << "height: ";
    rosidl_generator_traits::value_to_yaml(msg.height, out);
    out << ", ";
  }

  // member: lon
  {
    out << "lon: ";
    rosidl_generator_traits::value_to_yaml(msg.lon, out);
    out << ", ";
  }

  // member: lat
  {
    out << "lat: ";
    rosidl_generator_traits::value_to_yaml(msg.lat, out);
    out << ", ";
  }

  // member: planlist_num
  {
    out << "planlist_num: ";
    rosidl_generator_traits::value_to_yaml(msg.planlist_num, out);
    out << ", ";
  }

  // member: roadlist
  {
    if (msg.roadlist.size() == 0) {
      out << "roadlist: []";
    } else {
      out << "roadlist: [";
      size_t pending_items = msg.roadlist.size();
      for (auto item : msg.roadlist) {
        to_flow_style_yaml(item, out);
        if (--pending_items > 0) {
          out << ", ";
        }
      }
      out << "]";
    }
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const Obupant & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: ptc_type
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "ptc_type: ";
    rosidl_generator_traits::value_to_yaml(msg.ptc_type, out);
    out << "\n";
  }

  // member: ptc_id
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "ptc_id: ";
    rosidl_generator_traits::value_to_yaml(msg.ptc_id, out);
    out << "\n";
  }

  // member: source
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "source: ";
    rosidl_generator_traits::value_to_yaml(msg.source, out);
    out << "\n";
  }

  // member: source_id
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "source_id: ";
    rosidl_generator_traits::value_to_yaml(msg.source_id, out);
    out << "\n";
  }

  // member: sec_mark
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "sec_mark: ";
    rosidl_generator_traits::value_to_yaml(msg.sec_mark, out);
    out << "\n";
  }

  // member: pos_lon
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "pos_lon: ";
    rosidl_generator_traits::value_to_yaml(msg.pos_lon, out);
    out << "\n";
  }

  // member: pos_lat
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "pos_lat: ";
    rosidl_generator_traits::value_to_yaml(msg.pos_lat, out);
    out << "\n";
  }

  // member: pos_latitude
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "pos_latitude: ";
    rosidl_generator_traits::value_to_yaml(msg.pos_latitude, out);
    out << "\n";
  }

  // member: speed
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "speed: ";
    rosidl_generator_traits::value_to_yaml(msg.speed, out);
    out << "\n";
  }

  // member: heading
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "heading: ";
    rosidl_generator_traits::value_to_yaml(msg.heading, out);
    out << "\n";
  }

  // member: accel
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "accel: ";
    rosidl_generator_traits::value_to_yaml(msg.accel, out);
    out << "\n";
  }

  // member: accel_angle
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "accel_angle: ";
    rosidl_generator_traits::value_to_yaml(msg.accel_angle, out);
    out << "\n";
  }

  // member: acc4way_lon
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "acc4way_lon: ";
    rosidl_generator_traits::value_to_yaml(msg.acc4way_lon, out);
    out << "\n";
  }

  // member: acc4way_lat
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "acc4way_lat: ";
    rosidl_generator_traits::value_to_yaml(msg.acc4way_lat, out);
    out << "\n";
  }

  // member: acc4way_vert
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "acc4way_vert: ";
    rosidl_generator_traits::value_to_yaml(msg.acc4way_vert, out);
    out << "\n";
  }

  // member: acc4way_yaw
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "acc4way_yaw: ";
    rosidl_generator_traits::value_to_yaml(msg.acc4way_yaw, out);
    out << "\n";
  }

  // member: width
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "width: ";
    rosidl_generator_traits::value_to_yaml(msg.width, out);
    out << "\n";
  }

  // member: length
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "length: ";
    rosidl_generator_traits::value_to_yaml(msg.length, out);
    out << "\n";
  }

  // member: height
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "height: ";
    rosidl_generator_traits::value_to_yaml(msg.height, out);
    out << "\n";
  }

  // member: lon
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lon: ";
    rosidl_generator_traits::value_to_yaml(msg.lon, out);
    out << "\n";
  }

  // member: lat
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lat: ";
    rosidl_generator_traits::value_to_yaml(msg.lat, out);
    out << "\n";
  }

  // member: planlist_num
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "planlist_num: ";
    rosidl_generator_traits::value_to_yaml(msg.planlist_num, out);
    out << "\n";
  }

  // member: roadlist
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    if (msg.roadlist.size() == 0) {
      out << "roadlist: []\n";
    } else {
      out << "roadlist:\n";
      for (auto item : msg.roadlist) {
        if (indentation > 0) {
          out << std::string(indentation, ' ');
        }
        out << "-\n";
        to_block_style_yaml(item, out, indentation + 2);
      }
    }
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const Obupant & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace common_msgs_humble

namespace rosidl_generator_traits
{

[[deprecated("use common_msgs_humble::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const common_msgs_humble::msg::Obupant & msg,
  std::ostream & out, size_t indentation = 0)
{
  common_msgs_humble::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use common_msgs_humble::msg::to_yaml() instead")]]
inline std::string to_yaml(const common_msgs_humble::msg::Obupant & msg)
{
  return common_msgs_humble::msg::to_yaml(msg);
}

template<>
inline const char * data_type<common_msgs_humble::msg::Obupant>()
{
  return "common_msgs_humble::msg::Obupant";
}

template<>
inline const char * name<common_msgs_humble::msg::Obupant>()
{
  return "common_msgs_humble/msg/Obupant";
}

template<>
struct has_fixed_size<common_msgs_humble::msg::Obupant>
  : std::integral_constant<bool, false> {};

template<>
struct has_bounded_size<common_msgs_humble::msg::Obupant>
  : std::integral_constant<bool, false> {};

template<>
struct is_message<common_msgs_humble::msg::Obupant>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUPANT__TRAITS_HPP_
