// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Monitor.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__MONITOR__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__MONITOR__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/monitor__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Monitor_sensorstate
{
public:
  explicit Init_Monitor_sensorstate(::common_msgs_humble::msg::Monitor & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Monitor sensorstate(::common_msgs_humble::msg::Monitor::_sensorstate_type arg)
  {
    msg_.sensorstate = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Monitor msg_;
};

class Init_Monitor_status
{
public:
  explicit Init_Monitor_status(::common_msgs_humble::msg::Monitor & msg)
  : msg_(msg)
  {}
  Init_Monitor_sensorstate status(::common_msgs_humble::msg::Monitor::_status_type arg)
  {
    msg_.status = std::move(arg);
    return Init_Monitor_sensorstate(msg_);
  }

private:
  ::common_msgs_humble::msg::Monitor msg_;
};

class Init_Monitor_timestamp
{
public:
  explicit Init_Monitor_timestamp(::common_msgs_humble::msg::Monitor & msg)
  : msg_(msg)
  {}
  Init_Monitor_status timestamp(::common_msgs_humble::msg::Monitor::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return Init_Monitor_status(msg_);
  }

private:
  ::common_msgs_humble::msg::Monitor msg_;
};

class Init_Monitor_dotcnt
{
public:
  explicit Init_Monitor_dotcnt(::common_msgs_humble::msg::Monitor & msg)
  : msg_(msg)
  {}
  Init_Monitor_timestamp dotcnt(::common_msgs_humble::msg::Monitor::_dotcnt_type arg)
  {
    msg_.dotcnt = std::move(arg);
    return Init_Monitor_timestamp(msg_);
  }

private:
  ::common_msgs_humble::msg::Monitor msg_;
};

class Init_Monitor_destext
{
public:
  explicit Init_Monitor_destext(::common_msgs_humble::msg::Monitor & msg)
  : msg_(msg)
  {}
  Init_Monitor_dotcnt destext(::common_msgs_humble::msg::Monitor::_destext_type arg)
  {
    msg_.destext = std::move(arg);
    return Init_Monitor_dotcnt(msg_);
  }

private:
  ::common_msgs_humble::msg::Monitor msg_;
};

class Init_Monitor_valuetext
{
public:
  explicit Init_Monitor_valuetext(::common_msgs_humble::msg::Monitor & msg)
  : msg_(msg)
  {}
  Init_Monitor_destext valuetext(::common_msgs_humble::msg::Monitor::_valuetext_type arg)
  {
    msg_.valuetext = std::move(arg);
    return Init_Monitor_destext(msg_);
  }

private:
  ::common_msgs_humble::msg::Monitor msg_;
};

class Init_Monitor_deslight
{
public:
  explicit Init_Monitor_deslight(::common_msgs_humble::msg::Monitor & msg)
  : msg_(msg)
  {}
  Init_Monitor_valuetext deslight(::common_msgs_humble::msg::Monitor::_deslight_type arg)
  {
    msg_.deslight = std::move(arg);
    return Init_Monitor_valuetext(msg_);
  }

private:
  ::common_msgs_humble::msg::Monitor msg_;
};

class Init_Monitor_valuelight
{
public:
  Init_Monitor_valuelight()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Monitor_deslight valuelight(::common_msgs_humble::msg::Monitor::_valuelight_type arg)
  {
    msg_.valuelight = std::move(arg);
    return Init_Monitor_deslight(msg_);
  }

private:
  ::common_msgs_humble::msg::Monitor msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Monitor>()
{
  return common_msgs_humble::msg::builder::Init_Monitor_valuelight();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__MONITOR__BUILDER_HPP_
