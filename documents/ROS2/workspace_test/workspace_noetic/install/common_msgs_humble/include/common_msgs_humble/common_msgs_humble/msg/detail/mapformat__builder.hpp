// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Mapformat.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__MAPFORMAT__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__MAPFORMAT__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/mapformat__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Mapformat_backup9
{
public:
  explicit Init_Mapformat_backup9(::common_msgs_humble::msg::Mapformat & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Mapformat backup9(::common_msgs_humble::msg::Mapformat::_backup9_type arg)
  {
    msg_.backup9 = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Mapformat msg_;
};

class Init_Mapformat_backup8
{
public:
  explicit Init_Mapformat_backup8(::common_msgs_humble::msg::Mapformat & msg)
  : msg_(msg)
  {}
  Init_Mapformat_backup9 backup8(::common_msgs_humble::msg::Mapformat::_backup8_type arg)
  {
    msg_.backup8 = std::move(arg);
    return Init_Mapformat_backup9(msg_);
  }

private:
  ::common_msgs_humble::msg::Mapformat msg_;
};

class Init_Mapformat_backup7
{
public:
  explicit Init_Mapformat_backup7(::common_msgs_humble::msg::Mapformat & msg)
  : msg_(msg)
  {}
  Init_Mapformat_backup8 backup7(::common_msgs_humble::msg::Mapformat::_backup7_type arg)
  {
    msg_.backup7 = std::move(arg);
    return Init_Mapformat_backup8(msg_);
  }

private:
  ::common_msgs_humble::msg::Mapformat msg_;
};

class Init_Mapformat_backup6
{
public:
  explicit Init_Mapformat_backup6(::common_msgs_humble::msg::Mapformat & msg)
  : msg_(msg)
  {}
  Init_Mapformat_backup7 backup6(::common_msgs_humble::msg::Mapformat::_backup6_type arg)
  {
    msg_.backup6 = std::move(arg);
    return Init_Mapformat_backup7(msg_);
  }

private:
  ::common_msgs_humble::msg::Mapformat msg_;
};

class Init_Mapformat_backup5
{
public:
  explicit Init_Mapformat_backup5(::common_msgs_humble::msg::Mapformat & msg)
  : msg_(msg)
  {}
  Init_Mapformat_backup6 backup5(::common_msgs_humble::msg::Mapformat::_backup5_type arg)
  {
    msg_.backup5 = std::move(arg);
    return Init_Mapformat_backup6(msg_);
  }

private:
  ::common_msgs_humble::msg::Mapformat msg_;
};

class Init_Mapformat_backup4
{
public:
  explicit Init_Mapformat_backup4(::common_msgs_humble::msg::Mapformat & msg)
  : msg_(msg)
  {}
  Init_Mapformat_backup5 backup4(::common_msgs_humble::msg::Mapformat::_backup4_type arg)
  {
    msg_.backup4 = std::move(arg);
    return Init_Mapformat_backup5(msg_);
  }

private:
  ::common_msgs_humble::msg::Mapformat msg_;
};

class Init_Mapformat_backup3
{
public:
  explicit Init_Mapformat_backup3(::common_msgs_humble::msg::Mapformat & msg)
  : msg_(msg)
  {}
  Init_Mapformat_backup4 backup3(::common_msgs_humble::msg::Mapformat::_backup3_type arg)
  {
    msg_.backup3 = std::move(arg);
    return Init_Mapformat_backup4(msg_);
  }

private:
  ::common_msgs_humble::msg::Mapformat msg_;
};

class Init_Mapformat_backup2
{
public:
  explicit Init_Mapformat_backup2(::common_msgs_humble::msg::Mapformat & msg)
  : msg_(msg)
  {}
  Init_Mapformat_backup3 backup2(::common_msgs_humble::msg::Mapformat::_backup2_type arg)
  {
    msg_.backup2 = std::move(arg);
    return Init_Mapformat_backup3(msg_);
  }

private:
  ::common_msgs_humble::msg::Mapformat msg_;
};

class Init_Mapformat_backup1
{
public:
  explicit Init_Mapformat_backup1(::common_msgs_humble::msg::Mapformat & msg)
  : msg_(msg)
  {}
  Init_Mapformat_backup2 backup1(::common_msgs_humble::msg::Mapformat::_backup1_type arg)
  {
    msg_.backup1 = std::move(arg);
    return Init_Mapformat_backup2(msg_);
  }

private:
  ::common_msgs_humble::msg::Mapformat msg_;
};

class Init_Mapformat_lanenum
{
public:
  explicit Init_Mapformat_lanenum(::common_msgs_humble::msg::Mapformat & msg)
  : msg_(msg)
  {}
  Init_Mapformat_backup1 lanenum(::common_msgs_humble::msg::Mapformat::_lanenum_type arg)
  {
    msg_.lanenum = std::move(arg);
    return Init_Mapformat_backup1(msg_);
  }

private:
  ::common_msgs_humble::msg::Mapformat msg_;
};

class Init_Mapformat_lanesum
{
public:
  explicit Init_Mapformat_lanesum(::common_msgs_humble::msg::Mapformat & msg)
  : msg_(msg)
  {}
  Init_Mapformat_lanenum lanesum(::common_msgs_humble::msg::Mapformat::_lanesum_type arg)
  {
    msg_.lanesum = std::move(arg);
    return Init_Mapformat_lanenum(msg_);
  }

private:
  ::common_msgs_humble::msg::Mapformat msg_;
};

class Init_Mapformat_borrowflag
{
public:
  explicit Init_Mapformat_borrowflag(::common_msgs_humble::msg::Mapformat & msg)
  : msg_(msg)
  {}
  Init_Mapformat_lanesum borrowflag(::common_msgs_humble::msg::Mapformat::_borrowflag_type arg)
  {
    msg_.borrowflag = std::move(arg);
    return Init_Mapformat_lanesum(msg_);
  }

private:
  ::common_msgs_humble::msg::Mapformat msg_;
};

class Init_Mapformat_switchflag
{
public:
  explicit Init_Mapformat_switchflag(::common_msgs_humble::msg::Mapformat & msg)
  : msg_(msg)
  {}
  Init_Mapformat_borrowflag switchflag(::common_msgs_humble::msg::Mapformat::_switchflag_type arg)
  {
    msg_.switchflag = std::move(arg);
    return Init_Mapformat_borrowflag(msg_);
  }

private:
  ::common_msgs_humble::msg::Mapformat msg_;
};

class Init_Mapformat_gpstime
{
public:
  explicit Init_Mapformat_gpstime(::common_msgs_humble::msg::Mapformat & msg)
  : msg_(msg)
  {}
  Init_Mapformat_switchflag gpstime(::common_msgs_humble::msg::Mapformat::_gpstime_type arg)
  {
    msg_.gpstime = std::move(arg);
    return Init_Mapformat_switchflag(msg_);
  }

private:
  ::common_msgs_humble::msg::Mapformat msg_;
};

class Init_Mapformat_curvature
{
public:
  explicit Init_Mapformat_curvature(::common_msgs_humble::msg::Mapformat & msg)
  : msg_(msg)
  {}
  Init_Mapformat_gpstime curvature(::common_msgs_humble::msg::Mapformat::_curvature_type arg)
  {
    msg_.curvature = std::move(arg);
    return Init_Mapformat_gpstime(msg_);
  }

private:
  ::common_msgs_humble::msg::Mapformat msg_;
};

class Init_Mapformat_heading
{
public:
  explicit Init_Mapformat_heading(::common_msgs_humble::msg::Mapformat & msg)
  : msg_(msg)
  {}
  Init_Mapformat_curvature heading(::common_msgs_humble::msg::Mapformat::_heading_type arg)
  {
    msg_.heading = std::move(arg);
    return Init_Mapformat_curvature(msg_);
  }

private:
  ::common_msgs_humble::msg::Mapformat msg_;
};

class Init_Mapformat_rightsearchdis
{
public:
  explicit Init_Mapformat_rightsearchdis(::common_msgs_humble::msg::Mapformat & msg)
  : msg_(msg)
  {}
  Init_Mapformat_heading rightsearchdis(::common_msgs_humble::msg::Mapformat::_rightsearchdis_type arg)
  {
    msg_.rightsearchdis = std::move(arg);
    return Init_Mapformat_heading(msg_);
  }

private:
  ::common_msgs_humble::msg::Mapformat msg_;
};

class Init_Mapformat_leftsearchdis
{
public:
  explicit Init_Mapformat_leftsearchdis(::common_msgs_humble::msg::Mapformat & msg)
  : msg_(msg)
  {}
  Init_Mapformat_rightsearchdis leftsearchdis(::common_msgs_humble::msg::Mapformat::_leftsearchdis_type arg)
  {
    msg_.leftsearchdis = std::move(arg);
    return Init_Mapformat_rightsearchdis(msg_);
  }

private:
  ::common_msgs_humble::msg::Mapformat msg_;
};

class Init_Mapformat_rightlanewidth
{
public:
  explicit Init_Mapformat_rightlanewidth(::common_msgs_humble::msg::Mapformat & msg)
  : msg_(msg)
  {}
  Init_Mapformat_leftsearchdis rightlanewidth(::common_msgs_humble::msg::Mapformat::_rightlanewidth_type arg)
  {
    msg_.rightlanewidth = std::move(arg);
    return Init_Mapformat_leftsearchdis(msg_);
  }

private:
  ::common_msgs_humble::msg::Mapformat msg_;
};

class Init_Mapformat_leftlanewidth
{
public:
  explicit Init_Mapformat_leftlanewidth(::common_msgs_humble::msg::Mapformat & msg)
  : msg_(msg)
  {}
  Init_Mapformat_rightlanewidth leftlanewidth(::common_msgs_humble::msg::Mapformat::_leftlanewidth_type arg)
  {
    msg_.leftlanewidth = std::move(arg);
    return Init_Mapformat_rightlanewidth(msg_);
  }

private:
  ::common_msgs_humble::msg::Mapformat msg_;
};

class Init_Mapformat_lanewidth
{
public:
  explicit Init_Mapformat_lanewidth(::common_msgs_humble::msg::Mapformat & msg)
  : msg_(msg)
  {}
  Init_Mapformat_leftlanewidth lanewidth(::common_msgs_humble::msg::Mapformat::_lanewidth_type arg)
  {
    msg_.lanewidth = std::move(arg);
    return Init_Mapformat_leftlanewidth(msg_);
  }

private:
  ::common_msgs_humble::msg::Mapformat msg_;
};

class Init_Mapformat_sideroadwidth
{
public:
  explicit Init_Mapformat_sideroadwidth(::common_msgs_humble::msg::Mapformat & msg)
  : msg_(msg)
  {}
  Init_Mapformat_lanewidth sideroadwidth(::common_msgs_humble::msg::Mapformat::_sideroadwidth_type arg)
  {
    msg_.sideroadwidth = std::move(arg);
    return Init_Mapformat_lanewidth(msg_);
  }

private:
  ::common_msgs_humble::msg::Mapformat msg_;
};

class Init_Mapformat_turnlight
{
public:
  explicit Init_Mapformat_turnlight(::common_msgs_humble::msg::Mapformat & msg)
  : msg_(msg)
  {}
  Init_Mapformat_sideroadwidth turnlight(::common_msgs_humble::msg::Mapformat::_turnlight_type arg)
  {
    msg_.turnlight = std::move(arg);
    return Init_Mapformat_sideroadwidth(msg_);
  }

private:
  ::common_msgs_humble::msg::Mapformat msg_;
};

class Init_Mapformat_sensorlanetype
{
public:
  explicit Init_Mapformat_sensorlanetype(::common_msgs_humble::msg::Mapformat & msg)
  : msg_(msg)
  {}
  Init_Mapformat_turnlight sensorlanetype(::common_msgs_humble::msg::Mapformat::_sensorlanetype_type arg)
  {
    msg_.sensorlanetype = std::move(arg);
    return Init_Mapformat_turnlight(msg_);
  }

private:
  ::common_msgs_humble::msg::Mapformat msg_;
};

class Init_Mapformat_mergelanetype
{
public:
  explicit Init_Mapformat_mergelanetype(::common_msgs_humble::msg::Mapformat & msg)
  : msg_(msg)
  {}
  Init_Mapformat_sensorlanetype mergelanetype(::common_msgs_humble::msg::Mapformat::_mergelanetype_type arg)
  {
    msg_.mergelanetype = std::move(arg);
    return Init_Mapformat_sensorlanetype(msg_);
  }

private:
  ::common_msgs_humble::msg::Mapformat msg_;
};

class Init_Mapformat_lanetype
{
public:
  explicit Init_Mapformat_lanetype(::common_msgs_humble::msg::Mapformat & msg)
  : msg_(msg)
  {}
  Init_Mapformat_mergelanetype lanetype(::common_msgs_humble::msg::Mapformat::_lanetype_type arg)
  {
    msg_.lanetype = std::move(arg);
    return Init_Mapformat_mergelanetype(msg_);
  }

private:
  ::common_msgs_humble::msg::Mapformat msg_;
};

class Init_Mapformat_speed
{
public:
  explicit Init_Mapformat_speed(::common_msgs_humble::msg::Mapformat & msg)
  : msg_(msg)
  {}
  Init_Mapformat_lanetype speed(::common_msgs_humble::msg::Mapformat::_speed_type arg)
  {
    msg_.speed = std::move(arg);
    return Init_Mapformat_lanetype(msg_);
  }

private:
  ::common_msgs_humble::msg::Mapformat msg_;
};

class Init_Mapformat_roadtype
{
public:
  explicit Init_Mapformat_roadtype(::common_msgs_humble::msg::Mapformat & msg)
  : msg_(msg)
  {}
  Init_Mapformat_speed roadtype(::common_msgs_humble::msg::Mapformat::_roadtype_type arg)
  {
    msg_.roadtype = std::move(arg);
    return Init_Mapformat_speed(msg_);
  }

private:
  ::common_msgs_humble::msg::Mapformat msg_;
};

class Init_Mapformat_lat
{
public:
  explicit Init_Mapformat_lat(::common_msgs_humble::msg::Mapformat & msg)
  : msg_(msg)
  {}
  Init_Mapformat_roadtype lat(::common_msgs_humble::msg::Mapformat::_lat_type arg)
  {
    msg_.lat = std::move(arg);
    return Init_Mapformat_roadtype(msg_);
  }

private:
  ::common_msgs_humble::msg::Mapformat msg_;
};

class Init_Mapformat_lon
{
public:
  Init_Mapformat_lon()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Mapformat_lat lon(::common_msgs_humble::msg::Mapformat::_lon_type arg)
  {
    msg_.lon = std::move(arg);
    return Init_Mapformat_lat(msg_);
  }

private:
  ::common_msgs_humble::msg::Mapformat msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Mapformat>()
{
  return common_msgs_humble::msg::builder::Init_Mapformat_lon();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__MAPFORMAT__BUILDER_HPP_
