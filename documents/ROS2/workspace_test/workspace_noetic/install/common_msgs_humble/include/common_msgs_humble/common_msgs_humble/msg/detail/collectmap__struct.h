// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Collectmap.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__COLLECTMAP__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__COLLECTMAP__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

/// Struct defined in msg/Collectmap in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Collectmap
{
  uint8_t mapname;
  uint8_t zonename;
  uint8_t property;
  uint8_t laneattr;
  uint8_t speed;
  float sideroadwidth;
  uint8_t mergelanetype;
  uint8_t sensorlanetype;
  float leftsearchdis;
  float rightsearchdis;
  int64_t timestamp;
  float lanewidth;
  float leftlanewidth;
  float rightlanewidth;
  uint8_t laneswitch;
  uint8_t sidepass;
  uint8_t lanenum;
  uint8_t lanesite;
} common_msgs_humble__msg__Collectmap;

// Struct for a sequence of common_msgs_humble__msg__Collectmap.
typedef struct common_msgs_humble__msg__Collectmap__Sequence
{
  common_msgs_humble__msg__Collectmap * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Collectmap__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__COLLECTMAP__STRUCT_H_
