// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Lonlat.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__LONLAT__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__LONLAT__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/lonlat__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Lonlat_heading
{
public:
  explicit Init_Lonlat_heading(::common_msgs_humble::msg::Lonlat & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Lonlat heading(::common_msgs_humble::msg::Lonlat::_heading_type arg)
  {
    msg_.heading = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Lonlat msg_;
};

class Init_Lonlat_lat
{
public:
  explicit Init_Lonlat_lat(::common_msgs_humble::msg::Lonlat & msg)
  : msg_(msg)
  {}
  Init_Lonlat_heading lat(::common_msgs_humble::msg::Lonlat::_lat_type arg)
  {
    msg_.lat = std::move(arg);
    return Init_Lonlat_heading(msg_);
  }

private:
  ::common_msgs_humble::msg::Lonlat msg_;
};

class Init_Lonlat_lon
{
public:
  Init_Lonlat_lon()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Lonlat_lat lon(::common_msgs_humble::msg::Lonlat::_lon_type arg)
  {
    msg_.lon = std::move(arg);
    return Init_Lonlat_lat(msg_);
  }

private:
  ::common_msgs_humble::msg::Lonlat msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Lonlat>()
{
  return common_msgs_humble::msg::builder::Init_Lonlat_lon();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__LONLAT__BUILDER_HPP_
