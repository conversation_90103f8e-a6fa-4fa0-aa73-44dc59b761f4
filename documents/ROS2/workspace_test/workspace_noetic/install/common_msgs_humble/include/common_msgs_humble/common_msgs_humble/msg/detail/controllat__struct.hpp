// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Controllat.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__CONTROLLAT__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__CONTROLLAT__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Controllat __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Controllat __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Controllat_
{
  using Type = Controllat_<ContainerAllocator>;

  explicit Controllat_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->epsmethod = 0;
      this->epsangle = 0;
      this->limitspeed = 0.0f;
      this->epstorque = 0;
      this->lightmethod = 0;
      this->lights = 0;
      this->isvalid = 0;
      this->deviation = 0;
      this->timestamp = 0ll;
      this->apavstatus = 0;
      this->apsstate = 0;
      this->curve = 0.0f;
    }
  }

  explicit Controllat_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->epsmethod = 0;
      this->epsangle = 0;
      this->limitspeed = 0.0f;
      this->epstorque = 0;
      this->lightmethod = 0;
      this->lights = 0;
      this->isvalid = 0;
      this->deviation = 0;
      this->timestamp = 0ll;
      this->apavstatus = 0;
      this->apsstate = 0;
      this->curve = 0.0f;
    }
  }

  // field types and members
  using _epsmethod_type =
    uint8_t;
  _epsmethod_type epsmethod;
  using _epsangle_type =
    int16_t;
  _epsangle_type epsangle;
  using _limitspeed_type =
    float;
  _limitspeed_type limitspeed;
  using _epstorque_type =
    int16_t;
  _epstorque_type epstorque;
  using _lightmethod_type =
    uint8_t;
  _lightmethod_type lightmethod;
  using _lights_type =
    uint8_t;
  _lights_type lights;
  using _isvalid_type =
    uint8_t;
  _isvalid_type isvalid;
  using _deviation_type =
    int16_t;
  _deviation_type deviation;
  using _timestamp_type =
    int64_t;
  _timestamp_type timestamp;
  using _apavstatus_type =
    uint8_t;
  _apavstatus_type apavstatus;
  using _apsstate_type =
    uint8_t;
  _apsstate_type apsstate;
  using _curve_type =
    float;
  _curve_type curve;

  // setters for named parameter idiom
  Type & set__epsmethod(
    const uint8_t & _arg)
  {
    this->epsmethod = _arg;
    return *this;
  }
  Type & set__epsangle(
    const int16_t & _arg)
  {
    this->epsangle = _arg;
    return *this;
  }
  Type & set__limitspeed(
    const float & _arg)
  {
    this->limitspeed = _arg;
    return *this;
  }
  Type & set__epstorque(
    const int16_t & _arg)
  {
    this->epstorque = _arg;
    return *this;
  }
  Type & set__lightmethod(
    const uint8_t & _arg)
  {
    this->lightmethod = _arg;
    return *this;
  }
  Type & set__lights(
    const uint8_t & _arg)
  {
    this->lights = _arg;
    return *this;
  }
  Type & set__isvalid(
    const uint8_t & _arg)
  {
    this->isvalid = _arg;
    return *this;
  }
  Type & set__deviation(
    const int16_t & _arg)
  {
    this->deviation = _arg;
    return *this;
  }
  Type & set__timestamp(
    const int64_t & _arg)
  {
    this->timestamp = _arg;
    return *this;
  }
  Type & set__apavstatus(
    const uint8_t & _arg)
  {
    this->apavstatus = _arg;
    return *this;
  }
  Type & set__apsstate(
    const uint8_t & _arg)
  {
    this->apsstate = _arg;
    return *this;
  }
  Type & set__curve(
    const float & _arg)
  {
    this->curve = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Controllat_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Controllat_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Controllat_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Controllat_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Controllat_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Controllat_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Controllat_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Controllat_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Controllat_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Controllat_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Controllat
    std::shared_ptr<common_msgs_humble::msg::Controllat_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Controllat
    std::shared_ptr<common_msgs_humble::msg::Controllat_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Controllat_ & other) const
  {
    if (this->epsmethod != other.epsmethod) {
      return false;
    }
    if (this->epsangle != other.epsangle) {
      return false;
    }
    if (this->limitspeed != other.limitspeed) {
      return false;
    }
    if (this->epstorque != other.epstorque) {
      return false;
    }
    if (this->lightmethod != other.lightmethod) {
      return false;
    }
    if (this->lights != other.lights) {
      return false;
    }
    if (this->isvalid != other.isvalid) {
      return false;
    }
    if (this->deviation != other.deviation) {
      return false;
    }
    if (this->timestamp != other.timestamp) {
      return false;
    }
    if (this->apavstatus != other.apavstatus) {
      return false;
    }
    if (this->apsstate != other.apsstate) {
      return false;
    }
    if (this->curve != other.curve) {
      return false;
    }
    return true;
  }
  bool operator!=(const Controllat_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Controllat_

// alias to use template instance with default allocator
using Controllat =
  common_msgs_humble::msg::Controllat_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__CONTROLLAT__STRUCT_HPP_
