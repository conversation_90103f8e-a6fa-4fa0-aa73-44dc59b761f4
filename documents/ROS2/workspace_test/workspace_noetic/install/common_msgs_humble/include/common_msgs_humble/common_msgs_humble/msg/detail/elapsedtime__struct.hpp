// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Elapsedtime.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__ELAPSEDTIME__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__ELAPSEDTIME__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Elapsedtime __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Elapsedtime __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Elapsedtime_
{
  using Type = Elapsedtime_<ContainerAllocator>;

  explicit Elapsedtime_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->time = 0.0f;
    }
  }

  explicit Elapsedtime_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->time = 0.0f;
    }
  }

  // field types and members
  using _time_type =
    float;
  _time_type time;

  // setters for named parameter idiom
  Type & set__time(
    const float & _arg)
  {
    this->time = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Elapsedtime_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Elapsedtime_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Elapsedtime_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Elapsedtime_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Elapsedtime_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Elapsedtime_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Elapsedtime_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Elapsedtime_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Elapsedtime_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Elapsedtime_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Elapsedtime
    std::shared_ptr<common_msgs_humble::msg::Elapsedtime_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Elapsedtime
    std::shared_ptr<common_msgs_humble::msg::Elapsedtime_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Elapsedtime_ & other) const
  {
    if (this->time != other.time) {
      return false;
    }
    return true;
  }
  bool operator!=(const Elapsedtime_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Elapsedtime_

// alias to use template instance with default allocator
using Elapsedtime =
  common_msgs_humble::msg::Elapsedtime_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__ELAPSEDTIME__STRUCT_HPP_
