﻿// NOLINT: This file starts with a BOM since it contain non-ASCII characters
// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Remotedrivestatus.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__REMOTEDRIVESTATUS__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__REMOTEDRIVESTATUS__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

/// Struct defined in msg/Remotedrivestatus in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Remotedrivestatus
{
  int64_t timestamp;
  /// 驾驶模式 0-自动驾驶 1-远程驾驶 2-人工驾驶
  uint8_t drivemode;
  /// 远程驾驶状态 0-正常 1-warning 2-丢失数据帧数较多强制刹车 3-延迟较大强制刹车 4-上游数据给出异常强制刹车
  uint8_t remotedrivestatus;
  /// 车辆异常标志位，预留，待定义
  uint8_t systemstatus;
} common_msgs_humble__msg__Remotedrivestatus;

// Struct for a sequence of common_msgs_humble__msg__Remotedrivestatus.
typedef struct common_msgs_humble__msg__Remotedrivestatus__Sequence
{
  common_msgs_humble__msg__Remotedrivestatus * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Remotedrivestatus__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__REMOTEDRIVESTATUS__STRUCT_H_
