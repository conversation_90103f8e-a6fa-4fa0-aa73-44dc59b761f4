// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Sensorobjects.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSOROBJECTS__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSOROBJECTS__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/sensorobjects__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Sensorobjects_gpstime
{
public:
  explicit Init_Sensorobjects_gpstime(::common_msgs_humble::msg::Sensorobjects & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Sensorobjects gpstime(::common_msgs_humble::msg::Sensorobjects::_gpstime_type arg)
  {
    msg_.gpstime = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobjects msg_;
};

class Init_Sensorobjects_timestamp
{
public:
  explicit Init_Sensorobjects_timestamp(::common_msgs_humble::msg::Sensorobjects & msg)
  : msg_(msg)
  {}
  Init_Sensorobjects_gpstime timestamp(::common_msgs_humble::msg::Sensorobjects::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return Init_Sensorobjects_gpstime(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobjects msg_;
};

class Init_Sensorobjects_isvalid
{
public:
  explicit Init_Sensorobjects_isvalid(::common_msgs_humble::msg::Sensorobjects & msg)
  : msg_(msg)
  {}
  Init_Sensorobjects_timestamp isvalid(::common_msgs_humble::msg::Sensorobjects::_isvalid_type arg)
  {
    msg_.isvalid = std::move(arg);
    return Init_Sensorobjects_timestamp(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobjects msg_;
};

class Init_Sensorobjects_obs
{
public:
  Init_Sensorobjects_obs()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Sensorobjects_isvalid obs(::common_msgs_humble::msg::Sensorobjects::_obs_type arg)
  {
    msg_.obs = std::move(arg);
    return Init_Sensorobjects_isvalid(msg_);
  }

private:
  ::common_msgs_humble::msg::Sensorobjects msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Sensorobjects>()
{
  return common_msgs_humble::msg::builder::Init_Sensorobjects_obs();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSOROBJECTS__BUILDER_HPP_
