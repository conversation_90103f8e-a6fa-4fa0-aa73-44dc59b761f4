﻿// NOLINT: This file starts with a BOM since it contain non-ASCII characters
// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Error.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__ERROR__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__ERROR__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'describe'
#include "rosidl_runtime_c/string.h"

/// Struct defined in msg/Error in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Error
{
  /// 发生故障时的时间戳
  int64_t timestamp;
  uint8_t maincode;
  uint16_t subcode;
  /// 每个模块添加故障描，直接显示在界面
  rosidl_runtime_c__String describe;
} common_msgs_humble__msg__Error;

// Struct for a sequence of common_msgs_humble__msg__Error.
typedef struct common_msgs_humble__msg__Error__Sequence
{
  common_msgs_humble__msg__Error * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Error__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__ERROR__STRUCT_H_
