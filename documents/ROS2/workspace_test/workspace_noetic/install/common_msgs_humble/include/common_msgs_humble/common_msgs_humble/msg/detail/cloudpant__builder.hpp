// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Cloudpant.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__CLOUDPANT__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__CLOUDPANT__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/cloudpant__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Cloudpant_sportconfidence
{
public:
  explicit Init_Cloudpant_sportconfidence(::common_msgs_humble::msg::Cloudpant & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Cloudpant sportconfidence(::common_msgs_humble::msg::Cloudpant::_sportconfidence_type arg)
  {
    msg_.sportconfidence = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Cloudpant msg_;
};

class Init_Cloudpant_courseangle
{
public:
  explicit Init_Cloudpant_courseangle(::common_msgs_humble::msg::Cloudpant & msg)
  : msg_(msg)
  {}
  Init_Cloudpant_sportconfidence courseangle(::common_msgs_humble::msg::Cloudpant::_courseangle_type arg)
  {
    msg_.courseangle = std::move(arg);
    return Init_Cloudpant_sportconfidence(msg_);
  }

private:
  ::common_msgs_humble::msg::Cloudpant msg_;
};

class Init_Cloudpant_speed
{
public:
  explicit Init_Cloudpant_speed(::common_msgs_humble::msg::Cloudpant & msg)
  : msg_(msg)
  {}
  Init_Cloudpant_courseangle speed(::common_msgs_humble::msg::Cloudpant::_speed_type arg)
  {
    msg_.speed = std::move(arg);
    return Init_Cloudpant_courseangle(msg_);
  }

private:
  ::common_msgs_humble::msg::Cloudpant msg_;
};

class Init_Cloudpant_locationconfidence
{
public:
  explicit Init_Cloudpant_locationconfidence(::common_msgs_humble::msg::Cloudpant & msg)
  : msg_(msg)
  {}
  Init_Cloudpant_speed locationconfidence(::common_msgs_humble::msg::Cloudpant::_locationconfidence_type arg)
  {
    msg_.locationconfidence = std::move(arg);
    return Init_Cloudpant_speed(msg_);
  }

private:
  ::common_msgs_humble::msg::Cloudpant msg_;
};

class Init_Cloudpant_latitude
{
public:
  explicit Init_Cloudpant_latitude(::common_msgs_humble::msg::Cloudpant & msg)
  : msg_(msg)
  {}
  Init_Cloudpant_locationconfidence latitude(::common_msgs_humble::msg::Cloudpant::_latitude_type arg)
  {
    msg_.latitude = std::move(arg);
    return Init_Cloudpant_locationconfidence(msg_);
  }

private:
  ::common_msgs_humble::msg::Cloudpant msg_;
};

class Init_Cloudpant_longitude
{
public:
  explicit Init_Cloudpant_longitude(::common_msgs_humble::msg::Cloudpant & msg)
  : msg_(msg)
  {}
  Init_Cloudpant_latitude longitude(::common_msgs_humble::msg::Cloudpant::_longitude_type arg)
  {
    msg_.longitude = std::move(arg);
    return Init_Cloudpant_latitude(msg_);
  }

private:
  ::common_msgs_humble::msg::Cloudpant msg_;
};

class Init_Cloudpant_height
{
public:
  explicit Init_Cloudpant_height(::common_msgs_humble::msg::Cloudpant & msg)
  : msg_(msg)
  {}
  Init_Cloudpant_longitude height(::common_msgs_humble::msg::Cloudpant::_height_type arg)
  {
    msg_.height = std::move(arg);
    return Init_Cloudpant_longitude(msg_);
  }

private:
  ::common_msgs_humble::msg::Cloudpant msg_;
};

class Init_Cloudpant_width
{
public:
  explicit Init_Cloudpant_width(::common_msgs_humble::msg::Cloudpant & msg)
  : msg_(msg)
  {}
  Init_Cloudpant_height width(::common_msgs_humble::msg::Cloudpant::_width_type arg)
  {
    msg_.width = std::move(arg);
    return Init_Cloudpant_height(msg_);
  }

private:
  ::common_msgs_humble::msg::Cloudpant msg_;
};

class Init_Cloudpant_length
{
public:
  explicit Init_Cloudpant_length(::common_msgs_humble::msg::Cloudpant & msg)
  : msg_(msg)
  {}
  Init_Cloudpant_width length(::common_msgs_humble::msg::Cloudpant::_length_type arg)
  {
    msg_.length = std::move(arg);
    return Init_Cloudpant_width(msg_);
  }

private:
  ::common_msgs_humble::msg::Cloudpant msg_;
};

class Init_Cloudpant_vehicletype
{
public:
  explicit Init_Cloudpant_vehicletype(::common_msgs_humble::msg::Cloudpant & msg)
  : msg_(msg)
  {}
  Init_Cloudpant_length vehicletype(::common_msgs_humble::msg::Cloudpant::_vehicletype_type arg)
  {
    msg_.vehicletype = std::move(arg);
    return Init_Cloudpant_length(msg_);
  }

private:
  ::common_msgs_humble::msg::Cloudpant msg_;
};

class Init_Cloudpant_id
{
public:
  Init_Cloudpant_id()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Cloudpant_vehicletype id(::common_msgs_humble::msg::Cloudpant::_id_type arg)
  {
    msg_.id = std::move(arg);
    return Init_Cloudpant_vehicletype(msg_);
  }

private:
  ::common_msgs_humble::msg::Cloudpant msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Cloudpant>()
{
  return common_msgs_humble::msg::builder::Init_Cloudpant_id();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__CLOUDPANT__BUILDER_HPP_
