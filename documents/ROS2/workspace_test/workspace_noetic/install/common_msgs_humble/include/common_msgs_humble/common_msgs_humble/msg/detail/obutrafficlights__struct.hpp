// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Obutrafficlights.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUTRAFFICLIGHTS__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUTRAFFICLIGHTS__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'light'
#include "common_msgs_humble/msg/detail/obulight__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Obutrafficlights __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Obutrafficlights __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Obutrafficlights_
{
  using Type = Obutrafficlights_<ContainerAllocator>;

  explicit Obutrafficlights_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->timestamp = 0ll;
      this->region_id = 0ul;
      this->node_id = 0ul;
      this->light_status = 0;
      this->phase_cnt = 0;
    }
  }

  explicit Obutrafficlights_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->timestamp = 0ll;
      this->region_id = 0ul;
      this->node_id = 0ul;
      this->light_status = 0;
      this->phase_cnt = 0;
    }
  }

  // field types and members
  using _timestamp_type =
    int64_t;
  _timestamp_type timestamp;
  using _region_id_type =
    uint32_t;
  _region_id_type region_id;
  using _node_id_type =
    uint32_t;
  _node_id_type node_id;
  using _light_status_type =
    uint8_t;
  _light_status_type light_status;
  using _phase_cnt_type =
    uint8_t;
  _phase_cnt_type phase_cnt;
  using _light_type =
    std::vector<common_msgs_humble::msg::Obulight_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<common_msgs_humble::msg::Obulight_<ContainerAllocator>>>;
  _light_type light;

  // setters for named parameter idiom
  Type & set__timestamp(
    const int64_t & _arg)
  {
    this->timestamp = _arg;
    return *this;
  }
  Type & set__region_id(
    const uint32_t & _arg)
  {
    this->region_id = _arg;
    return *this;
  }
  Type & set__node_id(
    const uint32_t & _arg)
  {
    this->node_id = _arg;
    return *this;
  }
  Type & set__light_status(
    const uint8_t & _arg)
  {
    this->light_status = _arg;
    return *this;
  }
  Type & set__phase_cnt(
    const uint8_t & _arg)
  {
    this->phase_cnt = _arg;
    return *this;
  }
  Type & set__light(
    const std::vector<common_msgs_humble::msg::Obulight_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<common_msgs_humble::msg::Obulight_<ContainerAllocator>>> & _arg)
  {
    this->light = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Obutrafficlights_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Obutrafficlights_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Obutrafficlights_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Obutrafficlights_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Obutrafficlights_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Obutrafficlights_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Obutrafficlights_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Obutrafficlights_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Obutrafficlights_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Obutrafficlights_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Obutrafficlights
    std::shared_ptr<common_msgs_humble::msg::Obutrafficlights_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Obutrafficlights
    std::shared_ptr<common_msgs_humble::msg::Obutrafficlights_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Obutrafficlights_ & other) const
  {
    if (this->timestamp != other.timestamp) {
      return false;
    }
    if (this->region_id != other.region_id) {
      return false;
    }
    if (this->node_id != other.node_id) {
      return false;
    }
    if (this->light_status != other.light_status) {
      return false;
    }
    if (this->phase_cnt != other.phase_cnt) {
      return false;
    }
    if (this->light != other.light) {
      return false;
    }
    return true;
  }
  bool operator!=(const Obutrafficlights_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Obutrafficlights_

// alias to use template instance with default allocator
using Obutrafficlights =
  common_msgs_humble::msg::Obutrafficlights_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUTRAFFICLIGHTS__STRUCT_HPP_
