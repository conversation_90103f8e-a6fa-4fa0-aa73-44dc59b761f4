// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Decisionbehavior.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__DECISIONBEHAVIOR__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__DECISIONBEHAVIOR__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'obs'
#include "common_msgs_humble/msg/detail/sensorobject__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Decisionbehavior __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Decisionbehavior __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Decisionbehavior_
{
  using Type = Decisionbehavior_<ContainerAllocator>;

  explicit Decisionbehavior_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->drivebehavior = 0;
      this->isvalid = 0;
      this->turnlights = 0;
      this->laneblock = 0;
      this->door = 0;
      this->timestamp = 0ll;
      this->mergetrigger = 0;
      this->guidespeed = 0.0f;
      this->avoidsituation = 0;
      this->alert = 0;
      this->deviation = 0.0f;
      this->starttime = 0.0f;
      this->endtime = 0.0f;
      this->carworkstatus = 0;
      this->stationblock = false;
      this->needreplan = 0;
      this->virtualpointtype = 0;
    }
  }

  explicit Decisionbehavior_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->drivebehavior = 0;
      this->isvalid = 0;
      this->turnlights = 0;
      this->laneblock = 0;
      this->door = 0;
      this->timestamp = 0ll;
      this->mergetrigger = 0;
      this->guidespeed = 0.0f;
      this->avoidsituation = 0;
      this->alert = 0;
      this->deviation = 0.0f;
      this->starttime = 0.0f;
      this->endtime = 0.0f;
      this->carworkstatus = 0;
      this->stationblock = false;
      this->needreplan = 0;
      this->virtualpointtype = 0;
    }
  }

  // field types and members
  using _drivebehavior_type =
    uint8_t;
  _drivebehavior_type drivebehavior;
  using _obs_type =
    std::vector<common_msgs_humble::msg::Sensorobject_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<common_msgs_humble::msg::Sensorobject_<ContainerAllocator>>>;
  _obs_type obs;
  using _isvalid_type =
    uint8_t;
  _isvalid_type isvalid;
  using _turnlights_type =
    uint8_t;
  _turnlights_type turnlights;
  using _laneblock_type =
    uint8_t;
  _laneblock_type laneblock;
  using _door_type =
    uint8_t;
  _door_type door;
  using _timestamp_type =
    int64_t;
  _timestamp_type timestamp;
  using _mergetrigger_type =
    uint8_t;
  _mergetrigger_type mergetrigger;
  using _guidespeed_type =
    float;
  _guidespeed_type guidespeed;
  using _avoidsituation_type =
    uint8_t;
  _avoidsituation_type avoidsituation;
  using _alert_type =
    uint8_t;
  _alert_type alert;
  using _deviation_type =
    float;
  _deviation_type deviation;
  using _starttime_type =
    float;
  _starttime_type starttime;
  using _endtime_type =
    float;
  _endtime_type endtime;
  using _carworkstatus_type =
    uint8_t;
  _carworkstatus_type carworkstatus;
  using _stationblock_type =
    bool;
  _stationblock_type stationblock;
  using _needreplan_type =
    uint8_t;
  _needreplan_type needreplan;
  using _virtualpointtype_type =
    uint8_t;
  _virtualpointtype_type virtualpointtype;

  // setters for named parameter idiom
  Type & set__drivebehavior(
    const uint8_t & _arg)
  {
    this->drivebehavior = _arg;
    return *this;
  }
  Type & set__obs(
    const std::vector<common_msgs_humble::msg::Sensorobject_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<common_msgs_humble::msg::Sensorobject_<ContainerAllocator>>> & _arg)
  {
    this->obs = _arg;
    return *this;
  }
  Type & set__isvalid(
    const uint8_t & _arg)
  {
    this->isvalid = _arg;
    return *this;
  }
  Type & set__turnlights(
    const uint8_t & _arg)
  {
    this->turnlights = _arg;
    return *this;
  }
  Type & set__laneblock(
    const uint8_t & _arg)
  {
    this->laneblock = _arg;
    return *this;
  }
  Type & set__door(
    const uint8_t & _arg)
  {
    this->door = _arg;
    return *this;
  }
  Type & set__timestamp(
    const int64_t & _arg)
  {
    this->timestamp = _arg;
    return *this;
  }
  Type & set__mergetrigger(
    const uint8_t & _arg)
  {
    this->mergetrigger = _arg;
    return *this;
  }
  Type & set__guidespeed(
    const float & _arg)
  {
    this->guidespeed = _arg;
    return *this;
  }
  Type & set__avoidsituation(
    const uint8_t & _arg)
  {
    this->avoidsituation = _arg;
    return *this;
  }
  Type & set__alert(
    const uint8_t & _arg)
  {
    this->alert = _arg;
    return *this;
  }
  Type & set__deviation(
    const float & _arg)
  {
    this->deviation = _arg;
    return *this;
  }
  Type & set__starttime(
    const float & _arg)
  {
    this->starttime = _arg;
    return *this;
  }
  Type & set__endtime(
    const float & _arg)
  {
    this->endtime = _arg;
    return *this;
  }
  Type & set__carworkstatus(
    const uint8_t & _arg)
  {
    this->carworkstatus = _arg;
    return *this;
  }
  Type & set__stationblock(
    const bool & _arg)
  {
    this->stationblock = _arg;
    return *this;
  }
  Type & set__needreplan(
    const uint8_t & _arg)
  {
    this->needreplan = _arg;
    return *this;
  }
  Type & set__virtualpointtype(
    const uint8_t & _arg)
  {
    this->virtualpointtype = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Decisionbehavior_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Decisionbehavior_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Decisionbehavior_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Decisionbehavior_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Decisionbehavior_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Decisionbehavior_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Decisionbehavior_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Decisionbehavior_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Decisionbehavior_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Decisionbehavior_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Decisionbehavior
    std::shared_ptr<common_msgs_humble::msg::Decisionbehavior_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Decisionbehavior
    std::shared_ptr<common_msgs_humble::msg::Decisionbehavior_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Decisionbehavior_ & other) const
  {
    if (this->drivebehavior != other.drivebehavior) {
      return false;
    }
    if (this->obs != other.obs) {
      return false;
    }
    if (this->isvalid != other.isvalid) {
      return false;
    }
    if (this->turnlights != other.turnlights) {
      return false;
    }
    if (this->laneblock != other.laneblock) {
      return false;
    }
    if (this->door != other.door) {
      return false;
    }
    if (this->timestamp != other.timestamp) {
      return false;
    }
    if (this->mergetrigger != other.mergetrigger) {
      return false;
    }
    if (this->guidespeed != other.guidespeed) {
      return false;
    }
    if (this->avoidsituation != other.avoidsituation) {
      return false;
    }
    if (this->alert != other.alert) {
      return false;
    }
    if (this->deviation != other.deviation) {
      return false;
    }
    if (this->starttime != other.starttime) {
      return false;
    }
    if (this->endtime != other.endtime) {
      return false;
    }
    if (this->carworkstatus != other.carworkstatus) {
      return false;
    }
    if (this->stationblock != other.stationblock) {
      return false;
    }
    if (this->needreplan != other.needreplan) {
      return false;
    }
    if (this->virtualpointtype != other.virtualpointtype) {
      return false;
    }
    return true;
  }
  bool operator!=(const Decisionbehavior_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Decisionbehavior_

// alias to use template instance with default allocator
using Decisionbehavior =
  common_msgs_humble::msg::Decisionbehavior_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__DECISIONBEHAVIOR__STRUCT_HPP_
