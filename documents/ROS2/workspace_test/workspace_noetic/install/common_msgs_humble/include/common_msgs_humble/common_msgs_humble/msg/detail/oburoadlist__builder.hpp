// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Oburoadlist.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUROADLIST__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUROADLIST__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/oburoadlist__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Oburoadlist_oburoadpoint
{
public:
  explicit Init_Oburoadlist_oburoadpoint(::common_msgs_humble::msg::Oburoadlist & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Oburoadlist oburoadpoint(::common_msgs_humble::msg::Oburoadlist::_oburoadpoint_type arg)
  {
    msg_.oburoadpoint = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Oburoadlist msg_;
};

class Init_Oburoadlist_planpoints
{
public:
  Init_Oburoadlist_planpoints()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Oburoadlist_oburoadpoint planpoints(::common_msgs_humble::msg::Oburoadlist::_planpoints_type arg)
  {
    msg_.planpoints = std::move(arg);
    return Init_Oburoadlist_oburoadpoint(msg_);
  }

private:
  ::common_msgs_humble::msg::Oburoadlist msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Oburoadlist>()
{
  return common_msgs_humble::msg::builder::Init_Oburoadlist_planpoints();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUROADLIST__BUILDER_HPP_
