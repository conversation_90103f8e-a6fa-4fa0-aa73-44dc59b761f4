// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from common_msgs_humble:msg/Sensorobjects.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSOROBJECTS__TRAITS_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSOROBJECTS__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "common_msgs_humble/msg/detail/sensorobjects__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

// Include directives for member types
// Member 'obs'
#include "common_msgs_humble/msg/detail/sensorobject__traits.hpp"

namespace common_msgs_humble
{

namespace msg
{

inline void to_flow_style_yaml(
  const Sensorobjects & msg,
  std::ostream & out)
{
  out << "{";
  // member: obs
  {
    if (msg.obs.size() == 0) {
      out << "obs: []";
    } else {
      out << "obs: [";
      size_t pending_items = msg.obs.size();
      for (auto item : msg.obs) {
        to_flow_style_yaml(item, out);
        if (--pending_items > 0) {
          out << ", ";
        }
      }
      out << "]";
    }
    out << ", ";
  }

  // member: isvalid
  {
    out << "isvalid: ";
    rosidl_generator_traits::value_to_yaml(msg.isvalid, out);
    out << ", ";
  }

  // member: timestamp
  {
    out << "timestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.timestamp, out);
    out << ", ";
  }

  // member: gpstime
  {
    out << "gpstime: ";
    rosidl_generator_traits::value_to_yaml(msg.gpstime, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const Sensorobjects & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: obs
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    if (msg.obs.size() == 0) {
      out << "obs: []\n";
    } else {
      out << "obs:\n";
      for (auto item : msg.obs) {
        if (indentation > 0) {
          out << std::string(indentation, ' ');
        }
        out << "-\n";
        to_block_style_yaml(item, out, indentation + 2);
      }
    }
  }

  // member: isvalid
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "isvalid: ";
    rosidl_generator_traits::value_to_yaml(msg.isvalid, out);
    out << "\n";
  }

  // member: timestamp
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "timestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.timestamp, out);
    out << "\n";
  }

  // member: gpstime
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "gpstime: ";
    rosidl_generator_traits::value_to_yaml(msg.gpstime, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const Sensorobjects & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace common_msgs_humble

namespace rosidl_generator_traits
{

[[deprecated("use common_msgs_humble::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const common_msgs_humble::msg::Sensorobjects & msg,
  std::ostream & out, size_t indentation = 0)
{
  common_msgs_humble::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use common_msgs_humble::msg::to_yaml() instead")]]
inline std::string to_yaml(const common_msgs_humble::msg::Sensorobjects & msg)
{
  return common_msgs_humble::msg::to_yaml(msg);
}

template<>
inline const char * data_type<common_msgs_humble::msg::Sensorobjects>()
{
  return "common_msgs_humble::msg::Sensorobjects";
}

template<>
inline const char * name<common_msgs_humble::msg::Sensorobjects>()
{
  return "common_msgs_humble/msg/Sensorobjects";
}

template<>
struct has_fixed_size<common_msgs_humble::msg::Sensorobjects>
  : std::integral_constant<bool, false> {};

template<>
struct has_bounded_size<common_msgs_humble::msg::Sensorobjects>
  : std::integral_constant<bool, false> {};

template<>
struct is_message<common_msgs_humble::msg::Sensorobjects>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSOROBJECTS__TRAITS_HPP_
