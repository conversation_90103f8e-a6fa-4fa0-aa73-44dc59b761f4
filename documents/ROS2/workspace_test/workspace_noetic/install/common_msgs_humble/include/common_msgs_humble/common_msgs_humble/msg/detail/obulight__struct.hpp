// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Obulight.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__OBULIGHT__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__OBULIGHT__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Obulight __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Obulight __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Obulight_
{
  using Type = Obulight_<ContainerAllocator>;

  explicit Obulight_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->phase_id = 0l;
      this->status = 0;
      this->start_time = 0ll;
      this->end_time = 0ll;
      this->next_start_time = 0ll;
      this->lane_speed_lower = 0.0f;
      this->lane_speed_upper = 0.0f;
    }
  }

  explicit Obulight_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->phase_id = 0l;
      this->status = 0;
      this->start_time = 0ll;
      this->end_time = 0ll;
      this->next_start_time = 0ll;
      this->lane_speed_lower = 0.0f;
      this->lane_speed_upper = 0.0f;
    }
  }

  // field types and members
  using _phase_id_type =
    int32_t;
  _phase_id_type phase_id;
  using _status_type =
    uint8_t;
  _status_type status;
  using _start_time_type =
    int64_t;
  _start_time_type start_time;
  using _end_time_type =
    int64_t;
  _end_time_type end_time;
  using _next_start_time_type =
    int64_t;
  _next_start_time_type next_start_time;
  using _lane_speed_lower_type =
    float;
  _lane_speed_lower_type lane_speed_lower;
  using _lane_speed_upper_type =
    float;
  _lane_speed_upper_type lane_speed_upper;

  // setters for named parameter idiom
  Type & set__phase_id(
    const int32_t & _arg)
  {
    this->phase_id = _arg;
    return *this;
  }
  Type & set__status(
    const uint8_t & _arg)
  {
    this->status = _arg;
    return *this;
  }
  Type & set__start_time(
    const int64_t & _arg)
  {
    this->start_time = _arg;
    return *this;
  }
  Type & set__end_time(
    const int64_t & _arg)
  {
    this->end_time = _arg;
    return *this;
  }
  Type & set__next_start_time(
    const int64_t & _arg)
  {
    this->next_start_time = _arg;
    return *this;
  }
  Type & set__lane_speed_lower(
    const float & _arg)
  {
    this->lane_speed_lower = _arg;
    return *this;
  }
  Type & set__lane_speed_upper(
    const float & _arg)
  {
    this->lane_speed_upper = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Obulight_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Obulight_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Obulight_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Obulight_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Obulight_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Obulight_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Obulight_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Obulight_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Obulight_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Obulight_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Obulight
    std::shared_ptr<common_msgs_humble::msg::Obulight_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Obulight
    std::shared_ptr<common_msgs_humble::msg::Obulight_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Obulight_ & other) const
  {
    if (this->phase_id != other.phase_id) {
      return false;
    }
    if (this->status != other.status) {
      return false;
    }
    if (this->start_time != other.start_time) {
      return false;
    }
    if (this->end_time != other.end_time) {
      return false;
    }
    if (this->next_start_time != other.next_start_time) {
      return false;
    }
    if (this->lane_speed_lower != other.lane_speed_lower) {
      return false;
    }
    if (this->lane_speed_upper != other.lane_speed_upper) {
      return false;
    }
    return true;
  }
  bool operator!=(const Obulight_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Obulight_

// alias to use template instance with default allocator
using Obulight =
  common_msgs_humble::msg::Obulight_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__OBULIGHT__STRUCT_HPP_
