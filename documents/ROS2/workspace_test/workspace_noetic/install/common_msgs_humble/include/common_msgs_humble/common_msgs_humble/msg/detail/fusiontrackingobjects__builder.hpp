// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Fusiontrackingobjects.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__FUSIONTRACKINGOBJECTS__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__FUSIONTRACKINGOBJECTS__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/fusiontrackingobjects__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Fusiontrackingobjects_gpstime
{
public:
  explicit Init_Fusiontrackingobjects_gpstime(::common_msgs_humble::msg::Fusiontrackingobjects & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Fusiontrackingobjects gpstime(::common_msgs_humble::msg::Fusiontrackingobjects::_gpstime_type arg)
  {
    msg_.gpstime = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Fusiontrackingobjects msg_;
};

class Init_Fusiontrackingobjects_timestamp
{
public:
  explicit Init_Fusiontrackingobjects_timestamp(::common_msgs_humble::msg::Fusiontrackingobjects & msg)
  : msg_(msg)
  {}
  Init_Fusiontrackingobjects_gpstime timestamp(::common_msgs_humble::msg::Fusiontrackingobjects::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return Init_Fusiontrackingobjects_gpstime(msg_);
  }

private:
  ::common_msgs_humble::msg::Fusiontrackingobjects msg_;
};

class Init_Fusiontrackingobjects_isvalid
{
public:
  explicit Init_Fusiontrackingobjects_isvalid(::common_msgs_humble::msg::Fusiontrackingobjects & msg)
  : msg_(msg)
  {}
  Init_Fusiontrackingobjects_timestamp isvalid(::common_msgs_humble::msg::Fusiontrackingobjects::_isvalid_type arg)
  {
    msg_.isvalid = std::move(arg);
    return Init_Fusiontrackingobjects_timestamp(msg_);
  }

private:
  ::common_msgs_humble::msg::Fusiontrackingobjects msg_;
};

class Init_Fusiontrackingobjects_obs
{
public:
  Init_Fusiontrackingobjects_obs()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Fusiontrackingobjects_isvalid obs(::common_msgs_humble::msg::Fusiontrackingobjects::_obs_type arg)
  {
    msg_.obs = std::move(arg);
    return Init_Fusiontrackingobjects_isvalid(msg_);
  }

private:
  ::common_msgs_humble::msg::Fusiontrackingobjects msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Fusiontrackingobjects>()
{
  return common_msgs_humble::msg::builder::Init_Fusiontrackingobjects_obs();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__FUSIONTRACKINGOBJECTS__BUILDER_HPP_
