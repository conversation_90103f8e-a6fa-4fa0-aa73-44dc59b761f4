// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Lonlatmappoints.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__LONLATMAPPOINTS__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__LONLATMAPPOINTS__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'mapname'
// Member 'zonename'
#include "rosidl_runtime_c/string.h"
// Member 'points'
#include "common_msgs_humble/msg/detail/lonlat__struct.h"

/// Struct defined in msg/Lonlatmappoints in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Lonlatmappoints
{
  rosidl_runtime_c__String mapname;
  rosidl_runtime_c__String zonename;
  common_msgs_humble__msg__Lonlat__Sequence points;
  int64_t timestamp;
} common_msgs_humble__msg__Lonlatmappoints;

// Struct for a sequence of common_msgs_humble__msg__Lonlatmappoints.
typedef struct common_msgs_humble__msg__Lonlatmappoints__Sequence
{
  common_msgs_humble__msg__Lonlatmappoints * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Lonlatmappoints__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__LONLATMAPPOINTS__STRUCT_H_
