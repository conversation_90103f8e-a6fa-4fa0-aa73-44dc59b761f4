// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from common_msgs_humble:msg/Fusiontrackingobjects.idl
// generated code does not contain a copyright notice
#include "common_msgs_humble/msg/detail/fusiontrackingobjects__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


// Include directives for member types
// Member `obs`
#include "common_msgs_humble/msg/detail/fusiontrackingobject__functions.h"

bool
common_msgs_humble__msg__Fusiontrackingobjects__init(common_msgs_humble__msg__Fusiontrackingobjects * msg)
{
  if (!msg) {
    return false;
  }
  // obs
  if (!common_msgs_humble__msg__Fusiontrackingobject__Sequence__init(&msg->obs, 0)) {
    common_msgs_humble__msg__Fusiontrackingobjects__fini(msg);
    return false;
  }
  // isvalid
  // timestamp
  // gpstime
  return true;
}

void
common_msgs_humble__msg__Fusiontrackingobjects__fini(common_msgs_humble__msg__Fusiontrackingobjects * msg)
{
  if (!msg) {
    return;
  }
  // obs
  common_msgs_humble__msg__Fusiontrackingobject__Sequence__fini(&msg->obs);
  // isvalid
  // timestamp
  // gpstime
}

bool
common_msgs_humble__msg__Fusiontrackingobjects__are_equal(const common_msgs_humble__msg__Fusiontrackingobjects * lhs, const common_msgs_humble__msg__Fusiontrackingobjects * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // obs
  if (!common_msgs_humble__msg__Fusiontrackingobject__Sequence__are_equal(
      &(lhs->obs), &(rhs->obs)))
  {
    return false;
  }
  // isvalid
  if (lhs->isvalid != rhs->isvalid) {
    return false;
  }
  // timestamp
  if (lhs->timestamp != rhs->timestamp) {
    return false;
  }
  // gpstime
  if (lhs->gpstime != rhs->gpstime) {
    return false;
  }
  return true;
}

bool
common_msgs_humble__msg__Fusiontrackingobjects__copy(
  const common_msgs_humble__msg__Fusiontrackingobjects * input,
  common_msgs_humble__msg__Fusiontrackingobjects * output)
{
  if (!input || !output) {
    return false;
  }
  // obs
  if (!common_msgs_humble__msg__Fusiontrackingobject__Sequence__copy(
      &(input->obs), &(output->obs)))
  {
    return false;
  }
  // isvalid
  output->isvalid = input->isvalid;
  // timestamp
  output->timestamp = input->timestamp;
  // gpstime
  output->gpstime = input->gpstime;
  return true;
}

common_msgs_humble__msg__Fusiontrackingobjects *
common_msgs_humble__msg__Fusiontrackingobjects__create()
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Fusiontrackingobjects * msg = (common_msgs_humble__msg__Fusiontrackingobjects *)allocator.allocate(sizeof(common_msgs_humble__msg__Fusiontrackingobjects), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(common_msgs_humble__msg__Fusiontrackingobjects));
  bool success = common_msgs_humble__msg__Fusiontrackingobjects__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
common_msgs_humble__msg__Fusiontrackingobjects__destroy(common_msgs_humble__msg__Fusiontrackingobjects * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    common_msgs_humble__msg__Fusiontrackingobjects__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
common_msgs_humble__msg__Fusiontrackingobjects__Sequence__init(common_msgs_humble__msg__Fusiontrackingobjects__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Fusiontrackingobjects * data = NULL;

  if (size) {
    data = (common_msgs_humble__msg__Fusiontrackingobjects *)allocator.zero_allocate(size, sizeof(common_msgs_humble__msg__Fusiontrackingobjects), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = common_msgs_humble__msg__Fusiontrackingobjects__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        common_msgs_humble__msg__Fusiontrackingobjects__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
common_msgs_humble__msg__Fusiontrackingobjects__Sequence__fini(common_msgs_humble__msg__Fusiontrackingobjects__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      common_msgs_humble__msg__Fusiontrackingobjects__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

common_msgs_humble__msg__Fusiontrackingobjects__Sequence *
common_msgs_humble__msg__Fusiontrackingobjects__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Fusiontrackingobjects__Sequence * array = (common_msgs_humble__msg__Fusiontrackingobjects__Sequence *)allocator.allocate(sizeof(common_msgs_humble__msg__Fusiontrackingobjects__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = common_msgs_humble__msg__Fusiontrackingobjects__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
common_msgs_humble__msg__Fusiontrackingobjects__Sequence__destroy(common_msgs_humble__msg__Fusiontrackingobjects__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    common_msgs_humble__msg__Fusiontrackingobjects__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
common_msgs_humble__msg__Fusiontrackingobjects__Sequence__are_equal(const common_msgs_humble__msg__Fusiontrackingobjects__Sequence * lhs, const common_msgs_humble__msg__Fusiontrackingobjects__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!common_msgs_humble__msg__Fusiontrackingobjects__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
common_msgs_humble__msg__Fusiontrackingobjects__Sequence__copy(
  const common_msgs_humble__msg__Fusiontrackingobjects__Sequence * input,
  common_msgs_humble__msg__Fusiontrackingobjects__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(common_msgs_humble__msg__Fusiontrackingobjects);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    common_msgs_humble__msg__Fusiontrackingobjects * data =
      (common_msgs_humble__msg__Fusiontrackingobjects *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!common_msgs_humble__msg__Fusiontrackingobjects__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          common_msgs_humble__msg__Fusiontrackingobjects__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!common_msgs_humble__msg__Fusiontrackingobjects__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
