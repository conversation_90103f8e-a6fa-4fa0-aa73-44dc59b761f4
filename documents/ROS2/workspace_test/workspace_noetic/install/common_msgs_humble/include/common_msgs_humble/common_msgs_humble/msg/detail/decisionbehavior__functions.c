// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from common_msgs_humble:msg/Decisionbehavior.idl
// generated code does not contain a copyright notice
#include "common_msgs_humble/msg/detail/decisionbehavior__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


// Include directives for member types
// Member `obs`
#include "common_msgs_humble/msg/detail/sensorobject__functions.h"

bool
common_msgs_humble__msg__Decisionbehavior__init(common_msgs_humble__msg__Decisionbehavior * msg)
{
  if (!msg) {
    return false;
  }
  // drivebehavior
  // obs
  if (!common_msgs_humble__msg__Sensorobject__Sequence__init(&msg->obs, 0)) {
    common_msgs_humble__msg__Decisionbehavior__fini(msg);
    return false;
  }
  // isvalid
  // turnlights
  // laneblock
  // door
  // timestamp
  // mergetrigger
  // guidespeed
  // avoidsituation
  // alert
  // deviation
  // starttime
  // endtime
  // carworkstatus
  // stationblock
  // needreplan
  // virtualpointtype
  return true;
}

void
common_msgs_humble__msg__Decisionbehavior__fini(common_msgs_humble__msg__Decisionbehavior * msg)
{
  if (!msg) {
    return;
  }
  // drivebehavior
  // obs
  common_msgs_humble__msg__Sensorobject__Sequence__fini(&msg->obs);
  // isvalid
  // turnlights
  // laneblock
  // door
  // timestamp
  // mergetrigger
  // guidespeed
  // avoidsituation
  // alert
  // deviation
  // starttime
  // endtime
  // carworkstatus
  // stationblock
  // needreplan
  // virtualpointtype
}

bool
common_msgs_humble__msg__Decisionbehavior__are_equal(const common_msgs_humble__msg__Decisionbehavior * lhs, const common_msgs_humble__msg__Decisionbehavior * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // drivebehavior
  if (lhs->drivebehavior != rhs->drivebehavior) {
    return false;
  }
  // obs
  if (!common_msgs_humble__msg__Sensorobject__Sequence__are_equal(
      &(lhs->obs), &(rhs->obs)))
  {
    return false;
  }
  // isvalid
  if (lhs->isvalid != rhs->isvalid) {
    return false;
  }
  // turnlights
  if (lhs->turnlights != rhs->turnlights) {
    return false;
  }
  // laneblock
  if (lhs->laneblock != rhs->laneblock) {
    return false;
  }
  // door
  if (lhs->door != rhs->door) {
    return false;
  }
  // timestamp
  if (lhs->timestamp != rhs->timestamp) {
    return false;
  }
  // mergetrigger
  if (lhs->mergetrigger != rhs->mergetrigger) {
    return false;
  }
  // guidespeed
  if (lhs->guidespeed != rhs->guidespeed) {
    return false;
  }
  // avoidsituation
  if (lhs->avoidsituation != rhs->avoidsituation) {
    return false;
  }
  // alert
  if (lhs->alert != rhs->alert) {
    return false;
  }
  // deviation
  if (lhs->deviation != rhs->deviation) {
    return false;
  }
  // starttime
  if (lhs->starttime != rhs->starttime) {
    return false;
  }
  // endtime
  if (lhs->endtime != rhs->endtime) {
    return false;
  }
  // carworkstatus
  if (lhs->carworkstatus != rhs->carworkstatus) {
    return false;
  }
  // stationblock
  if (lhs->stationblock != rhs->stationblock) {
    return false;
  }
  // needreplan
  if (lhs->needreplan != rhs->needreplan) {
    return false;
  }
  // virtualpointtype
  if (lhs->virtualpointtype != rhs->virtualpointtype) {
    return false;
  }
  return true;
}

bool
common_msgs_humble__msg__Decisionbehavior__copy(
  const common_msgs_humble__msg__Decisionbehavior * input,
  common_msgs_humble__msg__Decisionbehavior * output)
{
  if (!input || !output) {
    return false;
  }
  // drivebehavior
  output->drivebehavior = input->drivebehavior;
  // obs
  if (!common_msgs_humble__msg__Sensorobject__Sequence__copy(
      &(input->obs), &(output->obs)))
  {
    return false;
  }
  // isvalid
  output->isvalid = input->isvalid;
  // turnlights
  output->turnlights = input->turnlights;
  // laneblock
  output->laneblock = input->laneblock;
  // door
  output->door = input->door;
  // timestamp
  output->timestamp = input->timestamp;
  // mergetrigger
  output->mergetrigger = input->mergetrigger;
  // guidespeed
  output->guidespeed = input->guidespeed;
  // avoidsituation
  output->avoidsituation = input->avoidsituation;
  // alert
  output->alert = input->alert;
  // deviation
  output->deviation = input->deviation;
  // starttime
  output->starttime = input->starttime;
  // endtime
  output->endtime = input->endtime;
  // carworkstatus
  output->carworkstatus = input->carworkstatus;
  // stationblock
  output->stationblock = input->stationblock;
  // needreplan
  output->needreplan = input->needreplan;
  // virtualpointtype
  output->virtualpointtype = input->virtualpointtype;
  return true;
}

common_msgs_humble__msg__Decisionbehavior *
common_msgs_humble__msg__Decisionbehavior__create()
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Decisionbehavior * msg = (common_msgs_humble__msg__Decisionbehavior *)allocator.allocate(sizeof(common_msgs_humble__msg__Decisionbehavior), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(common_msgs_humble__msg__Decisionbehavior));
  bool success = common_msgs_humble__msg__Decisionbehavior__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
common_msgs_humble__msg__Decisionbehavior__destroy(common_msgs_humble__msg__Decisionbehavior * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    common_msgs_humble__msg__Decisionbehavior__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
common_msgs_humble__msg__Decisionbehavior__Sequence__init(common_msgs_humble__msg__Decisionbehavior__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Decisionbehavior * data = NULL;

  if (size) {
    data = (common_msgs_humble__msg__Decisionbehavior *)allocator.zero_allocate(size, sizeof(common_msgs_humble__msg__Decisionbehavior), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = common_msgs_humble__msg__Decisionbehavior__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        common_msgs_humble__msg__Decisionbehavior__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
common_msgs_humble__msg__Decisionbehavior__Sequence__fini(common_msgs_humble__msg__Decisionbehavior__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      common_msgs_humble__msg__Decisionbehavior__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

common_msgs_humble__msg__Decisionbehavior__Sequence *
common_msgs_humble__msg__Decisionbehavior__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  common_msgs_humble__msg__Decisionbehavior__Sequence * array = (common_msgs_humble__msg__Decisionbehavior__Sequence *)allocator.allocate(sizeof(common_msgs_humble__msg__Decisionbehavior__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = common_msgs_humble__msg__Decisionbehavior__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
common_msgs_humble__msg__Decisionbehavior__Sequence__destroy(common_msgs_humble__msg__Decisionbehavior__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    common_msgs_humble__msg__Decisionbehavior__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
common_msgs_humble__msg__Decisionbehavior__Sequence__are_equal(const common_msgs_humble__msg__Decisionbehavior__Sequence * lhs, const common_msgs_humble__msg__Decisionbehavior__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!common_msgs_humble__msg__Decisionbehavior__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
common_msgs_humble__msg__Decisionbehavior__Sequence__copy(
  const common_msgs_humble__msg__Decisionbehavior__Sequence * input,
  common_msgs_humble__msg__Decisionbehavior__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(common_msgs_humble__msg__Decisionbehavior);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    common_msgs_humble__msg__Decisionbehavior * data =
      (common_msgs_humble__msg__Decisionbehavior *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!common_msgs_humble__msg__Decisionbehavior__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          common_msgs_humble__msg__Decisionbehavior__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!common_msgs_humble__msg__Decisionbehavior__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
