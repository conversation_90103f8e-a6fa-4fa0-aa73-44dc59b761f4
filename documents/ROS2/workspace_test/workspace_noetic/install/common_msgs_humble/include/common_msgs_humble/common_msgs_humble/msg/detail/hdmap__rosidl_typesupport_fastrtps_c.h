// generated from rosidl_typesupport_fastrtps_c/resource/idl__rosidl_typesupport_fastrtps_c.h.em
// with input from common_msgs_humble:msg/Hdmap.idl
// generated code does not contain a copyright notice
#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__HDMAP__ROSIDL_TYPESUPPORT_FASTRTPS_C_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__HDMAP__ROSIDL_TYPESUPPORT_FASTRTPS_C_H_


#include <stddef.h>
#include "rosidl_runtime_c/message_type_support_struct.h"
#include "rosidl_typesupport_interface/macros.h"
#include "common_msgs_humble/msg/rosidl_typesupport_fastrtps_c__visibility_control.h"

#ifdef __cplusplus
extern "C"
{
#endif

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_common_msgs_humble
size_t get_serialized_size_common_msgs_humble__msg__Hdmap(
  const void * untyped_ros_message,
  size_t current_alignment);

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_common_msgs_humble
size_t max_serialized_size_common_msgs_humble__msg__Hdmap(
  bool & full_bounded,
  bool & is_plain,
  size_t current_alignment);

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_common_msgs_humble
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_fastrtps_c, common_msgs_humble, msg, Hdmap)();

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__HDMAP__ROSIDL_TYPESUPPORT_FASTRTPS_C_H_
