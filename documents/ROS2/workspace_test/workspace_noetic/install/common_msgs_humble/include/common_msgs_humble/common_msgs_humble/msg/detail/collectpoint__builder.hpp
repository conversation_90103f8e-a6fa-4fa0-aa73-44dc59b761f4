// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Collectpoint.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__COLLECTPOINT__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__COLLECTPOINT__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/collectpoint__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Collectpoint_orientation
{
public:
  explicit Init_Collectpoint_orientation(::common_msgs_humble::msg::Collectpoint & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Collectpoint orientation(::common_msgs_humble::msg::Collectpoint::_orientation_type arg)
  {
    msg_.orientation = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Collectpoint msg_;
};

class Init_Collectpoint_property
{
public:
  explicit Init_Collectpoint_property(::common_msgs_humble::msg::Collectpoint & msg)
  : msg_(msg)
  {}
  Init_Collectpoint_orientation property(::common_msgs_humble::msg::Collectpoint::_property_type arg)
  {
    msg_.property = std::move(arg);
    return Init_Collectpoint_orientation(msg_);
  }

private:
  ::common_msgs_humble::msg::Collectpoint msg_;
};

class Init_Collectpoint_stoptime
{
public:
  explicit Init_Collectpoint_stoptime(::common_msgs_humble::msg::Collectpoint & msg)
  : msg_(msg)
  {}
  Init_Collectpoint_property stoptime(::common_msgs_humble::msg::Collectpoint::_stoptime_type arg)
  {
    msg_.stoptime = std::move(arg);
    return Init_Collectpoint_property(msg_);
  }

private:
  ::common_msgs_humble::msg::Collectpoint msg_;
};

class Init_Collectpoint_index
{
public:
  explicit Init_Collectpoint_index(::common_msgs_humble::msg::Collectpoint & msg)
  : msg_(msg)
  {}
  Init_Collectpoint_stoptime index(::common_msgs_humble::msg::Collectpoint::_index_type arg)
  {
    msg_.index = std::move(arg);
    return Init_Collectpoint_stoptime(msg_);
  }

private:
  ::common_msgs_humble::msg::Collectpoint msg_;
};

class Init_Collectpoint_zonename
{
public:
  Init_Collectpoint_zonename()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Collectpoint_index zonename(::common_msgs_humble::msg::Collectpoint::_zonename_type arg)
  {
    msg_.zonename = std::move(arg);
    return Init_Collectpoint_index(msg_);
  }

private:
  ::common_msgs_humble::msg::Collectpoint msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Collectpoint>()
{
  return common_msgs_humble::msg::builder::Init_Collectpoint_zonename();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__COLLECTPOINT__BUILDER_HPP_
