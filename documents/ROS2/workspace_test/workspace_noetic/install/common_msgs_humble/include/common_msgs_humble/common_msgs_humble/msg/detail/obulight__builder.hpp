// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Obulight.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__OBULIGHT__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__OBULIGHT__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/obulight__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Obulight_lane_speed_upper
{
public:
  explicit Init_Obulight_lane_speed_upper(::common_msgs_humble::msg::Obulight & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Obulight lane_speed_upper(::common_msgs_humble::msg::Obulight::_lane_speed_upper_type arg)
  {
    msg_.lane_speed_upper = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Obulight msg_;
};

class Init_Obulight_lane_speed_lower
{
public:
  explicit Init_Obulight_lane_speed_lower(::common_msgs_humble::msg::Obulight & msg)
  : msg_(msg)
  {}
  Init_Obulight_lane_speed_upper lane_speed_lower(::common_msgs_humble::msg::Obulight::_lane_speed_lower_type arg)
  {
    msg_.lane_speed_lower = std::move(arg);
    return Init_Obulight_lane_speed_upper(msg_);
  }

private:
  ::common_msgs_humble::msg::Obulight msg_;
};

class Init_Obulight_next_start_time
{
public:
  explicit Init_Obulight_next_start_time(::common_msgs_humble::msg::Obulight & msg)
  : msg_(msg)
  {}
  Init_Obulight_lane_speed_lower next_start_time(::common_msgs_humble::msg::Obulight::_next_start_time_type arg)
  {
    msg_.next_start_time = std::move(arg);
    return Init_Obulight_lane_speed_lower(msg_);
  }

private:
  ::common_msgs_humble::msg::Obulight msg_;
};

class Init_Obulight_end_time
{
public:
  explicit Init_Obulight_end_time(::common_msgs_humble::msg::Obulight & msg)
  : msg_(msg)
  {}
  Init_Obulight_next_start_time end_time(::common_msgs_humble::msg::Obulight::_end_time_type arg)
  {
    msg_.end_time = std::move(arg);
    return Init_Obulight_next_start_time(msg_);
  }

private:
  ::common_msgs_humble::msg::Obulight msg_;
};

class Init_Obulight_start_time
{
public:
  explicit Init_Obulight_start_time(::common_msgs_humble::msg::Obulight & msg)
  : msg_(msg)
  {}
  Init_Obulight_end_time start_time(::common_msgs_humble::msg::Obulight::_start_time_type arg)
  {
    msg_.start_time = std::move(arg);
    return Init_Obulight_end_time(msg_);
  }

private:
  ::common_msgs_humble::msg::Obulight msg_;
};

class Init_Obulight_status
{
public:
  explicit Init_Obulight_status(::common_msgs_humble::msg::Obulight & msg)
  : msg_(msg)
  {}
  Init_Obulight_start_time status(::common_msgs_humble::msg::Obulight::_status_type arg)
  {
    msg_.status = std::move(arg);
    return Init_Obulight_start_time(msg_);
  }

private:
  ::common_msgs_humble::msg::Obulight msg_;
};

class Init_Obulight_phase_id
{
public:
  Init_Obulight_phase_id()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Obulight_status phase_id(::common_msgs_humble::msg::Obulight::_phase_id_type arg)
  {
    msg_.phase_id = std::move(arg);
    return Init_Obulight_status(msg_);
  }

private:
  ::common_msgs_humble::msg::Obulight msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Obulight>()
{
  return common_msgs_humble::msg::builder::Init_Obulight_phase_id();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__OBULIGHT__BUILDER_HPP_
