// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Objectprediction.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__OBJECTPREDICTION__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__OBJECTPREDICTION__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Objectprediction __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Objectprediction __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Objectprediction_
{
  using Type = Objectprediction_<ContainerAllocator>;

  explicit Objectprediction_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->timestep = 0.0f;
      this->x = 0.0f;
      this->y = 0.0f;
      this->z = 0.0f;
      this->longtitude = 0.0;
      this->latitude = 0.0;
      this->altitude = 0.0;
      this->rollrad = 0.0f;
      this->pitchrad = 0.0f;
      this->azimuth = 0.0f;
      this->relavx = 0.0f;
      this->relavy = 0.0f;
      this->absvx = 0.0f;
      this->absvy = 0.0f;
      this->heading = 0.0f;
      this->s = 0.0f;
      this->l = 0.0f;
      this->speeds = 0.0f;
      this->speedl = 0.0f;
    }
  }

  explicit Objectprediction_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->timestep = 0.0f;
      this->x = 0.0f;
      this->y = 0.0f;
      this->z = 0.0f;
      this->longtitude = 0.0;
      this->latitude = 0.0;
      this->altitude = 0.0;
      this->rollrad = 0.0f;
      this->pitchrad = 0.0f;
      this->azimuth = 0.0f;
      this->relavx = 0.0f;
      this->relavy = 0.0f;
      this->absvx = 0.0f;
      this->absvy = 0.0f;
      this->heading = 0.0f;
      this->s = 0.0f;
      this->l = 0.0f;
      this->speeds = 0.0f;
      this->speedl = 0.0f;
    }
  }

  // field types and members
  using _timestep_type =
    float;
  _timestep_type timestep;
  using _x_type =
    float;
  _x_type x;
  using _y_type =
    float;
  _y_type y;
  using _z_type =
    float;
  _z_type z;
  using _longtitude_type =
    double;
  _longtitude_type longtitude;
  using _latitude_type =
    double;
  _latitude_type latitude;
  using _altitude_type =
    double;
  _altitude_type altitude;
  using _rollrad_type =
    float;
  _rollrad_type rollrad;
  using _pitchrad_type =
    float;
  _pitchrad_type pitchrad;
  using _azimuth_type =
    float;
  _azimuth_type azimuth;
  using _relavx_type =
    float;
  _relavx_type relavx;
  using _relavy_type =
    float;
  _relavy_type relavy;
  using _absvx_type =
    float;
  _absvx_type absvx;
  using _absvy_type =
    float;
  _absvy_type absvy;
  using _heading_type =
    float;
  _heading_type heading;
  using _s_type =
    float;
  _s_type s;
  using _l_type =
    float;
  _l_type l;
  using _speeds_type =
    float;
  _speeds_type speeds;
  using _speedl_type =
    float;
  _speedl_type speedl;

  // setters for named parameter idiom
  Type & set__timestep(
    const float & _arg)
  {
    this->timestep = _arg;
    return *this;
  }
  Type & set__x(
    const float & _arg)
  {
    this->x = _arg;
    return *this;
  }
  Type & set__y(
    const float & _arg)
  {
    this->y = _arg;
    return *this;
  }
  Type & set__z(
    const float & _arg)
  {
    this->z = _arg;
    return *this;
  }
  Type & set__longtitude(
    const double & _arg)
  {
    this->longtitude = _arg;
    return *this;
  }
  Type & set__latitude(
    const double & _arg)
  {
    this->latitude = _arg;
    return *this;
  }
  Type & set__altitude(
    const double & _arg)
  {
    this->altitude = _arg;
    return *this;
  }
  Type & set__rollrad(
    const float & _arg)
  {
    this->rollrad = _arg;
    return *this;
  }
  Type & set__pitchrad(
    const float & _arg)
  {
    this->pitchrad = _arg;
    return *this;
  }
  Type & set__azimuth(
    const float & _arg)
  {
    this->azimuth = _arg;
    return *this;
  }
  Type & set__relavx(
    const float & _arg)
  {
    this->relavx = _arg;
    return *this;
  }
  Type & set__relavy(
    const float & _arg)
  {
    this->relavy = _arg;
    return *this;
  }
  Type & set__absvx(
    const float & _arg)
  {
    this->absvx = _arg;
    return *this;
  }
  Type & set__absvy(
    const float & _arg)
  {
    this->absvy = _arg;
    return *this;
  }
  Type & set__heading(
    const float & _arg)
  {
    this->heading = _arg;
    return *this;
  }
  Type & set__s(
    const float & _arg)
  {
    this->s = _arg;
    return *this;
  }
  Type & set__l(
    const float & _arg)
  {
    this->l = _arg;
    return *this;
  }
  Type & set__speeds(
    const float & _arg)
  {
    this->speeds = _arg;
    return *this;
  }
  Type & set__speedl(
    const float & _arg)
  {
    this->speedl = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Objectprediction_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Objectprediction_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Objectprediction_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Objectprediction_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Objectprediction_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Objectprediction_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Objectprediction_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Objectprediction_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Objectprediction_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Objectprediction_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Objectprediction
    std::shared_ptr<common_msgs_humble::msg::Objectprediction_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Objectprediction
    std::shared_ptr<common_msgs_humble::msg::Objectprediction_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Objectprediction_ & other) const
  {
    if (this->timestep != other.timestep) {
      return false;
    }
    if (this->x != other.x) {
      return false;
    }
    if (this->y != other.y) {
      return false;
    }
    if (this->z != other.z) {
      return false;
    }
    if (this->longtitude != other.longtitude) {
      return false;
    }
    if (this->latitude != other.latitude) {
      return false;
    }
    if (this->altitude != other.altitude) {
      return false;
    }
    if (this->rollrad != other.rollrad) {
      return false;
    }
    if (this->pitchrad != other.pitchrad) {
      return false;
    }
    if (this->azimuth != other.azimuth) {
      return false;
    }
    if (this->relavx != other.relavx) {
      return false;
    }
    if (this->relavy != other.relavy) {
      return false;
    }
    if (this->absvx != other.absvx) {
      return false;
    }
    if (this->absvy != other.absvy) {
      return false;
    }
    if (this->heading != other.heading) {
      return false;
    }
    if (this->s != other.s) {
      return false;
    }
    if (this->l != other.l) {
      return false;
    }
    if (this->speeds != other.speeds) {
      return false;
    }
    if (this->speedl != other.speedl) {
      return false;
    }
    return true;
  }
  bool operator!=(const Objectprediction_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Objectprediction_

// alias to use template instance with default allocator
using Objectprediction =
  common_msgs_humble::msg::Objectprediction_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__OBJECTPREDICTION__STRUCT_HPP_
