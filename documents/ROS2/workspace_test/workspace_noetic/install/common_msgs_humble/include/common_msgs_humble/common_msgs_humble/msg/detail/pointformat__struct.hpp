// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Pointformat.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__POINTFORMAT__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__POINTFORMAT__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Pointformat __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Pointformat __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Pointformat_
{
  using Type = Pointformat_<ContainerAllocator>;

  explicit Pointformat_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->lon = 0.0;
      this->lat = 0.0;
      this->heading = 0.0f;
      this->index = 0;
      this->backup1 = 0;
      this->backup2 = 0;
      this->backup3 = 0;
      this->backup4 = 0.0f;
      this->backup5 = 0.0f;
      this->backup6 = 0.0f;
      this->backup7 = 0.0;
      this->backup8 = 0.0;
      this->backup9 = 0.0;
      this->path = "";
    }
  }

  explicit Pointformat_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : path(_alloc)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->lon = 0.0;
      this->lat = 0.0;
      this->heading = 0.0f;
      this->index = 0;
      this->backup1 = 0;
      this->backup2 = 0;
      this->backup3 = 0;
      this->backup4 = 0.0f;
      this->backup5 = 0.0f;
      this->backup6 = 0.0f;
      this->backup7 = 0.0;
      this->backup8 = 0.0;
      this->backup9 = 0.0;
      this->path = "";
    }
  }

  // field types and members
  using _lon_type =
    double;
  _lon_type lon;
  using _lat_type =
    double;
  _lat_type lat;
  using _heading_type =
    float;
  _heading_type heading;
  using _index_type =
    uint8_t;
  _index_type index;
  using _backup1_type =
    uint8_t;
  _backup1_type backup1;
  using _backup2_type =
    uint8_t;
  _backup2_type backup2;
  using _backup3_type =
    uint8_t;
  _backup3_type backup3;
  using _backup4_type =
    float;
  _backup4_type backup4;
  using _backup5_type =
    float;
  _backup5_type backup5;
  using _backup6_type =
    float;
  _backup6_type backup6;
  using _backup7_type =
    double;
  _backup7_type backup7;
  using _backup8_type =
    double;
  _backup8_type backup8;
  using _backup9_type =
    double;
  _backup9_type backup9;
  using _path_type =
    std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>;
  _path_type path;

  // setters for named parameter idiom
  Type & set__lon(
    const double & _arg)
  {
    this->lon = _arg;
    return *this;
  }
  Type & set__lat(
    const double & _arg)
  {
    this->lat = _arg;
    return *this;
  }
  Type & set__heading(
    const float & _arg)
  {
    this->heading = _arg;
    return *this;
  }
  Type & set__index(
    const uint8_t & _arg)
  {
    this->index = _arg;
    return *this;
  }
  Type & set__backup1(
    const uint8_t & _arg)
  {
    this->backup1 = _arg;
    return *this;
  }
  Type & set__backup2(
    const uint8_t & _arg)
  {
    this->backup2 = _arg;
    return *this;
  }
  Type & set__backup3(
    const uint8_t & _arg)
  {
    this->backup3 = _arg;
    return *this;
  }
  Type & set__backup4(
    const float & _arg)
  {
    this->backup4 = _arg;
    return *this;
  }
  Type & set__backup5(
    const float & _arg)
  {
    this->backup5 = _arg;
    return *this;
  }
  Type & set__backup6(
    const float & _arg)
  {
    this->backup6 = _arg;
    return *this;
  }
  Type & set__backup7(
    const double & _arg)
  {
    this->backup7 = _arg;
    return *this;
  }
  Type & set__backup8(
    const double & _arg)
  {
    this->backup8 = _arg;
    return *this;
  }
  Type & set__backup9(
    const double & _arg)
  {
    this->backup9 = _arg;
    return *this;
  }
  Type & set__path(
    const std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> & _arg)
  {
    this->path = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Pointformat_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Pointformat_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Pointformat_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Pointformat_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Pointformat_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Pointformat_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Pointformat_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Pointformat_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Pointformat_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Pointformat_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Pointformat
    std::shared_ptr<common_msgs_humble::msg::Pointformat_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Pointformat
    std::shared_ptr<common_msgs_humble::msg::Pointformat_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Pointformat_ & other) const
  {
    if (this->lon != other.lon) {
      return false;
    }
    if (this->lat != other.lat) {
      return false;
    }
    if (this->heading != other.heading) {
      return false;
    }
    if (this->index != other.index) {
      return false;
    }
    if (this->backup1 != other.backup1) {
      return false;
    }
    if (this->backup2 != other.backup2) {
      return false;
    }
    if (this->backup3 != other.backup3) {
      return false;
    }
    if (this->backup4 != other.backup4) {
      return false;
    }
    if (this->backup5 != other.backup5) {
      return false;
    }
    if (this->backup6 != other.backup6) {
      return false;
    }
    if (this->backup7 != other.backup7) {
      return false;
    }
    if (this->backup8 != other.backup8) {
      return false;
    }
    if (this->backup9 != other.backup9) {
      return false;
    }
    if (this->path != other.path) {
      return false;
    }
    return true;
  }
  bool operator!=(const Pointformat_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Pointformat_

// alias to use template instance with default allocator
using Pointformat =
  common_msgs_humble::msg::Pointformat_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__POINTFORMAT__STRUCT_HPP_
