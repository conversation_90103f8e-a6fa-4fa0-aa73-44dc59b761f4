﻿// NOLINT: This file starts with a BOM since it contain non-ASCII characters
// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Controllat.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__CONTROLLAT__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__CONTROLLAT__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

/// Struct defined in msg/Controllat in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Controllat
{
  /// 转角使能
  uint8_t epsmethod;
  /// 目标角度
  int16_t epsangle;
  /// 限速
  float limitspeed;
  /// 转角速度
  int16_t epstorque;
  /// 灯光使能
  uint8_t lightmethod;
  /// 目标灯光
  uint8_t lights;
  /// 有效位
  uint8_t isvalid;
  /// 偏移距离
  int16_t deviation;
  /// 时间戳
  int64_t timestamp;
  uint8_t apavstatus;
  uint8_t apsstate;
  float curve;
} common_msgs_humble__msg__Controllat;

// Struct for a sequence of common_msgs_humble__msg__Controllat.
typedef struct common_msgs_humble__msg__Controllat__Sequence
{
  common_msgs_humble__msg__Controllat * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Controllat__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__CONTROLLAT__STRUCT_H_
