// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Roadpoint.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__ROADPOINT__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__ROADPOINT__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Roadpoint __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Roadpoint __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Roadpoint_
{
  using Type = Roadpoint_<ContainerAllocator>;

  explicit Roadpoint_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->x = 0.0f;
      this->y = 0.0f;
      this->gx = 0.0;
      this->gy = 0.0;
      this->roadtype = 0;
      this->speed = 0.0f;
      this->a = 0.0f;
      this->jerk = 0.0f;
      this->lanetype = 0;
      this->turnlight = 0;
      this->mergelanetype = 0;
      this->sensorlanetype = 0;
      this->heading = 0.0f;
      this->curvature = 0.0f;
      this->dkappa = 0.0f;
      this->ddkappa = 0.0f;
      this->leftsearchdis = 0.0f;
      this->rightsearchdis = 0.0f;
      this->s = 0.0;
      this->sideroadwidth = 0.0f;
      this->lanewidth = 0.0f;
      this->leftlanewidth = 0.0f;
      this->rightlanewidth = 0.0f;
      this->relativetime = 0.0f;
      this->laneswitch = 0;
      this->laneborrow = 0;
      this->lanenum = 0;
      this->lanesite = 0;
    }
  }

  explicit Roadpoint_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->x = 0.0f;
      this->y = 0.0f;
      this->gx = 0.0;
      this->gy = 0.0;
      this->roadtype = 0;
      this->speed = 0.0f;
      this->a = 0.0f;
      this->jerk = 0.0f;
      this->lanetype = 0;
      this->turnlight = 0;
      this->mergelanetype = 0;
      this->sensorlanetype = 0;
      this->heading = 0.0f;
      this->curvature = 0.0f;
      this->dkappa = 0.0f;
      this->ddkappa = 0.0f;
      this->leftsearchdis = 0.0f;
      this->rightsearchdis = 0.0f;
      this->s = 0.0;
      this->sideroadwidth = 0.0f;
      this->lanewidth = 0.0f;
      this->leftlanewidth = 0.0f;
      this->rightlanewidth = 0.0f;
      this->relativetime = 0.0f;
      this->laneswitch = 0;
      this->laneborrow = 0;
      this->lanenum = 0;
      this->lanesite = 0;
    }
  }

  // field types and members
  using _x_type =
    float;
  _x_type x;
  using _y_type =
    float;
  _y_type y;
  using _gx_type =
    double;
  _gx_type gx;
  using _gy_type =
    double;
  _gy_type gy;
  using _roadtype_type =
    uint8_t;
  _roadtype_type roadtype;
  using _speed_type =
    float;
  _speed_type speed;
  using _a_type =
    float;
  _a_type a;
  using _jerk_type =
    float;
  _jerk_type jerk;
  using _lanetype_type =
    uint8_t;
  _lanetype_type lanetype;
  using _turnlight_type =
    uint8_t;
  _turnlight_type turnlight;
  using _mergelanetype_type =
    uint8_t;
  _mergelanetype_type mergelanetype;
  using _sensorlanetype_type =
    uint8_t;
  _sensorlanetype_type sensorlanetype;
  using _heading_type =
    float;
  _heading_type heading;
  using _curvature_type =
    float;
  _curvature_type curvature;
  using _dkappa_type =
    float;
  _dkappa_type dkappa;
  using _ddkappa_type =
    float;
  _ddkappa_type ddkappa;
  using _leftsearchdis_type =
    float;
  _leftsearchdis_type leftsearchdis;
  using _rightsearchdis_type =
    float;
  _rightsearchdis_type rightsearchdis;
  using _s_type =
    double;
  _s_type s;
  using _sideroadwidth_type =
    float;
  _sideroadwidth_type sideroadwidth;
  using _lanewidth_type =
    float;
  _lanewidth_type lanewidth;
  using _leftlanewidth_type =
    float;
  _leftlanewidth_type leftlanewidth;
  using _rightlanewidth_type =
    float;
  _rightlanewidth_type rightlanewidth;
  using _relativetime_type =
    float;
  _relativetime_type relativetime;
  using _laneswitch_type =
    uint8_t;
  _laneswitch_type laneswitch;
  using _laneborrow_type =
    uint8_t;
  _laneborrow_type laneborrow;
  using _lanenum_type =
    uint8_t;
  _lanenum_type lanenum;
  using _lanesite_type =
    uint8_t;
  _lanesite_type lanesite;

  // setters for named parameter idiom
  Type & set__x(
    const float & _arg)
  {
    this->x = _arg;
    return *this;
  }
  Type & set__y(
    const float & _arg)
  {
    this->y = _arg;
    return *this;
  }
  Type & set__gx(
    const double & _arg)
  {
    this->gx = _arg;
    return *this;
  }
  Type & set__gy(
    const double & _arg)
  {
    this->gy = _arg;
    return *this;
  }
  Type & set__roadtype(
    const uint8_t & _arg)
  {
    this->roadtype = _arg;
    return *this;
  }
  Type & set__speed(
    const float & _arg)
  {
    this->speed = _arg;
    return *this;
  }
  Type & set__a(
    const float & _arg)
  {
    this->a = _arg;
    return *this;
  }
  Type & set__jerk(
    const float & _arg)
  {
    this->jerk = _arg;
    return *this;
  }
  Type & set__lanetype(
    const uint8_t & _arg)
  {
    this->lanetype = _arg;
    return *this;
  }
  Type & set__turnlight(
    const uint8_t & _arg)
  {
    this->turnlight = _arg;
    return *this;
  }
  Type & set__mergelanetype(
    const uint8_t & _arg)
  {
    this->mergelanetype = _arg;
    return *this;
  }
  Type & set__sensorlanetype(
    const uint8_t & _arg)
  {
    this->sensorlanetype = _arg;
    return *this;
  }
  Type & set__heading(
    const float & _arg)
  {
    this->heading = _arg;
    return *this;
  }
  Type & set__curvature(
    const float & _arg)
  {
    this->curvature = _arg;
    return *this;
  }
  Type & set__dkappa(
    const float & _arg)
  {
    this->dkappa = _arg;
    return *this;
  }
  Type & set__ddkappa(
    const float & _arg)
  {
    this->ddkappa = _arg;
    return *this;
  }
  Type & set__leftsearchdis(
    const float & _arg)
  {
    this->leftsearchdis = _arg;
    return *this;
  }
  Type & set__rightsearchdis(
    const float & _arg)
  {
    this->rightsearchdis = _arg;
    return *this;
  }
  Type & set__s(
    const double & _arg)
  {
    this->s = _arg;
    return *this;
  }
  Type & set__sideroadwidth(
    const float & _arg)
  {
    this->sideroadwidth = _arg;
    return *this;
  }
  Type & set__lanewidth(
    const float & _arg)
  {
    this->lanewidth = _arg;
    return *this;
  }
  Type & set__leftlanewidth(
    const float & _arg)
  {
    this->leftlanewidth = _arg;
    return *this;
  }
  Type & set__rightlanewidth(
    const float & _arg)
  {
    this->rightlanewidth = _arg;
    return *this;
  }
  Type & set__relativetime(
    const float & _arg)
  {
    this->relativetime = _arg;
    return *this;
  }
  Type & set__laneswitch(
    const uint8_t & _arg)
  {
    this->laneswitch = _arg;
    return *this;
  }
  Type & set__laneborrow(
    const uint8_t & _arg)
  {
    this->laneborrow = _arg;
    return *this;
  }
  Type & set__lanenum(
    const uint8_t & _arg)
  {
    this->lanenum = _arg;
    return *this;
  }
  Type & set__lanesite(
    const uint8_t & _arg)
  {
    this->lanesite = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Roadpoint_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Roadpoint_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Roadpoint_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Roadpoint_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Roadpoint_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Roadpoint_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Roadpoint_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Roadpoint_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Roadpoint_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Roadpoint_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Roadpoint
    std::shared_ptr<common_msgs_humble::msg::Roadpoint_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Roadpoint
    std::shared_ptr<common_msgs_humble::msg::Roadpoint_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Roadpoint_ & other) const
  {
    if (this->x != other.x) {
      return false;
    }
    if (this->y != other.y) {
      return false;
    }
    if (this->gx != other.gx) {
      return false;
    }
    if (this->gy != other.gy) {
      return false;
    }
    if (this->roadtype != other.roadtype) {
      return false;
    }
    if (this->speed != other.speed) {
      return false;
    }
    if (this->a != other.a) {
      return false;
    }
    if (this->jerk != other.jerk) {
      return false;
    }
    if (this->lanetype != other.lanetype) {
      return false;
    }
    if (this->turnlight != other.turnlight) {
      return false;
    }
    if (this->mergelanetype != other.mergelanetype) {
      return false;
    }
    if (this->sensorlanetype != other.sensorlanetype) {
      return false;
    }
    if (this->heading != other.heading) {
      return false;
    }
    if (this->curvature != other.curvature) {
      return false;
    }
    if (this->dkappa != other.dkappa) {
      return false;
    }
    if (this->ddkappa != other.ddkappa) {
      return false;
    }
    if (this->leftsearchdis != other.leftsearchdis) {
      return false;
    }
    if (this->rightsearchdis != other.rightsearchdis) {
      return false;
    }
    if (this->s != other.s) {
      return false;
    }
    if (this->sideroadwidth != other.sideroadwidth) {
      return false;
    }
    if (this->lanewidth != other.lanewidth) {
      return false;
    }
    if (this->leftlanewidth != other.leftlanewidth) {
      return false;
    }
    if (this->rightlanewidth != other.rightlanewidth) {
      return false;
    }
    if (this->relativetime != other.relativetime) {
      return false;
    }
    if (this->laneswitch != other.laneswitch) {
      return false;
    }
    if (this->laneborrow != other.laneborrow) {
      return false;
    }
    if (this->lanenum != other.lanenum) {
      return false;
    }
    if (this->lanesite != other.lanesite) {
      return false;
    }
    return true;
  }
  bool operator!=(const Roadpoint_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Roadpoint_

// alias to use template instance with default allocator
using Roadpoint =
  common_msgs_humble::msg::Roadpoint_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__ROADPOINT__STRUCT_HPP_
