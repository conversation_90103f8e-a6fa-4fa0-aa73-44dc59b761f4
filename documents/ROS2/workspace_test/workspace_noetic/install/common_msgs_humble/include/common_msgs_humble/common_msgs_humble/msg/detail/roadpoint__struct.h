﻿// NOLINT: This file starts with a BOM since it contain non-ASCII characters
// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Roadpoint.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__ROADPOINT__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__ROADPOINT__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

/// Struct defined in msg/Roadpoint in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Roadpoint
{
  /// 局部横坐标x,cm
  float x;
  /// 局部纵坐标y,cm
  float y;
  /// 经度
  double gx;
  /// 纬度
  double gy;
  /// 道路属性
  uint8_t roadtype;
  /// 速度       m/s
  float speed;
  /// 加速度     m/s^2
  float a;
  /// 冲击度 nan     m/s^3
  float jerk;
  /// 车道属性
  uint8_t lanetype;
  /// 转向灯
  uint8_t turnlight;
  /// 并道属性
  uint8_t mergelanetype;
  /// nan
  uint8_t sensorlanetype;
  /// 航向角   deg
  float heading;
  /// 道路曲率
  float curvature;
  /// 曲率的一阶导数 nan
  float dkappa;
  /// 曲率的二阶导数 nan
  float ddkappa;
  /// nan
  float leftsearchdis;
  /// nan
  float rightsearchdis;
  /// frenet坐标下s
  double s;
  /// 到车道边界距离
  float sideroadwidth;
  /// 车道宽
  float lanewidth;
  /// 左车道宽
  float leftlanewidth;
  /// 右车道宽
  float rightlanewidth;
  /// nan
  float relativetime;
  /// 换道标志位,0-canot/1-both/2-left/3-right
  uint8_t laneswitch;
  /// 借道标志位,0-canot/1-both/2-left/3-right
  uint8_t laneborrow;
  /// 车道总数
  uint8_t lanenum;
  /// 所在车道数
  uint8_t lanesite;
} common_msgs_humble__msg__Roadpoint;

// Struct for a sequence of common_msgs_humble__msg__Roadpoint.
typedef struct common_msgs_humble__msg__Roadpoint__Sequence
{
  common_msgs_humble__msg__Roadpoint * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Roadpoint__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__ROADPOINT__STRUCT_H_
