// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Mapformat.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__MAPFORMAT__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__MAPFORMAT__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Mapformat __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Mapformat __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Mapformat_
{
  using Type = Mapformat_<ContainerAllocator>;

  explicit Mapformat_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->lon = 0.0;
      this->lat = 0.0;
      this->roadtype = 0;
      this->speed = 0.0f;
      this->lanetype = 0;
      this->mergelanetype = 0;
      this->sensorlanetype = 0;
      this->turnlight = 0;
      this->sideroadwidth = 0.0f;
      this->lanewidth = 0.0f;
      this->leftlanewidth = 0.0f;
      this->rightlanewidth = 0.0f;
      this->leftsearchdis = 0.0f;
      this->rightsearchdis = 0.0f;
      this->heading = 0.0f;
      this->curvature = 0.0f;
      this->gpstime = 0.0;
      this->switchflag = 0;
      this->borrowflag = 0;
      this->lanesum = 0;
      this->lanenum = 0;
      this->backup1 = 0;
      this->backup2 = 0;
      this->backup3 = 0;
      this->backup4 = 0.0f;
      this->backup5 = 0.0f;
      this->backup6 = 0.0f;
      this->backup7 = 0.0;
      this->backup8 = 0.0;
      this->backup9 = 0.0;
    }
  }

  explicit Mapformat_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->lon = 0.0;
      this->lat = 0.0;
      this->roadtype = 0;
      this->speed = 0.0f;
      this->lanetype = 0;
      this->mergelanetype = 0;
      this->sensorlanetype = 0;
      this->turnlight = 0;
      this->sideroadwidth = 0.0f;
      this->lanewidth = 0.0f;
      this->leftlanewidth = 0.0f;
      this->rightlanewidth = 0.0f;
      this->leftsearchdis = 0.0f;
      this->rightsearchdis = 0.0f;
      this->heading = 0.0f;
      this->curvature = 0.0f;
      this->gpstime = 0.0;
      this->switchflag = 0;
      this->borrowflag = 0;
      this->lanesum = 0;
      this->lanenum = 0;
      this->backup1 = 0;
      this->backup2 = 0;
      this->backup3 = 0;
      this->backup4 = 0.0f;
      this->backup5 = 0.0f;
      this->backup6 = 0.0f;
      this->backup7 = 0.0;
      this->backup8 = 0.0;
      this->backup9 = 0.0;
    }
  }

  // field types and members
  using _lon_type =
    double;
  _lon_type lon;
  using _lat_type =
    double;
  _lat_type lat;
  using _roadtype_type =
    uint8_t;
  _roadtype_type roadtype;
  using _speed_type =
    float;
  _speed_type speed;
  using _lanetype_type =
    uint8_t;
  _lanetype_type lanetype;
  using _mergelanetype_type =
    uint8_t;
  _mergelanetype_type mergelanetype;
  using _sensorlanetype_type =
    uint8_t;
  _sensorlanetype_type sensorlanetype;
  using _turnlight_type =
    uint8_t;
  _turnlight_type turnlight;
  using _sideroadwidth_type =
    float;
  _sideroadwidth_type sideroadwidth;
  using _lanewidth_type =
    float;
  _lanewidth_type lanewidth;
  using _leftlanewidth_type =
    float;
  _leftlanewidth_type leftlanewidth;
  using _rightlanewidth_type =
    float;
  _rightlanewidth_type rightlanewidth;
  using _leftsearchdis_type =
    float;
  _leftsearchdis_type leftsearchdis;
  using _rightsearchdis_type =
    float;
  _rightsearchdis_type rightsearchdis;
  using _heading_type =
    float;
  _heading_type heading;
  using _curvature_type =
    float;
  _curvature_type curvature;
  using _gpstime_type =
    double;
  _gpstime_type gpstime;
  using _switchflag_type =
    uint8_t;
  _switchflag_type switchflag;
  using _borrowflag_type =
    uint8_t;
  _borrowflag_type borrowflag;
  using _lanesum_type =
    uint8_t;
  _lanesum_type lanesum;
  using _lanenum_type =
    uint8_t;
  _lanenum_type lanenum;
  using _backup1_type =
    uint8_t;
  _backup1_type backup1;
  using _backup2_type =
    uint8_t;
  _backup2_type backup2;
  using _backup3_type =
    uint8_t;
  _backup3_type backup3;
  using _backup4_type =
    float;
  _backup4_type backup4;
  using _backup5_type =
    float;
  _backup5_type backup5;
  using _backup6_type =
    float;
  _backup6_type backup6;
  using _backup7_type =
    double;
  _backup7_type backup7;
  using _backup8_type =
    double;
  _backup8_type backup8;
  using _backup9_type =
    double;
  _backup9_type backup9;

  // setters for named parameter idiom
  Type & set__lon(
    const double & _arg)
  {
    this->lon = _arg;
    return *this;
  }
  Type & set__lat(
    const double & _arg)
  {
    this->lat = _arg;
    return *this;
  }
  Type & set__roadtype(
    const uint8_t & _arg)
  {
    this->roadtype = _arg;
    return *this;
  }
  Type & set__speed(
    const float & _arg)
  {
    this->speed = _arg;
    return *this;
  }
  Type & set__lanetype(
    const uint8_t & _arg)
  {
    this->lanetype = _arg;
    return *this;
  }
  Type & set__mergelanetype(
    const uint8_t & _arg)
  {
    this->mergelanetype = _arg;
    return *this;
  }
  Type & set__sensorlanetype(
    const uint8_t & _arg)
  {
    this->sensorlanetype = _arg;
    return *this;
  }
  Type & set__turnlight(
    const uint8_t & _arg)
  {
    this->turnlight = _arg;
    return *this;
  }
  Type & set__sideroadwidth(
    const float & _arg)
  {
    this->sideroadwidth = _arg;
    return *this;
  }
  Type & set__lanewidth(
    const float & _arg)
  {
    this->lanewidth = _arg;
    return *this;
  }
  Type & set__leftlanewidth(
    const float & _arg)
  {
    this->leftlanewidth = _arg;
    return *this;
  }
  Type & set__rightlanewidth(
    const float & _arg)
  {
    this->rightlanewidth = _arg;
    return *this;
  }
  Type & set__leftsearchdis(
    const float & _arg)
  {
    this->leftsearchdis = _arg;
    return *this;
  }
  Type & set__rightsearchdis(
    const float & _arg)
  {
    this->rightsearchdis = _arg;
    return *this;
  }
  Type & set__heading(
    const float & _arg)
  {
    this->heading = _arg;
    return *this;
  }
  Type & set__curvature(
    const float & _arg)
  {
    this->curvature = _arg;
    return *this;
  }
  Type & set__gpstime(
    const double & _arg)
  {
    this->gpstime = _arg;
    return *this;
  }
  Type & set__switchflag(
    const uint8_t & _arg)
  {
    this->switchflag = _arg;
    return *this;
  }
  Type & set__borrowflag(
    const uint8_t & _arg)
  {
    this->borrowflag = _arg;
    return *this;
  }
  Type & set__lanesum(
    const uint8_t & _arg)
  {
    this->lanesum = _arg;
    return *this;
  }
  Type & set__lanenum(
    const uint8_t & _arg)
  {
    this->lanenum = _arg;
    return *this;
  }
  Type & set__backup1(
    const uint8_t & _arg)
  {
    this->backup1 = _arg;
    return *this;
  }
  Type & set__backup2(
    const uint8_t & _arg)
  {
    this->backup2 = _arg;
    return *this;
  }
  Type & set__backup3(
    const uint8_t & _arg)
  {
    this->backup3 = _arg;
    return *this;
  }
  Type & set__backup4(
    const float & _arg)
  {
    this->backup4 = _arg;
    return *this;
  }
  Type & set__backup5(
    const float & _arg)
  {
    this->backup5 = _arg;
    return *this;
  }
  Type & set__backup6(
    const float & _arg)
  {
    this->backup6 = _arg;
    return *this;
  }
  Type & set__backup7(
    const double & _arg)
  {
    this->backup7 = _arg;
    return *this;
  }
  Type & set__backup8(
    const double & _arg)
  {
    this->backup8 = _arg;
    return *this;
  }
  Type & set__backup9(
    const double & _arg)
  {
    this->backup9 = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Mapformat_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Mapformat_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Mapformat_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Mapformat_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Mapformat_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Mapformat_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Mapformat_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Mapformat_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Mapformat_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Mapformat_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Mapformat
    std::shared_ptr<common_msgs_humble::msg::Mapformat_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Mapformat
    std::shared_ptr<common_msgs_humble::msg::Mapformat_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Mapformat_ & other) const
  {
    if (this->lon != other.lon) {
      return false;
    }
    if (this->lat != other.lat) {
      return false;
    }
    if (this->roadtype != other.roadtype) {
      return false;
    }
    if (this->speed != other.speed) {
      return false;
    }
    if (this->lanetype != other.lanetype) {
      return false;
    }
    if (this->mergelanetype != other.mergelanetype) {
      return false;
    }
    if (this->sensorlanetype != other.sensorlanetype) {
      return false;
    }
    if (this->turnlight != other.turnlight) {
      return false;
    }
    if (this->sideroadwidth != other.sideroadwidth) {
      return false;
    }
    if (this->lanewidth != other.lanewidth) {
      return false;
    }
    if (this->leftlanewidth != other.leftlanewidth) {
      return false;
    }
    if (this->rightlanewidth != other.rightlanewidth) {
      return false;
    }
    if (this->leftsearchdis != other.leftsearchdis) {
      return false;
    }
    if (this->rightsearchdis != other.rightsearchdis) {
      return false;
    }
    if (this->heading != other.heading) {
      return false;
    }
    if (this->curvature != other.curvature) {
      return false;
    }
    if (this->gpstime != other.gpstime) {
      return false;
    }
    if (this->switchflag != other.switchflag) {
      return false;
    }
    if (this->borrowflag != other.borrowflag) {
      return false;
    }
    if (this->lanesum != other.lanesum) {
      return false;
    }
    if (this->lanenum != other.lanenum) {
      return false;
    }
    if (this->backup1 != other.backup1) {
      return false;
    }
    if (this->backup2 != other.backup2) {
      return false;
    }
    if (this->backup3 != other.backup3) {
      return false;
    }
    if (this->backup4 != other.backup4) {
      return false;
    }
    if (this->backup5 != other.backup5) {
      return false;
    }
    if (this->backup6 != other.backup6) {
      return false;
    }
    if (this->backup7 != other.backup7) {
      return false;
    }
    if (this->backup8 != other.backup8) {
      return false;
    }
    if (this->backup9 != other.backup9) {
      return false;
    }
    return true;
  }
  bool operator!=(const Mapformat_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Mapformat_

// alias to use template instance with default allocator
using Mapformat =
  common_msgs_humble::msg::Mapformat_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__MAPFORMAT__STRUCT_HPP_
