// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Controllon.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__CONTROLLON__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__CONTROLLON__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/controllon__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Controllon_objtype
{
public:
  explicit Init_Controllon_objtype(::common_msgs_humble::msg::Controllon & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Controllon objtype(::common_msgs_humble::msg::Controllon::_objtype_type arg)
  {
    msg_.objtype = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Controllon msg_;
};

class Init_Controllon_apadis
{
public:
  explicit Init_Controllon_apadis(::common_msgs_humble::msg::Controllon & msg)
  : msg_(msg)
  {}
  Init_Controllon_objtype apadis(::common_msgs_humble::msg::Controllon::_apadis_type arg)
  {
    msg_.apadis = std::move(arg);
    return Init_Controllon_objtype(msg_);
  }

private:
  ::common_msgs_humble::msg::Controllon msg_;
};

class Init_Controllon_targetspeed
{
public:
  explicit Init_Controllon_targetspeed(::common_msgs_humble::msg::Controllon & msg)
  : msg_(msg)
  {}
  Init_Controllon_apadis targetspeed(::common_msgs_humble::msg::Controllon::_targetspeed_type arg)
  {
    msg_.targetspeed = std::move(arg);
    return Init_Controllon_apadis(msg_);
  }

private:
  ::common_msgs_humble::msg::Controllon msg_;
};

class Init_Controllon_timestamp
{
public:
  explicit Init_Controllon_timestamp(::common_msgs_humble::msg::Controllon & msg)
  : msg_(msg)
  {}
  Init_Controllon_targetspeed timestamp(::common_msgs_humble::msg::Controllon::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return Init_Controllon_targetspeed(msg_);
  }

private:
  ::common_msgs_humble::msg::Controllon msg_;
};

class Init_Controllon_isvalid
{
public:
  explicit Init_Controllon_isvalid(::common_msgs_humble::msg::Controllon & msg)
  : msg_(msg)
  {}
  Init_Controllon_timestamp isvalid(::common_msgs_humble::msg::Controllon::_isvalid_type arg)
  {
    msg_.isvalid = std::move(arg);
    return Init_Controllon_timestamp(msg_);
  }

private:
  ::common_msgs_humble::msg::Controllon msg_;
};

class Init_Controllon_mode
{
public:
  explicit Init_Controllon_mode(::common_msgs_humble::msg::Controllon & msg)
  : msg_(msg)
  {}
  Init_Controllon_isvalid mode(::common_msgs_humble::msg::Controllon::_mode_type arg)
  {
    msg_.mode = std::move(arg);
    return Init_Controllon_isvalid(msg_);
  }

private:
  ::common_msgs_humble::msg::Controllon msg_;
};

class Init_Controllon_objrel
{
public:
  explicit Init_Controllon_objrel(::common_msgs_humble::msg::Controllon & msg)
  : msg_(msg)
  {}
  Init_Controllon_mode objrel(::common_msgs_humble::msg::Controllon::_objrel_type arg)
  {
    msg_.objrel = std::move(arg);
    return Init_Controllon_mode(msg_);
  }

private:
  ::common_msgs_humble::msg::Controllon msg_;
};

class Init_Controllon_objdis
{
public:
  explicit Init_Controllon_objdis(::common_msgs_humble::msg::Controllon & msg)
  : msg_(msg)
  {}
  Init_Controllon_objrel objdis(::common_msgs_humble::msg::Controllon::_objdis_type arg)
  {
    msg_.objdis = std::move(arg);
    return Init_Controllon_objrel(msg_);
  }

private:
  ::common_msgs_humble::msg::Controllon msg_;
};

class Init_Controllon_pcmethod
{
public:
  explicit Init_Controllon_pcmethod(::common_msgs_humble::msg::Controllon & msg)
  : msg_(msg)
  {}
  Init_Controllon_objdis pcmethod(::common_msgs_humble::msg::Controllon::_pcmethod_type arg)
  {
    msg_.pcmethod = std::move(arg);
    return Init_Controllon_objdis(msg_);
  }

private:
  ::common_msgs_humble::msg::Controllon msg_;
};

class Init_Controllon_light
{
public:
  explicit Init_Controllon_light(::common_msgs_humble::msg::Controllon & msg)
  : msg_(msg)
  {}
  Init_Controllon_pcmethod light(::common_msgs_humble::msg::Controllon::_light_type arg)
  {
    msg_.light = std::move(arg);
    return Init_Controllon_pcmethod(msg_);
  }

private:
  ::common_msgs_humble::msg::Controllon msg_;
};

class Init_Controllon_station
{
public:
  explicit Init_Controllon_station(::common_msgs_humble::msg::Controllon & msg)
  : msg_(msg)
  {}
  Init_Controllon_light station(::common_msgs_humble::msg::Controllon::_station_type arg)
  {
    msg_.station = std::move(arg);
    return Init_Controllon_light(msg_);
  }

private:
  ::common_msgs_humble::msg::Controllon msg_;
};

class Init_Controllon_gaspedal
{
public:
  explicit Init_Controllon_gaspedal(::common_msgs_humble::msg::Controllon & msg)
  : msg_(msg)
  {}
  Init_Controllon_station gaspedal(::common_msgs_humble::msg::Controllon::_gaspedal_type arg)
  {
    msg_.gaspedal = std::move(arg);
    return Init_Controllon_station(msg_);
  }

private:
  ::common_msgs_humble::msg::Controllon msg_;
};

class Init_Controllon_brakepedal
{
public:
  explicit Init_Controllon_brakepedal(::common_msgs_humble::msg::Controllon & msg)
  : msg_(msg)
  {}
  Init_Controllon_gaspedal brakepedal(::common_msgs_humble::msg::Controllon::_brakepedal_type arg)
  {
    msg_.brakepedal = std::move(arg);
    return Init_Controllon_gaspedal(msg_);
  }

private:
  ::common_msgs_humble::msg::Controllon msg_;
};

class Init_Controllon_brakemethod
{
public:
  explicit Init_Controllon_brakemethod(::common_msgs_humble::msg::Controllon & msg)
  : msg_(msg)
  {}
  Init_Controllon_brakepedal brakemethod(::common_msgs_humble::msg::Controllon::_brakemethod_type arg)
  {
    msg_.brakemethod = std::move(arg);
    return Init_Controllon_brakepedal(msg_);
  }

private:
  ::common_msgs_humble::msg::Controllon msg_;
};

class Init_Controllon_gear
{
public:
  explicit Init_Controllon_gear(::common_msgs_humble::msg::Controllon & msg)
  : msg_(msg)
  {}
  Init_Controllon_brakemethod gear(::common_msgs_humble::msg::Controllon::_gear_type arg)
  {
    msg_.gear = std::move(arg);
    return Init_Controllon_brakemethod(msg_);
  }

private:
  ::common_msgs_humble::msg::Controllon msg_;
};

class Init_Controllon_geermethod
{
public:
  explicit Init_Controllon_geermethod(::common_msgs_humble::msg::Controllon & msg)
  : msg_(msg)
  {}
  Init_Controllon_gear geermethod(::common_msgs_humble::msg::Controllon::_geermethod_type arg)
  {
    msg_.geermethod = std::move(arg);
    return Init_Controllon_gear(msg_);
  }

private:
  ::common_msgs_humble::msg::Controllon msg_;
};

class Init_Controllon_epb
{
public:
  explicit Init_Controllon_epb(::common_msgs_humble::msg::Controllon & msg)
  : msg_(msg)
  {}
  Init_Controllon_geermethod epb(::common_msgs_humble::msg::Controllon::_epb_type arg)
  {
    msg_.epb = std::move(arg);
    return Init_Controllon_geermethod(msg_);
  }

private:
  ::common_msgs_humble::msg::Controllon msg_;
};

class Init_Controllon_epbmethod
{
public:
  explicit Init_Controllon_epbmethod(::common_msgs_humble::msg::Controllon & msg)
  : msg_(msg)
  {}
  Init_Controllon_epb epbmethod(::common_msgs_humble::msg::Controllon::_epbmethod_type arg)
  {
    msg_.epbmethod = std::move(arg);
    return Init_Controllon_epb(msg_);
  }

private:
  ::common_msgs_humble::msg::Controllon msg_;
};

class Init_Controllon_gpsdis
{
public:
  explicit Init_Controllon_gpsdis(::common_msgs_humble::msg::Controllon & msg)
  : msg_(msg)
  {}
  Init_Controllon_epbmethod gpsdis(::common_msgs_humble::msg::Controllon::_gpsdis_type arg)
  {
    msg_.gpsdis = std::move(arg);
    return Init_Controllon_epbmethod(msg_);
  }

private:
  ::common_msgs_humble::msg::Controllon msg_;
};

class Init_Controllon_espmethod
{
public:
  Init_Controllon_espmethod()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Controllon_gpsdis espmethod(::common_msgs_humble::msg::Controllon::_espmethod_type arg)
  {
    msg_.espmethod = std::move(arg);
    return Init_Controllon_gpsdis(msg_);
  }

private:
  ::common_msgs_humble::msg::Controllon msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Controllon>()
{
  return common_msgs_humble::msg::builder::Init_Controllon_espmethod();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__CONTROLLON__BUILDER_HPP_
