﻿// NOLINT: This file starts with a BOM since it contain non-ASCII characters
// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Oburoadlist.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUROADLIST__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUROADLIST__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'oburoadpoint'
#include "common_msgs_humble/msg/detail/oburoadpoint__struct.h"

/// Struct defined in msg/Oburoadlist in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Oburoadlist
{
  /// 预测轨迹点数
  uint8_t planpoints;
  common_msgs_humble__msg__Oburoadpoint__Sequence oburoadpoint;
} common_msgs_humble__msg__Oburoadlist;

// Struct for a sequence of common_msgs_humble__msg__Oburoadlist.
typedef struct common_msgs_humble__msg__Oburoadlist__Sequence
{
  common_msgs_humble__msg__Oburoadlist * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Oburoadlist__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUROADLIST__STRUCT_H_
