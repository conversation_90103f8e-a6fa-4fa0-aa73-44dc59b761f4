// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Collectmap.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__COLLECTMAP__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__COLLECTMAP__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/collectmap__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Collectmap_lanesite
{
public:
  explicit Init_Collectmap_lanesite(::common_msgs_humble::msg::Collectmap & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Collectmap lanesite(::common_msgs_humble::msg::Collectmap::_lanesite_type arg)
  {
    msg_.lanesite = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Collectmap msg_;
};

class Init_Collectmap_lanenum
{
public:
  explicit Init_Collectmap_lanenum(::common_msgs_humble::msg::Collectmap & msg)
  : msg_(msg)
  {}
  Init_Collectmap_lanesite lanenum(::common_msgs_humble::msg::Collectmap::_lanenum_type arg)
  {
    msg_.lanenum = std::move(arg);
    return Init_Collectmap_lanesite(msg_);
  }

private:
  ::common_msgs_humble::msg::Collectmap msg_;
};

class Init_Collectmap_sidepass
{
public:
  explicit Init_Collectmap_sidepass(::common_msgs_humble::msg::Collectmap & msg)
  : msg_(msg)
  {}
  Init_Collectmap_lanenum sidepass(::common_msgs_humble::msg::Collectmap::_sidepass_type arg)
  {
    msg_.sidepass = std::move(arg);
    return Init_Collectmap_lanenum(msg_);
  }

private:
  ::common_msgs_humble::msg::Collectmap msg_;
};

class Init_Collectmap_laneswitch
{
public:
  explicit Init_Collectmap_laneswitch(::common_msgs_humble::msg::Collectmap & msg)
  : msg_(msg)
  {}
  Init_Collectmap_sidepass laneswitch(::common_msgs_humble::msg::Collectmap::_laneswitch_type arg)
  {
    msg_.laneswitch = std::move(arg);
    return Init_Collectmap_sidepass(msg_);
  }

private:
  ::common_msgs_humble::msg::Collectmap msg_;
};

class Init_Collectmap_rightlanewidth
{
public:
  explicit Init_Collectmap_rightlanewidth(::common_msgs_humble::msg::Collectmap & msg)
  : msg_(msg)
  {}
  Init_Collectmap_laneswitch rightlanewidth(::common_msgs_humble::msg::Collectmap::_rightlanewidth_type arg)
  {
    msg_.rightlanewidth = std::move(arg);
    return Init_Collectmap_laneswitch(msg_);
  }

private:
  ::common_msgs_humble::msg::Collectmap msg_;
};

class Init_Collectmap_leftlanewidth
{
public:
  explicit Init_Collectmap_leftlanewidth(::common_msgs_humble::msg::Collectmap & msg)
  : msg_(msg)
  {}
  Init_Collectmap_rightlanewidth leftlanewidth(::common_msgs_humble::msg::Collectmap::_leftlanewidth_type arg)
  {
    msg_.leftlanewidth = std::move(arg);
    return Init_Collectmap_rightlanewidth(msg_);
  }

private:
  ::common_msgs_humble::msg::Collectmap msg_;
};

class Init_Collectmap_lanewidth
{
public:
  explicit Init_Collectmap_lanewidth(::common_msgs_humble::msg::Collectmap & msg)
  : msg_(msg)
  {}
  Init_Collectmap_leftlanewidth lanewidth(::common_msgs_humble::msg::Collectmap::_lanewidth_type arg)
  {
    msg_.lanewidth = std::move(arg);
    return Init_Collectmap_leftlanewidth(msg_);
  }

private:
  ::common_msgs_humble::msg::Collectmap msg_;
};

class Init_Collectmap_timestamp
{
public:
  explicit Init_Collectmap_timestamp(::common_msgs_humble::msg::Collectmap & msg)
  : msg_(msg)
  {}
  Init_Collectmap_lanewidth timestamp(::common_msgs_humble::msg::Collectmap::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return Init_Collectmap_lanewidth(msg_);
  }

private:
  ::common_msgs_humble::msg::Collectmap msg_;
};

class Init_Collectmap_rightsearchdis
{
public:
  explicit Init_Collectmap_rightsearchdis(::common_msgs_humble::msg::Collectmap & msg)
  : msg_(msg)
  {}
  Init_Collectmap_timestamp rightsearchdis(::common_msgs_humble::msg::Collectmap::_rightsearchdis_type arg)
  {
    msg_.rightsearchdis = std::move(arg);
    return Init_Collectmap_timestamp(msg_);
  }

private:
  ::common_msgs_humble::msg::Collectmap msg_;
};

class Init_Collectmap_leftsearchdis
{
public:
  explicit Init_Collectmap_leftsearchdis(::common_msgs_humble::msg::Collectmap & msg)
  : msg_(msg)
  {}
  Init_Collectmap_rightsearchdis leftsearchdis(::common_msgs_humble::msg::Collectmap::_leftsearchdis_type arg)
  {
    msg_.leftsearchdis = std::move(arg);
    return Init_Collectmap_rightsearchdis(msg_);
  }

private:
  ::common_msgs_humble::msg::Collectmap msg_;
};

class Init_Collectmap_sensorlanetype
{
public:
  explicit Init_Collectmap_sensorlanetype(::common_msgs_humble::msg::Collectmap & msg)
  : msg_(msg)
  {}
  Init_Collectmap_leftsearchdis sensorlanetype(::common_msgs_humble::msg::Collectmap::_sensorlanetype_type arg)
  {
    msg_.sensorlanetype = std::move(arg);
    return Init_Collectmap_leftsearchdis(msg_);
  }

private:
  ::common_msgs_humble::msg::Collectmap msg_;
};

class Init_Collectmap_mergelanetype
{
public:
  explicit Init_Collectmap_mergelanetype(::common_msgs_humble::msg::Collectmap & msg)
  : msg_(msg)
  {}
  Init_Collectmap_sensorlanetype mergelanetype(::common_msgs_humble::msg::Collectmap::_mergelanetype_type arg)
  {
    msg_.mergelanetype = std::move(arg);
    return Init_Collectmap_sensorlanetype(msg_);
  }

private:
  ::common_msgs_humble::msg::Collectmap msg_;
};

class Init_Collectmap_sideroadwidth
{
public:
  explicit Init_Collectmap_sideroadwidth(::common_msgs_humble::msg::Collectmap & msg)
  : msg_(msg)
  {}
  Init_Collectmap_mergelanetype sideroadwidth(::common_msgs_humble::msg::Collectmap::_sideroadwidth_type arg)
  {
    msg_.sideroadwidth = std::move(arg);
    return Init_Collectmap_mergelanetype(msg_);
  }

private:
  ::common_msgs_humble::msg::Collectmap msg_;
};

class Init_Collectmap_speed
{
public:
  explicit Init_Collectmap_speed(::common_msgs_humble::msg::Collectmap & msg)
  : msg_(msg)
  {}
  Init_Collectmap_sideroadwidth speed(::common_msgs_humble::msg::Collectmap::_speed_type arg)
  {
    msg_.speed = std::move(arg);
    return Init_Collectmap_sideroadwidth(msg_);
  }

private:
  ::common_msgs_humble::msg::Collectmap msg_;
};

class Init_Collectmap_laneattr
{
public:
  explicit Init_Collectmap_laneattr(::common_msgs_humble::msg::Collectmap & msg)
  : msg_(msg)
  {}
  Init_Collectmap_speed laneattr(::common_msgs_humble::msg::Collectmap::_laneattr_type arg)
  {
    msg_.laneattr = std::move(arg);
    return Init_Collectmap_speed(msg_);
  }

private:
  ::common_msgs_humble::msg::Collectmap msg_;
};

class Init_Collectmap_property
{
public:
  explicit Init_Collectmap_property(::common_msgs_humble::msg::Collectmap & msg)
  : msg_(msg)
  {}
  Init_Collectmap_laneattr property(::common_msgs_humble::msg::Collectmap::_property_type arg)
  {
    msg_.property = std::move(arg);
    return Init_Collectmap_laneattr(msg_);
  }

private:
  ::common_msgs_humble::msg::Collectmap msg_;
};

class Init_Collectmap_zonename
{
public:
  explicit Init_Collectmap_zonename(::common_msgs_humble::msg::Collectmap & msg)
  : msg_(msg)
  {}
  Init_Collectmap_property zonename(::common_msgs_humble::msg::Collectmap::_zonename_type arg)
  {
    msg_.zonename = std::move(arg);
    return Init_Collectmap_property(msg_);
  }

private:
  ::common_msgs_humble::msg::Collectmap msg_;
};

class Init_Collectmap_mapname
{
public:
  Init_Collectmap_mapname()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Collectmap_zonename mapname(::common_msgs_humble::msg::Collectmap::_mapname_type arg)
  {
    msg_.mapname = std::move(arg);
    return Init_Collectmap_zonename(msg_);
  }

private:
  ::common_msgs_humble::msg::Collectmap msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Collectmap>()
{
  return common_msgs_humble::msg::builder::Init_Collectmap_mapname();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__COLLECTMAP__BUILDER_HPP_
