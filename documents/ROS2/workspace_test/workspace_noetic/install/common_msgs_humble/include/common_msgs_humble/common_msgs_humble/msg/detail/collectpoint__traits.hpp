// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from common_msgs_humble:msg/Collectpoint.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__COLLECTPOINT__TRAITS_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__COLLECTPOINT__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "common_msgs_humble/msg/detail/collectpoint__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

namespace common_msgs_humble
{

namespace msg
{

inline void to_flow_style_yaml(
  const Collectpoint & msg,
  std::ostream & out)
{
  out << "{";
  // member: zonename
  {
    out << "zonename: ";
    rosidl_generator_traits::value_to_yaml(msg.zonename, out);
    out << ", ";
  }

  // member: index
  {
    out << "index: ";
    rosidl_generator_traits::value_to_yaml(msg.index, out);
    out << ", ";
  }

  // member: stoptime
  {
    out << "stoptime: ";
    rosidl_generator_traits::value_to_yaml(msg.stoptime, out);
    out << ", ";
  }

  // member: property
  {
    out << "property: ";
    rosidl_generator_traits::value_to_yaml(msg.property, out);
    out << ", ";
  }

  // member: orientation
  {
    out << "orientation: ";
    rosidl_generator_traits::value_to_yaml(msg.orientation, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const Collectpoint & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: zonename
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "zonename: ";
    rosidl_generator_traits::value_to_yaml(msg.zonename, out);
    out << "\n";
  }

  // member: index
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "index: ";
    rosidl_generator_traits::value_to_yaml(msg.index, out);
    out << "\n";
  }

  // member: stoptime
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "stoptime: ";
    rosidl_generator_traits::value_to_yaml(msg.stoptime, out);
    out << "\n";
  }

  // member: property
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "property: ";
    rosidl_generator_traits::value_to_yaml(msg.property, out);
    out << "\n";
  }

  // member: orientation
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "orientation: ";
    rosidl_generator_traits::value_to_yaml(msg.orientation, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const Collectpoint & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace common_msgs_humble

namespace rosidl_generator_traits
{

[[deprecated("use common_msgs_humble::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const common_msgs_humble::msg::Collectpoint & msg,
  std::ostream & out, size_t indentation = 0)
{
  common_msgs_humble::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use common_msgs_humble::msg::to_yaml() instead")]]
inline std::string to_yaml(const common_msgs_humble::msg::Collectpoint & msg)
{
  return common_msgs_humble::msg::to_yaml(msg);
}

template<>
inline const char * data_type<common_msgs_humble::msg::Collectpoint>()
{
  return "common_msgs_humble::msg::Collectpoint";
}

template<>
inline const char * name<common_msgs_humble::msg::Collectpoint>()
{
  return "common_msgs_humble/msg/Collectpoint";
}

template<>
struct has_fixed_size<common_msgs_humble::msg::Collectpoint>
  : std::integral_constant<bool, true> {};

template<>
struct has_bounded_size<common_msgs_humble::msg::Collectpoint>
  : std::integral_constant<bool, true> {};

template<>
struct is_message<common_msgs_humble::msg::Collectpoint>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__COLLECTPOINT__TRAITS_HPP_
