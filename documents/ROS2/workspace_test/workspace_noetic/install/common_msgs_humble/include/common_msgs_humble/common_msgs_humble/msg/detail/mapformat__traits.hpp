// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from common_msgs_humble:msg/Mapformat.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__MAPFORMAT__TRAITS_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__MAPFORMAT__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "common_msgs_humble/msg/detail/mapformat__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

namespace common_msgs_humble
{

namespace msg
{

inline void to_flow_style_yaml(
  const Mapformat & msg,
  std::ostream & out)
{
  out << "{";
  // member: lon
  {
    out << "lon: ";
    rosidl_generator_traits::value_to_yaml(msg.lon, out);
    out << ", ";
  }

  // member: lat
  {
    out << "lat: ";
    rosidl_generator_traits::value_to_yaml(msg.lat, out);
    out << ", ";
  }

  // member: roadtype
  {
    out << "roadtype: ";
    rosidl_generator_traits::value_to_yaml(msg.roadtype, out);
    out << ", ";
  }

  // member: speed
  {
    out << "speed: ";
    rosidl_generator_traits::value_to_yaml(msg.speed, out);
    out << ", ";
  }

  // member: lanetype
  {
    out << "lanetype: ";
    rosidl_generator_traits::value_to_yaml(msg.lanetype, out);
    out << ", ";
  }

  // member: mergelanetype
  {
    out << "mergelanetype: ";
    rosidl_generator_traits::value_to_yaml(msg.mergelanetype, out);
    out << ", ";
  }

  // member: sensorlanetype
  {
    out << "sensorlanetype: ";
    rosidl_generator_traits::value_to_yaml(msg.sensorlanetype, out);
    out << ", ";
  }

  // member: turnlight
  {
    out << "turnlight: ";
    rosidl_generator_traits::value_to_yaml(msg.turnlight, out);
    out << ", ";
  }

  // member: sideroadwidth
  {
    out << "sideroadwidth: ";
    rosidl_generator_traits::value_to_yaml(msg.sideroadwidth, out);
    out << ", ";
  }

  // member: lanewidth
  {
    out << "lanewidth: ";
    rosidl_generator_traits::value_to_yaml(msg.lanewidth, out);
    out << ", ";
  }

  // member: leftlanewidth
  {
    out << "leftlanewidth: ";
    rosidl_generator_traits::value_to_yaml(msg.leftlanewidth, out);
    out << ", ";
  }

  // member: rightlanewidth
  {
    out << "rightlanewidth: ";
    rosidl_generator_traits::value_to_yaml(msg.rightlanewidth, out);
    out << ", ";
  }

  // member: leftsearchdis
  {
    out << "leftsearchdis: ";
    rosidl_generator_traits::value_to_yaml(msg.leftsearchdis, out);
    out << ", ";
  }

  // member: rightsearchdis
  {
    out << "rightsearchdis: ";
    rosidl_generator_traits::value_to_yaml(msg.rightsearchdis, out);
    out << ", ";
  }

  // member: heading
  {
    out << "heading: ";
    rosidl_generator_traits::value_to_yaml(msg.heading, out);
    out << ", ";
  }

  // member: curvature
  {
    out << "curvature: ";
    rosidl_generator_traits::value_to_yaml(msg.curvature, out);
    out << ", ";
  }

  // member: gpstime
  {
    out << "gpstime: ";
    rosidl_generator_traits::value_to_yaml(msg.gpstime, out);
    out << ", ";
  }

  // member: switchflag
  {
    out << "switchflag: ";
    rosidl_generator_traits::value_to_yaml(msg.switchflag, out);
    out << ", ";
  }

  // member: borrowflag
  {
    out << "borrowflag: ";
    rosidl_generator_traits::value_to_yaml(msg.borrowflag, out);
    out << ", ";
  }

  // member: lanesum
  {
    out << "lanesum: ";
    rosidl_generator_traits::value_to_yaml(msg.lanesum, out);
    out << ", ";
  }

  // member: lanenum
  {
    out << "lanenum: ";
    rosidl_generator_traits::value_to_yaml(msg.lanenum, out);
    out << ", ";
  }

  // member: backup1
  {
    out << "backup1: ";
    rosidl_generator_traits::value_to_yaml(msg.backup1, out);
    out << ", ";
  }

  // member: backup2
  {
    out << "backup2: ";
    rosidl_generator_traits::value_to_yaml(msg.backup2, out);
    out << ", ";
  }

  // member: backup3
  {
    out << "backup3: ";
    rosidl_generator_traits::value_to_yaml(msg.backup3, out);
    out << ", ";
  }

  // member: backup4
  {
    out << "backup4: ";
    rosidl_generator_traits::value_to_yaml(msg.backup4, out);
    out << ", ";
  }

  // member: backup5
  {
    out << "backup5: ";
    rosidl_generator_traits::value_to_yaml(msg.backup5, out);
    out << ", ";
  }

  // member: backup6
  {
    out << "backup6: ";
    rosidl_generator_traits::value_to_yaml(msg.backup6, out);
    out << ", ";
  }

  // member: backup7
  {
    out << "backup7: ";
    rosidl_generator_traits::value_to_yaml(msg.backup7, out);
    out << ", ";
  }

  // member: backup8
  {
    out << "backup8: ";
    rosidl_generator_traits::value_to_yaml(msg.backup8, out);
    out << ", ";
  }

  // member: backup9
  {
    out << "backup9: ";
    rosidl_generator_traits::value_to_yaml(msg.backup9, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const Mapformat & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: lon
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lon: ";
    rosidl_generator_traits::value_to_yaml(msg.lon, out);
    out << "\n";
  }

  // member: lat
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lat: ";
    rosidl_generator_traits::value_to_yaml(msg.lat, out);
    out << "\n";
  }

  // member: roadtype
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "roadtype: ";
    rosidl_generator_traits::value_to_yaml(msg.roadtype, out);
    out << "\n";
  }

  // member: speed
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "speed: ";
    rosidl_generator_traits::value_to_yaml(msg.speed, out);
    out << "\n";
  }

  // member: lanetype
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lanetype: ";
    rosidl_generator_traits::value_to_yaml(msg.lanetype, out);
    out << "\n";
  }

  // member: mergelanetype
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "mergelanetype: ";
    rosidl_generator_traits::value_to_yaml(msg.mergelanetype, out);
    out << "\n";
  }

  // member: sensorlanetype
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "sensorlanetype: ";
    rosidl_generator_traits::value_to_yaml(msg.sensorlanetype, out);
    out << "\n";
  }

  // member: turnlight
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "turnlight: ";
    rosidl_generator_traits::value_to_yaml(msg.turnlight, out);
    out << "\n";
  }

  // member: sideroadwidth
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "sideroadwidth: ";
    rosidl_generator_traits::value_to_yaml(msg.sideroadwidth, out);
    out << "\n";
  }

  // member: lanewidth
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lanewidth: ";
    rosidl_generator_traits::value_to_yaml(msg.lanewidth, out);
    out << "\n";
  }

  // member: leftlanewidth
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "leftlanewidth: ";
    rosidl_generator_traits::value_to_yaml(msg.leftlanewidth, out);
    out << "\n";
  }

  // member: rightlanewidth
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "rightlanewidth: ";
    rosidl_generator_traits::value_to_yaml(msg.rightlanewidth, out);
    out << "\n";
  }

  // member: leftsearchdis
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "leftsearchdis: ";
    rosidl_generator_traits::value_to_yaml(msg.leftsearchdis, out);
    out << "\n";
  }

  // member: rightsearchdis
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "rightsearchdis: ";
    rosidl_generator_traits::value_to_yaml(msg.rightsearchdis, out);
    out << "\n";
  }

  // member: heading
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "heading: ";
    rosidl_generator_traits::value_to_yaml(msg.heading, out);
    out << "\n";
  }

  // member: curvature
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "curvature: ";
    rosidl_generator_traits::value_to_yaml(msg.curvature, out);
    out << "\n";
  }

  // member: gpstime
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "gpstime: ";
    rosidl_generator_traits::value_to_yaml(msg.gpstime, out);
    out << "\n";
  }

  // member: switchflag
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "switchflag: ";
    rosidl_generator_traits::value_to_yaml(msg.switchflag, out);
    out << "\n";
  }

  // member: borrowflag
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "borrowflag: ";
    rosidl_generator_traits::value_to_yaml(msg.borrowflag, out);
    out << "\n";
  }

  // member: lanesum
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lanesum: ";
    rosidl_generator_traits::value_to_yaml(msg.lanesum, out);
    out << "\n";
  }

  // member: lanenum
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lanenum: ";
    rosidl_generator_traits::value_to_yaml(msg.lanenum, out);
    out << "\n";
  }

  // member: backup1
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "backup1: ";
    rosidl_generator_traits::value_to_yaml(msg.backup1, out);
    out << "\n";
  }

  // member: backup2
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "backup2: ";
    rosidl_generator_traits::value_to_yaml(msg.backup2, out);
    out << "\n";
  }

  // member: backup3
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "backup3: ";
    rosidl_generator_traits::value_to_yaml(msg.backup3, out);
    out << "\n";
  }

  // member: backup4
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "backup4: ";
    rosidl_generator_traits::value_to_yaml(msg.backup4, out);
    out << "\n";
  }

  // member: backup5
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "backup5: ";
    rosidl_generator_traits::value_to_yaml(msg.backup5, out);
    out << "\n";
  }

  // member: backup6
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "backup6: ";
    rosidl_generator_traits::value_to_yaml(msg.backup6, out);
    out << "\n";
  }

  // member: backup7
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "backup7: ";
    rosidl_generator_traits::value_to_yaml(msg.backup7, out);
    out << "\n";
  }

  // member: backup8
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "backup8: ";
    rosidl_generator_traits::value_to_yaml(msg.backup8, out);
    out << "\n";
  }

  // member: backup9
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "backup9: ";
    rosidl_generator_traits::value_to_yaml(msg.backup9, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const Mapformat & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace common_msgs_humble

namespace rosidl_generator_traits
{

[[deprecated("use common_msgs_humble::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const common_msgs_humble::msg::Mapformat & msg,
  std::ostream & out, size_t indentation = 0)
{
  common_msgs_humble::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use common_msgs_humble::msg::to_yaml() instead")]]
inline std::string to_yaml(const common_msgs_humble::msg::Mapformat & msg)
{
  return common_msgs_humble::msg::to_yaml(msg);
}

template<>
inline const char * data_type<common_msgs_humble::msg::Mapformat>()
{
  return "common_msgs_humble::msg::Mapformat";
}

template<>
inline const char * name<common_msgs_humble::msg::Mapformat>()
{
  return "common_msgs_humble/msg/Mapformat";
}

template<>
struct has_fixed_size<common_msgs_humble::msg::Mapformat>
  : std::integral_constant<bool, true> {};

template<>
struct has_bounded_size<common_msgs_humble::msg::Mapformat>
  : std::integral_constant<bool, true> {};

template<>
struct is_message<common_msgs_humble::msg::Mapformat>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__MAPFORMAT__TRAITS_HPP_
