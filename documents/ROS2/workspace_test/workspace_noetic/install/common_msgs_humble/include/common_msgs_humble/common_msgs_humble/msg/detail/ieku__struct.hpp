// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Ieku.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__IEKU__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__IEKU__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Ieku __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Ieku __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Ieku_
{
  using Type = Ieku_<ContainerAllocator>;

  explicit Ieku_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->id = 0;
      this->lata = 0.0;
      this->lona = 0.0;
      this->latb = 0.0;
      this->lonb = 0.0;
      this->width = 0.0f;
    }
  }

  explicit Ieku_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->id = 0;
      this->lata = 0.0;
      this->lona = 0.0;
      this->latb = 0.0;
      this->lonb = 0.0;
      this->width = 0.0f;
    }
  }

  // field types and members
  using _id_type =
    uint8_t;
  _id_type id;
  using _lata_type =
    double;
  _lata_type lata;
  using _lona_type =
    double;
  _lona_type lona;
  using _latb_type =
    double;
  _latb_type latb;
  using _lonb_type =
    double;
  _lonb_type lonb;
  using _width_type =
    float;
  _width_type width;

  // setters for named parameter idiom
  Type & set__id(
    const uint8_t & _arg)
  {
    this->id = _arg;
    return *this;
  }
  Type & set__lata(
    const double & _arg)
  {
    this->lata = _arg;
    return *this;
  }
  Type & set__lona(
    const double & _arg)
  {
    this->lona = _arg;
    return *this;
  }
  Type & set__latb(
    const double & _arg)
  {
    this->latb = _arg;
    return *this;
  }
  Type & set__lonb(
    const double & _arg)
  {
    this->lonb = _arg;
    return *this;
  }
  Type & set__width(
    const float & _arg)
  {
    this->width = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Ieku_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Ieku_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Ieku_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Ieku_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Ieku_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Ieku_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Ieku_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Ieku_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Ieku_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Ieku_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Ieku
    std::shared_ptr<common_msgs_humble::msg::Ieku_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Ieku
    std::shared_ptr<common_msgs_humble::msg::Ieku_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Ieku_ & other) const
  {
    if (this->id != other.id) {
      return false;
    }
    if (this->lata != other.lata) {
      return false;
    }
    if (this->lona != other.lona) {
      return false;
    }
    if (this->latb != other.latb) {
      return false;
    }
    if (this->lonb != other.lonb) {
      return false;
    }
    if (this->width != other.width) {
      return false;
    }
    return true;
  }
  bool operator!=(const Ieku_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Ieku_

// alias to use template instance with default allocator
using Ieku =
  common_msgs_humble::msg::Ieku_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__IEKU__STRUCT_HPP_
