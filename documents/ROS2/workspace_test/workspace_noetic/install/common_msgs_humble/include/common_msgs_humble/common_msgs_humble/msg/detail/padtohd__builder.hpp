// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Padtohd.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__PADTOHD__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__PADTOHD__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/padtohd__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Padtohd_path
{
public:
  explicit Init_Padtohd_path(::common_msgs_humble::msg::Padtohd & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Padtohd path(::common_msgs_humble::msg::Padtohd::_path_type arg)
  {
    msg_.path = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Padtohd msg_;
};

class Init_Padtohd_timestamp
{
public:
  explicit Init_Padtohd_timestamp(::common_msgs_humble::msg::Padtohd & msg)
  : msg_(msg)
  {}
  Init_Padtohd_path timestamp(::common_msgs_humble::msg::Padtohd::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return Init_Padtohd_path(msg_);
  }

private:
  ::common_msgs_humble::msg::Padtohd msg_;
};

class Init_Padtohd_index
{
public:
  Init_Padtohd_index()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Padtohd_timestamp index(::common_msgs_humble::msg::Padtohd::_index_type arg)
  {
    msg_.index = std::move(arg);
    return Init_Padtohd_timestamp(msg_);
  }

private:
  ::common_msgs_humble::msg::Padtohd msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Padtohd>()
{
  return common_msgs_humble::msg::builder::Init_Padtohd_index();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__PADTOHD__BUILDER_HPP_
