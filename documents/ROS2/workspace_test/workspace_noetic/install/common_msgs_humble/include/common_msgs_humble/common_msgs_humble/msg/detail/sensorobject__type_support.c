// generated from rosidl_typesupport_introspection_c/resource/idl__type_support.c.em
// with input from common_msgs_humble:msg/Sensorobject.idl
// generated code does not contain a copyright notice

#include <stddef.h>
#include "common_msgs_humble/msg/detail/sensorobject__rosidl_typesupport_introspection_c.h"
#include "common_msgs_humble/msg/rosidl_typesupport_introspection_c__visibility_control.h"
#include "rosidl_typesupport_introspection_c/field_types.h"
#include "rosidl_typesupport_introspection_c/identifier.h"
#include "rosidl_typesupport_introspection_c/message_introspection.h"
#include "common_msgs_humble/msg/detail/sensorobject__functions.h"
#include "common_msgs_humble/msg/detail/sensorobject__struct.h"


// Include directives for member types
// Member `points`
#include "common_msgs_humble/msg/point3d.h"
// Member `points`
#include "common_msgs_humble/msg/detail/point3d__rosidl_typesupport_introspection_c.h"
// Member `object_history`
#include "common_msgs_humble/msg/objecthistory.h"
// Member `object_history`
#include "common_msgs_humble/msg/detail/objecthistory__rosidl_typesupport_introspection_c.h"
// Member `object_prediction`
#include "common_msgs_humble/msg/objectprediction.h"
// Member `object_prediction`
#include "common_msgs_humble/msg/detail/objectprediction__rosidl_typesupport_introspection_c.h"

#ifdef __cplusplus
extern "C"
{
#endif

void common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__Sensorobject_init_function(
  void * message_memory, enum rosidl_runtime_c__message_initialization _init)
{
  // TODO(karsten1987): initializers are not yet implemented for typesupport c
  // see https://bgithub.xyz/ros2/ros2/issues/397
  (void) _init;
  common_msgs_humble__msg__Sensorobject__init(message_memory);
}

void common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__Sensorobject_fini_function(void * message_memory)
{
  common_msgs_humble__msg__Sensorobject__fini(message_memory);
}

size_t common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__size_function__Sensorobject__points(
  const void * untyped_member)
{
  const common_msgs_humble__msg__Point3d__Sequence * member =
    (const common_msgs_humble__msg__Point3d__Sequence *)(untyped_member);
  return member->size;
}

const void * common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__get_const_function__Sensorobject__points(
  const void * untyped_member, size_t index)
{
  const common_msgs_humble__msg__Point3d__Sequence * member =
    (const common_msgs_humble__msg__Point3d__Sequence *)(untyped_member);
  return &member->data[index];
}

void * common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__get_function__Sensorobject__points(
  void * untyped_member, size_t index)
{
  common_msgs_humble__msg__Point3d__Sequence * member =
    (common_msgs_humble__msg__Point3d__Sequence *)(untyped_member);
  return &member->data[index];
}

void common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__fetch_function__Sensorobject__points(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const common_msgs_humble__msg__Point3d * item =
    ((const common_msgs_humble__msg__Point3d *)
    common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__get_const_function__Sensorobject__points(untyped_member, index));
  common_msgs_humble__msg__Point3d * value =
    (common_msgs_humble__msg__Point3d *)(untyped_value);
  *value = *item;
}

void common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__assign_function__Sensorobject__points(
  void * untyped_member, size_t index, const void * untyped_value)
{
  common_msgs_humble__msg__Point3d * item =
    ((common_msgs_humble__msg__Point3d *)
    common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__get_function__Sensorobject__points(untyped_member, index));
  const common_msgs_humble__msg__Point3d * value =
    (const common_msgs_humble__msg__Point3d *)(untyped_value);
  *item = *value;
}

bool common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__resize_function__Sensorobject__points(
  void * untyped_member, size_t size)
{
  common_msgs_humble__msg__Point3d__Sequence * member =
    (common_msgs_humble__msg__Point3d__Sequence *)(untyped_member);
  common_msgs_humble__msg__Point3d__Sequence__fini(member);
  return common_msgs_humble__msg__Point3d__Sequence__init(member, size);
}

size_t common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__size_function__Sensorobject__object_history(
  const void * untyped_member)
{
  const common_msgs_humble__msg__Objecthistory__Sequence * member =
    (const common_msgs_humble__msg__Objecthistory__Sequence *)(untyped_member);
  return member->size;
}

const void * common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__get_const_function__Sensorobject__object_history(
  const void * untyped_member, size_t index)
{
  const common_msgs_humble__msg__Objecthistory__Sequence * member =
    (const common_msgs_humble__msg__Objecthistory__Sequence *)(untyped_member);
  return &member->data[index];
}

void * common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__get_function__Sensorobject__object_history(
  void * untyped_member, size_t index)
{
  common_msgs_humble__msg__Objecthistory__Sequence * member =
    (common_msgs_humble__msg__Objecthistory__Sequence *)(untyped_member);
  return &member->data[index];
}

void common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__fetch_function__Sensorobject__object_history(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const common_msgs_humble__msg__Objecthistory * item =
    ((const common_msgs_humble__msg__Objecthistory *)
    common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__get_const_function__Sensorobject__object_history(untyped_member, index));
  common_msgs_humble__msg__Objecthistory * value =
    (common_msgs_humble__msg__Objecthistory *)(untyped_value);
  *value = *item;
}

void common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__assign_function__Sensorobject__object_history(
  void * untyped_member, size_t index, const void * untyped_value)
{
  common_msgs_humble__msg__Objecthistory * item =
    ((common_msgs_humble__msg__Objecthistory *)
    common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__get_function__Sensorobject__object_history(untyped_member, index));
  const common_msgs_humble__msg__Objecthistory * value =
    (const common_msgs_humble__msg__Objecthistory *)(untyped_value);
  *item = *value;
}

bool common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__resize_function__Sensorobject__object_history(
  void * untyped_member, size_t size)
{
  common_msgs_humble__msg__Objecthistory__Sequence * member =
    (common_msgs_humble__msg__Objecthistory__Sequence *)(untyped_member);
  common_msgs_humble__msg__Objecthistory__Sequence__fini(member);
  return common_msgs_humble__msg__Objecthistory__Sequence__init(member, size);
}

size_t common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__size_function__Sensorobject__object_prediction(
  const void * untyped_member)
{
  const common_msgs_humble__msg__Objectprediction__Sequence * member =
    (const common_msgs_humble__msg__Objectprediction__Sequence *)(untyped_member);
  return member->size;
}

const void * common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__get_const_function__Sensorobject__object_prediction(
  const void * untyped_member, size_t index)
{
  const common_msgs_humble__msg__Objectprediction__Sequence * member =
    (const common_msgs_humble__msg__Objectprediction__Sequence *)(untyped_member);
  return &member->data[index];
}

void * common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__get_function__Sensorobject__object_prediction(
  void * untyped_member, size_t index)
{
  common_msgs_humble__msg__Objectprediction__Sequence * member =
    (common_msgs_humble__msg__Objectprediction__Sequence *)(untyped_member);
  return &member->data[index];
}

void common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__fetch_function__Sensorobject__object_prediction(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const common_msgs_humble__msg__Objectprediction * item =
    ((const common_msgs_humble__msg__Objectprediction *)
    common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__get_const_function__Sensorobject__object_prediction(untyped_member, index));
  common_msgs_humble__msg__Objectprediction * value =
    (common_msgs_humble__msg__Objectprediction *)(untyped_value);
  *value = *item;
}

void common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__assign_function__Sensorobject__object_prediction(
  void * untyped_member, size_t index, const void * untyped_value)
{
  common_msgs_humble__msg__Objectprediction * item =
    ((common_msgs_humble__msg__Objectprediction *)
    common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__get_function__Sensorobject__object_prediction(untyped_member, index));
  const common_msgs_humble__msg__Objectprediction * value =
    (const common_msgs_humble__msg__Objectprediction *)(untyped_value);
  *item = *value;
}

bool common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__resize_function__Sensorobject__object_prediction(
  void * untyped_member, size_t size)
{
  common_msgs_humble__msg__Objectprediction__Sequence * member =
    (common_msgs_humble__msg__Objectprediction__Sequence *)(untyped_member);
  common_msgs_humble__msg__Objectprediction__Sequence__fini(member);
  return common_msgs_humble__msg__Objectprediction__Sequence__init(member, size);
}

static rosidl_typesupport_introspection_c__MessageMember common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__Sensorobject_message_member_array[33] = {
  {
    "id",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_UINT32,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorobject, id),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "x",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_FLOAT,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorobject, x),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "y",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_FLOAT,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorobject, y),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "z",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_FLOAT,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorobject, z),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "longtitude",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_DOUBLE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorobject, longtitude),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "latitude",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_DOUBLE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorobject, latitude),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "altitude",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_DOUBLE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorobject, altitude),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "relspeedy",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_FLOAT,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorobject, relspeedy),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "relspeedx",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_FLOAT,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorobject, relspeedx),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "rollrad",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_FLOAT,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorobject, rollrad),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "pitchrad",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_FLOAT,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorobject, pitchrad),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "azimuth",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_FLOAT,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorobject, azimuth),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "pitchrate",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_DOUBLE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorobject, pitchrate),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "rollrate",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_DOUBLE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorobject, rollrate),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "yawrate",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_DOUBLE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorobject, yawrate),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "width",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_FLOAT,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorobject, width),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "length",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_FLOAT,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorobject, length),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "height",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_FLOAT,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorobject, height),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "classification",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_UINT8,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorobject, classification),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "value",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_UINT8,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorobject, value),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "confidence",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_FLOAT,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorobject, confidence),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "points",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message (initialized later)
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorobject, points),  // bytes offset in struct
    NULL,  // default value
    common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__size_function__Sensorobject__points,  // size() function pointer
    common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__get_const_function__Sensorobject__points,  // get_const(index) function pointer
    common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__get_function__Sensorobject__points,  // get(index) function pointer
    common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__fetch_function__Sensorobject__points,  // fetch(index, &value) function pointer
    common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__assign_function__Sensorobject__points,  // assign(index, value) function pointer
    common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__resize_function__Sensorobject__points  // resize(index) function pointer
  },
  {
    "driving_intent",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_UINT8,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorobject, driving_intent),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "behavior_state",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_UINT8,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorobject, behavior_state),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "radarindex",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_UINT8,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorobject, radarindex),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "radarobjectid",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_UINT8,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorobject, radarobjectid),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "s",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_FLOAT,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorobject, s),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "l",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_FLOAT,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorobject, l),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "speeds",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_FLOAT,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorobject, speeds),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "speedl",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_FLOAT,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorobject, speedl),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "object_decision",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_UINT8,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorobject, object_decision),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "object_history",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message (initialized later)
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorobject, object_history),  // bytes offset in struct
    NULL,  // default value
    common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__size_function__Sensorobject__object_history,  // size() function pointer
    common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__get_const_function__Sensorobject__object_history,  // get_const(index) function pointer
    common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__get_function__Sensorobject__object_history,  // get(index) function pointer
    common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__fetch_function__Sensorobject__object_history,  // fetch(index, &value) function pointer
    common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__assign_function__Sensorobject__object_history,  // assign(index, value) function pointer
    common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__resize_function__Sensorobject__object_history  // resize(index) function pointer
  },
  {
    "object_prediction",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message (initialized later)
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(common_msgs_humble__msg__Sensorobject, object_prediction),  // bytes offset in struct
    NULL,  // default value
    common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__size_function__Sensorobject__object_prediction,  // size() function pointer
    common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__get_const_function__Sensorobject__object_prediction,  // get_const(index) function pointer
    common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__get_function__Sensorobject__object_prediction,  // get(index) function pointer
    common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__fetch_function__Sensorobject__object_prediction,  // fetch(index, &value) function pointer
    common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__assign_function__Sensorobject__object_prediction,  // assign(index, value) function pointer
    common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__resize_function__Sensorobject__object_prediction  // resize(index) function pointer
  }
};

static const rosidl_typesupport_introspection_c__MessageMembers common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__Sensorobject_message_members = {
  "common_msgs_humble__msg",  // message namespace
  "Sensorobject",  // message name
  33,  // number of fields
  sizeof(common_msgs_humble__msg__Sensorobject),
  common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__Sensorobject_message_member_array,  // message members
  common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__Sensorobject_init_function,  // function to initialize message memory (memory has to be allocated)
  common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__Sensorobject_fini_function  // function to terminate message instance (will not free memory)
};

// this is not const since it must be initialized on first access
// since C does not allow non-integral compile-time constants
static rosidl_message_type_support_t common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__Sensorobject_message_type_support_handle = {
  0,
  &common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__Sensorobject_message_members,
  get_message_typesupport_handle_function,
};

ROSIDL_TYPESUPPORT_INTROSPECTION_C_EXPORT_common_msgs_humble
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, common_msgs_humble, msg, Sensorobject)() {
  common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__Sensorobject_message_member_array[21].members_ =
    ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, common_msgs_humble, msg, Point3d)();
  common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__Sensorobject_message_member_array[31].members_ =
    ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, common_msgs_humble, msg, Objecthistory)();
  common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__Sensorobject_message_member_array[32].members_ =
    ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, common_msgs_humble, msg, Objectprediction)();
  if (!common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__Sensorobject_message_type_support_handle.typesupport_identifier) {
    common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__Sensorobject_message_type_support_handle.typesupport_identifier =
      rosidl_typesupport_introspection_c__identifier;
  }
  return &common_msgs_humble__msg__Sensorobject__rosidl_typesupport_introspection_c__Sensorobject_message_type_support_handle;
}
#ifdef __cplusplus
}
#endif
