// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Remotedrivestatus.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__REMOTEDRIVESTATUS__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__REMOTEDRIVESTATUS__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/remotedrivestatus__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Remotedrivestatus_systemstatus
{
public:
  explicit Init_Remotedrivestatus_systemstatus(::common_msgs_humble::msg::Remotedrivestatus & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Remotedrivestatus systemstatus(::common_msgs_humble::msg::Remotedrivestatus::_systemstatus_type arg)
  {
    msg_.systemstatus = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Remotedrivestatus msg_;
};

class Init_Remotedrivestatus_remotedrivestatus
{
public:
  explicit Init_Remotedrivestatus_remotedrivestatus(::common_msgs_humble::msg::Remotedrivestatus & msg)
  : msg_(msg)
  {}
  Init_Remotedrivestatus_systemstatus remotedrivestatus(::common_msgs_humble::msg::Remotedrivestatus::_remotedrivestatus_type arg)
  {
    msg_.remotedrivestatus = std::move(arg);
    return Init_Remotedrivestatus_systemstatus(msg_);
  }

private:
  ::common_msgs_humble::msg::Remotedrivestatus msg_;
};

class Init_Remotedrivestatus_drivemode
{
public:
  explicit Init_Remotedrivestatus_drivemode(::common_msgs_humble::msg::Remotedrivestatus & msg)
  : msg_(msg)
  {}
  Init_Remotedrivestatus_remotedrivestatus drivemode(::common_msgs_humble::msg::Remotedrivestatus::_drivemode_type arg)
  {
    msg_.drivemode = std::move(arg);
    return Init_Remotedrivestatus_remotedrivestatus(msg_);
  }

private:
  ::common_msgs_humble::msg::Remotedrivestatus msg_;
};

class Init_Remotedrivestatus_timestamp
{
public:
  Init_Remotedrivestatus_timestamp()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Remotedrivestatus_drivemode timestamp(::common_msgs_humble::msg::Remotedrivestatus::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return Init_Remotedrivestatus_drivemode(msg_);
  }

private:
  ::common_msgs_humble::msg::Remotedrivestatus msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Remotedrivestatus>()
{
  return common_msgs_humble::msg::builder::Init_Remotedrivestatus_timestamp();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__REMOTEDRIVESTATUS__BUILDER_HPP_
