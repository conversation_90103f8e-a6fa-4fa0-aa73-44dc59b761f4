// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/App.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__APP__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__APP__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__App __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__App __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct App_
{
  using Type = App_<ContainerAllocator>;

  explicit App_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->stopgo = 0;
      this->zonename = 0;
      this->apsnum = 0;
      this->estop = 0;
      this->park = 0;
      this->timestamp = 0ll;
    }
  }

  explicit App_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->stopgo = 0;
      this->zonename = 0;
      this->apsnum = 0;
      this->estop = 0;
      this->park = 0;
      this->timestamp = 0ll;
    }
  }

  // field types and members
  using _stopgo_type =
    uint8_t;
  _stopgo_type stopgo;
  using _zonename_type =
    uint8_t;
  _zonename_type zonename;
  using _apsnum_type =
    uint8_t;
  _apsnum_type apsnum;
  using _estop_type =
    uint8_t;
  _estop_type estop;
  using _park_type =
    uint8_t;
  _park_type park;
  using _timestamp_type =
    int64_t;
  _timestamp_type timestamp;

  // setters for named parameter idiom
  Type & set__stopgo(
    const uint8_t & _arg)
  {
    this->stopgo = _arg;
    return *this;
  }
  Type & set__zonename(
    const uint8_t & _arg)
  {
    this->zonename = _arg;
    return *this;
  }
  Type & set__apsnum(
    const uint8_t & _arg)
  {
    this->apsnum = _arg;
    return *this;
  }
  Type & set__estop(
    const uint8_t & _arg)
  {
    this->estop = _arg;
    return *this;
  }
  Type & set__park(
    const uint8_t & _arg)
  {
    this->park = _arg;
    return *this;
  }
  Type & set__timestamp(
    const int64_t & _arg)
  {
    this->timestamp = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::App_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::App_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::App_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::App_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::App_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::App_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::App_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::App_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::App_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::App_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__App
    std::shared_ptr<common_msgs_humble::msg::App_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__App
    std::shared_ptr<common_msgs_humble::msg::App_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const App_ & other) const
  {
    if (this->stopgo != other.stopgo) {
      return false;
    }
    if (this->zonename != other.zonename) {
      return false;
    }
    if (this->apsnum != other.apsnum) {
      return false;
    }
    if (this->estop != other.estop) {
      return false;
    }
    if (this->park != other.park) {
      return false;
    }
    if (this->timestamp != other.timestamp) {
      return false;
    }
    return true;
  }
  bool operator!=(const App_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct App_

// alias to use template instance with default allocator
using App =
  common_msgs_humble::msg::App_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__APP__STRUCT_HPP_
