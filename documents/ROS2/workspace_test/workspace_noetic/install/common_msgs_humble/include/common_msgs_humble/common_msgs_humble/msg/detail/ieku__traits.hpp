// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from common_msgs_humble:msg/Ieku.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__IEKU__TRAITS_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__IEKU__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "common_msgs_humble/msg/detail/ieku__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

namespace common_msgs_humble
{

namespace msg
{

inline void to_flow_style_yaml(
  const Ieku & msg,
  std::ostream & out)
{
  out << "{";
  // member: id
  {
    out << "id: ";
    rosidl_generator_traits::value_to_yaml(msg.id, out);
    out << ", ";
  }

  // member: lata
  {
    out << "lata: ";
    rosidl_generator_traits::value_to_yaml(msg.lata, out);
    out << ", ";
  }

  // member: lona
  {
    out << "lona: ";
    rosidl_generator_traits::value_to_yaml(msg.lona, out);
    out << ", ";
  }

  // member: latb
  {
    out << "latb: ";
    rosidl_generator_traits::value_to_yaml(msg.latb, out);
    out << ", ";
  }

  // member: lonb
  {
    out << "lonb: ";
    rosidl_generator_traits::value_to_yaml(msg.lonb, out);
    out << ", ";
  }

  // member: width
  {
    out << "width: ";
    rosidl_generator_traits::value_to_yaml(msg.width, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const Ieku & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: id
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "id: ";
    rosidl_generator_traits::value_to_yaml(msg.id, out);
    out << "\n";
  }

  // member: lata
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lata: ";
    rosidl_generator_traits::value_to_yaml(msg.lata, out);
    out << "\n";
  }

  // member: lona
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lona: ";
    rosidl_generator_traits::value_to_yaml(msg.lona, out);
    out << "\n";
  }

  // member: latb
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "latb: ";
    rosidl_generator_traits::value_to_yaml(msg.latb, out);
    out << "\n";
  }

  // member: lonb
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "lonb: ";
    rosidl_generator_traits::value_to_yaml(msg.lonb, out);
    out << "\n";
  }

  // member: width
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "width: ";
    rosidl_generator_traits::value_to_yaml(msg.width, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const Ieku & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace common_msgs_humble

namespace rosidl_generator_traits
{

[[deprecated("use common_msgs_humble::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const common_msgs_humble::msg::Ieku & msg,
  std::ostream & out, size_t indentation = 0)
{
  common_msgs_humble::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use common_msgs_humble::msg::to_yaml() instead")]]
inline std::string to_yaml(const common_msgs_humble::msg::Ieku & msg)
{
  return common_msgs_humble::msg::to_yaml(msg);
}

template<>
inline const char * data_type<common_msgs_humble::msg::Ieku>()
{
  return "common_msgs_humble::msg::Ieku";
}

template<>
inline const char * name<common_msgs_humble::msg::Ieku>()
{
  return "common_msgs_humble/msg/Ieku";
}

template<>
struct has_fixed_size<common_msgs_humble::msg::Ieku>
  : std::integral_constant<bool, true> {};

template<>
struct has_bounded_size<common_msgs_humble::msg::Ieku>
  : std::integral_constant<bool, true> {};

template<>
struct is_message<common_msgs_humble::msg::Ieku>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__IEKU__TRAITS_HPP_
