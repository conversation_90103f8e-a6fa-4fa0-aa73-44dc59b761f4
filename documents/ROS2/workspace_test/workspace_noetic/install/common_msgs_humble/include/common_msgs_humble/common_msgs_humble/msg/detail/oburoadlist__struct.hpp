// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Oburoadlist.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUROADLIST__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUROADLIST__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'oburoadpoint'
#include "common_msgs_humble/msg/detail/oburoadpoint__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Oburoadlist __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Oburoadlist __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Oburoadlist_
{
  using Type = Oburoadlist_<ContainerAllocator>;

  explicit Oburoadlist_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->planpoints = 0;
    }
  }

  explicit Oburoadlist_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->planpoints = 0;
    }
  }

  // field types and members
  using _planpoints_type =
    uint8_t;
  _planpoints_type planpoints;
  using _oburoadpoint_type =
    std::vector<common_msgs_humble::msg::Oburoadpoint_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<common_msgs_humble::msg::Oburoadpoint_<ContainerAllocator>>>;
  _oburoadpoint_type oburoadpoint;

  // setters for named parameter idiom
  Type & set__planpoints(
    const uint8_t & _arg)
  {
    this->planpoints = _arg;
    return *this;
  }
  Type & set__oburoadpoint(
    const std::vector<common_msgs_humble::msg::Oburoadpoint_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<common_msgs_humble::msg::Oburoadpoint_<ContainerAllocator>>> & _arg)
  {
    this->oburoadpoint = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Oburoadlist_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Oburoadlist_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Oburoadlist_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Oburoadlist_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Oburoadlist_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Oburoadlist_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Oburoadlist_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Oburoadlist_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Oburoadlist_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Oburoadlist_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Oburoadlist
    std::shared_ptr<common_msgs_humble::msg::Oburoadlist_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Oburoadlist
    std::shared_ptr<common_msgs_humble::msg::Oburoadlist_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Oburoadlist_ & other) const
  {
    if (this->planpoints != other.planpoints) {
      return false;
    }
    if (this->oburoadpoint != other.oburoadpoint) {
      return false;
    }
    return true;
  }
  bool operator!=(const Oburoadlist_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Oburoadlist_

// alias to use template instance with default allocator
using Oburoadlist =
  common_msgs_humble::msg::Oburoadlist_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUROADLIST__STRUCT_HPP_
