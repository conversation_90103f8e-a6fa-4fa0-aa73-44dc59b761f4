﻿// NOLINT: This file starts with a BOM since it contain non-ASCII characters
// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Decisionbehavior.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__DECISIONBEHAVIOR__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__DECISIONBEHAVIOR__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'obs'
#include "common_msgs_humble/msg/detail/sensorobject__struct.h"

/// Struct defined in msg/Decisionbehavior in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Decisionbehavior
{
  /// 驾驶场景,默认1
  uint8_t drivebehavior;
  /// 影响驾驶的障碍物,车辆坐标系右前上
  common_msgs_humble__msg__Sensorobject__Sequence obs;
  /// 有效位
  uint8_t isvalid;
  /// 转向灯,0-无/1-左/2-右/3-双闪
  uint8_t turnlights;
  /// 异常类型,默认0
  uint8_t laneblock;
  /// 所在车道,默认0
  uint8_t door;
  /// 时间戳(同当帧gps时间戳)
  int64_t timestamp;
  /// nan
  uint8_t mergetrigger;
  /// 期望速度,m/s
  float guidespeed;
  /// nan
  uint8_t avoidsituation;
  /// 预测结果有效标志位,默认0
  uint8_t alert;
  /// 偏离参考线距离
  float deviation;
  /// nan
  float starttime;
  /// nan
  float endtime;
  /// 车辆行使方向,默认0
  uint8_t carworkstatus;
  /// 动态巡航触发标志位,默认0
  bool stationblock;
  /// nan
  uint8_t needreplan;
  /// 异常显示文字类型,默认0
  uint8_t virtualpointtype;
} common_msgs_humble__msg__Decisionbehavior;

// Struct for a sequence of common_msgs_humble__msg__Decisionbehavior.
typedef struct common_msgs_humble__msg__Decisionbehavior__Sequence
{
  common_msgs_humble__msg__Decisionbehavior * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Decisionbehavior__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__DECISIONBEHAVIOR__STRUCT_H_
