// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from common_msgs_humble:msg/Obupant.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUPANT__BUILDER_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUPANT__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "common_msgs_humble/msg/detail/obupant__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace common_msgs_humble
{

namespace msg
{

namespace builder
{

class Init_Obupant_roadlist
{
public:
  explicit Init_Obupant_roadlist(::common_msgs_humble::msg::Obupant & msg)
  : msg_(msg)
  {}
  ::common_msgs_humble::msg::Obupant roadlist(::common_msgs_humble::msg::Obupant::_roadlist_type arg)
  {
    msg_.roadlist = std::move(arg);
    return std::move(msg_);
  }

private:
  ::common_msgs_humble::msg::Obupant msg_;
};

class Init_Obupant_planlist_num
{
public:
  explicit Init_Obupant_planlist_num(::common_msgs_humble::msg::Obupant & msg)
  : msg_(msg)
  {}
  Init_Obupant_roadlist planlist_num(::common_msgs_humble::msg::Obupant::_planlist_num_type arg)
  {
    msg_.planlist_num = std::move(arg);
    return Init_Obupant_roadlist(msg_);
  }

private:
  ::common_msgs_humble::msg::Obupant msg_;
};

class Init_Obupant_lat
{
public:
  explicit Init_Obupant_lat(::common_msgs_humble::msg::Obupant & msg)
  : msg_(msg)
  {}
  Init_Obupant_planlist_num lat(::common_msgs_humble::msg::Obupant::_lat_type arg)
  {
    msg_.lat = std::move(arg);
    return Init_Obupant_planlist_num(msg_);
  }

private:
  ::common_msgs_humble::msg::Obupant msg_;
};

class Init_Obupant_lon
{
public:
  explicit Init_Obupant_lon(::common_msgs_humble::msg::Obupant & msg)
  : msg_(msg)
  {}
  Init_Obupant_lat lon(::common_msgs_humble::msg::Obupant::_lon_type arg)
  {
    msg_.lon = std::move(arg);
    return Init_Obupant_lat(msg_);
  }

private:
  ::common_msgs_humble::msg::Obupant msg_;
};

class Init_Obupant_height
{
public:
  explicit Init_Obupant_height(::common_msgs_humble::msg::Obupant & msg)
  : msg_(msg)
  {}
  Init_Obupant_lon height(::common_msgs_humble::msg::Obupant::_height_type arg)
  {
    msg_.height = std::move(arg);
    return Init_Obupant_lon(msg_);
  }

private:
  ::common_msgs_humble::msg::Obupant msg_;
};

class Init_Obupant_length
{
public:
  explicit Init_Obupant_length(::common_msgs_humble::msg::Obupant & msg)
  : msg_(msg)
  {}
  Init_Obupant_height length(::common_msgs_humble::msg::Obupant::_length_type arg)
  {
    msg_.length = std::move(arg);
    return Init_Obupant_height(msg_);
  }

private:
  ::common_msgs_humble::msg::Obupant msg_;
};

class Init_Obupant_width
{
public:
  explicit Init_Obupant_width(::common_msgs_humble::msg::Obupant & msg)
  : msg_(msg)
  {}
  Init_Obupant_length width(::common_msgs_humble::msg::Obupant::_width_type arg)
  {
    msg_.width = std::move(arg);
    return Init_Obupant_length(msg_);
  }

private:
  ::common_msgs_humble::msg::Obupant msg_;
};

class Init_Obupant_acc4way_yaw
{
public:
  explicit Init_Obupant_acc4way_yaw(::common_msgs_humble::msg::Obupant & msg)
  : msg_(msg)
  {}
  Init_Obupant_width acc4way_yaw(::common_msgs_humble::msg::Obupant::_acc4way_yaw_type arg)
  {
    msg_.acc4way_yaw = std::move(arg);
    return Init_Obupant_width(msg_);
  }

private:
  ::common_msgs_humble::msg::Obupant msg_;
};

class Init_Obupant_acc4way_vert
{
public:
  explicit Init_Obupant_acc4way_vert(::common_msgs_humble::msg::Obupant & msg)
  : msg_(msg)
  {}
  Init_Obupant_acc4way_yaw acc4way_vert(::common_msgs_humble::msg::Obupant::_acc4way_vert_type arg)
  {
    msg_.acc4way_vert = std::move(arg);
    return Init_Obupant_acc4way_yaw(msg_);
  }

private:
  ::common_msgs_humble::msg::Obupant msg_;
};

class Init_Obupant_acc4way_lat
{
public:
  explicit Init_Obupant_acc4way_lat(::common_msgs_humble::msg::Obupant & msg)
  : msg_(msg)
  {}
  Init_Obupant_acc4way_vert acc4way_lat(::common_msgs_humble::msg::Obupant::_acc4way_lat_type arg)
  {
    msg_.acc4way_lat = std::move(arg);
    return Init_Obupant_acc4way_vert(msg_);
  }

private:
  ::common_msgs_humble::msg::Obupant msg_;
};

class Init_Obupant_acc4way_lon
{
public:
  explicit Init_Obupant_acc4way_lon(::common_msgs_humble::msg::Obupant & msg)
  : msg_(msg)
  {}
  Init_Obupant_acc4way_lat acc4way_lon(::common_msgs_humble::msg::Obupant::_acc4way_lon_type arg)
  {
    msg_.acc4way_lon = std::move(arg);
    return Init_Obupant_acc4way_lat(msg_);
  }

private:
  ::common_msgs_humble::msg::Obupant msg_;
};

class Init_Obupant_accel_angle
{
public:
  explicit Init_Obupant_accel_angle(::common_msgs_humble::msg::Obupant & msg)
  : msg_(msg)
  {}
  Init_Obupant_acc4way_lon accel_angle(::common_msgs_humble::msg::Obupant::_accel_angle_type arg)
  {
    msg_.accel_angle = std::move(arg);
    return Init_Obupant_acc4way_lon(msg_);
  }

private:
  ::common_msgs_humble::msg::Obupant msg_;
};

class Init_Obupant_accel
{
public:
  explicit Init_Obupant_accel(::common_msgs_humble::msg::Obupant & msg)
  : msg_(msg)
  {}
  Init_Obupant_accel_angle accel(::common_msgs_humble::msg::Obupant::_accel_type arg)
  {
    msg_.accel = std::move(arg);
    return Init_Obupant_accel_angle(msg_);
  }

private:
  ::common_msgs_humble::msg::Obupant msg_;
};

class Init_Obupant_heading
{
public:
  explicit Init_Obupant_heading(::common_msgs_humble::msg::Obupant & msg)
  : msg_(msg)
  {}
  Init_Obupant_accel heading(::common_msgs_humble::msg::Obupant::_heading_type arg)
  {
    msg_.heading = std::move(arg);
    return Init_Obupant_accel(msg_);
  }

private:
  ::common_msgs_humble::msg::Obupant msg_;
};

class Init_Obupant_speed
{
public:
  explicit Init_Obupant_speed(::common_msgs_humble::msg::Obupant & msg)
  : msg_(msg)
  {}
  Init_Obupant_heading speed(::common_msgs_humble::msg::Obupant::_speed_type arg)
  {
    msg_.speed = std::move(arg);
    return Init_Obupant_heading(msg_);
  }

private:
  ::common_msgs_humble::msg::Obupant msg_;
};

class Init_Obupant_pos_latitude
{
public:
  explicit Init_Obupant_pos_latitude(::common_msgs_humble::msg::Obupant & msg)
  : msg_(msg)
  {}
  Init_Obupant_speed pos_latitude(::common_msgs_humble::msg::Obupant::_pos_latitude_type arg)
  {
    msg_.pos_latitude = std::move(arg);
    return Init_Obupant_speed(msg_);
  }

private:
  ::common_msgs_humble::msg::Obupant msg_;
};

class Init_Obupant_pos_lat
{
public:
  explicit Init_Obupant_pos_lat(::common_msgs_humble::msg::Obupant & msg)
  : msg_(msg)
  {}
  Init_Obupant_pos_latitude pos_lat(::common_msgs_humble::msg::Obupant::_pos_lat_type arg)
  {
    msg_.pos_lat = std::move(arg);
    return Init_Obupant_pos_latitude(msg_);
  }

private:
  ::common_msgs_humble::msg::Obupant msg_;
};

class Init_Obupant_pos_lon
{
public:
  explicit Init_Obupant_pos_lon(::common_msgs_humble::msg::Obupant & msg)
  : msg_(msg)
  {}
  Init_Obupant_pos_lat pos_lon(::common_msgs_humble::msg::Obupant::_pos_lon_type arg)
  {
    msg_.pos_lon = std::move(arg);
    return Init_Obupant_pos_lat(msg_);
  }

private:
  ::common_msgs_humble::msg::Obupant msg_;
};

class Init_Obupant_sec_mark
{
public:
  explicit Init_Obupant_sec_mark(::common_msgs_humble::msg::Obupant & msg)
  : msg_(msg)
  {}
  Init_Obupant_pos_lon sec_mark(::common_msgs_humble::msg::Obupant::_sec_mark_type arg)
  {
    msg_.sec_mark = std::move(arg);
    return Init_Obupant_pos_lon(msg_);
  }

private:
  ::common_msgs_humble::msg::Obupant msg_;
};

class Init_Obupant_source_id
{
public:
  explicit Init_Obupant_source_id(::common_msgs_humble::msg::Obupant & msg)
  : msg_(msg)
  {}
  Init_Obupant_sec_mark source_id(::common_msgs_humble::msg::Obupant::_source_id_type arg)
  {
    msg_.source_id = std::move(arg);
    return Init_Obupant_sec_mark(msg_);
  }

private:
  ::common_msgs_humble::msg::Obupant msg_;
};

class Init_Obupant_source
{
public:
  explicit Init_Obupant_source(::common_msgs_humble::msg::Obupant & msg)
  : msg_(msg)
  {}
  Init_Obupant_source_id source(::common_msgs_humble::msg::Obupant::_source_type arg)
  {
    msg_.source = std::move(arg);
    return Init_Obupant_source_id(msg_);
  }

private:
  ::common_msgs_humble::msg::Obupant msg_;
};

class Init_Obupant_ptc_id
{
public:
  explicit Init_Obupant_ptc_id(::common_msgs_humble::msg::Obupant & msg)
  : msg_(msg)
  {}
  Init_Obupant_source ptc_id(::common_msgs_humble::msg::Obupant::_ptc_id_type arg)
  {
    msg_.ptc_id = std::move(arg);
    return Init_Obupant_source(msg_);
  }

private:
  ::common_msgs_humble::msg::Obupant msg_;
};

class Init_Obupant_ptc_type
{
public:
  Init_Obupant_ptc_type()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_Obupant_ptc_id ptc_type(::common_msgs_humble::msg::Obupant::_ptc_type_type arg)
  {
    msg_.ptc_type = std::move(arg);
    return Init_Obupant_ptc_id(msg_);
  }

private:
  ::common_msgs_humble::msg::Obupant msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::common_msgs_humble::msg::Obupant>()
{
  return common_msgs_humble::msg::builder::Init_Obupant_ptc_type();
}

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUPANT__BUILDER_HPP_
