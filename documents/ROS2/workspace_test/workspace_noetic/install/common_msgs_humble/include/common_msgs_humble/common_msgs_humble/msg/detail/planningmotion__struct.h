﻿// NOLINT: This file starts with a BOM since it contain non-ASCII characters
// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Planningmotion.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__PLANNINGMOTION__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__PLANNINGMOTION__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'points'
#include "common_msgs_humble/msg/detail/roadpoint__struct.h"

/// Struct defined in msg/Planningmotion in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Planningmotion
{
  /// 轨迹点
  common_msgs_humble__msg__Roadpoint__Sequence points;
  /// 期望速度,m/s
  float guidespeed;
  /// nan
  float guideangle;
  /// nan
  float changelanedis;
  /// nan
  float obslondis;
  /// nan
  float obslatdis;
  /// 有效位
  uint8_t isvalid;
  /// 时间戳
  int64_t timestamp;
} common_msgs_humble__msg__Planningmotion;

// Struct for a sequence of common_msgs_humble__msg__Planningmotion.
typedef struct common_msgs_humble__msg__Planningmotion__Sequence
{
  common_msgs_humble__msg__Planningmotion * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Planningmotion__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__PLANNINGMOTION__STRUCT_H_
