// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from common_msgs_humble:msg/Oburoadpoint.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUROADPOINT__STRUCT_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUROADPOINT__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


#ifndef _WIN32
# define DEPRECATED__common_msgs_humble__msg__Oburoadpoint __attribute__((deprecated))
#else
# define DEPRECATED__common_msgs_humble__msg__Oburoadpoint __declspec(deprecated)
#endif

namespace common_msgs_humble
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct Oburoadpoint_
{
  using Type = Oburoadpoint_<ContainerAllocator>;

  explicit Oburoadpoint_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->lon = 0.0;
      this->lat = 0.0;
      this->speed = 0.0;
      this->accel = 0.0;
      this->heading = 0.0;
      this->availability = 0l;
    }
  }

  explicit Oburoadpoint_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->lon = 0.0;
      this->lat = 0.0;
      this->speed = 0.0;
      this->accel = 0.0;
      this->heading = 0.0;
      this->availability = 0l;
    }
  }

  // field types and members
  using _lon_type =
    double;
  _lon_type lon;
  using _lat_type =
    double;
  _lat_type lat;
  using _speed_type =
    double;
  _speed_type speed;
  using _accel_type =
    double;
  _accel_type accel;
  using _heading_type =
    double;
  _heading_type heading;
  using _availability_type =
    int32_t;
  _availability_type availability;

  // setters for named parameter idiom
  Type & set__lon(
    const double & _arg)
  {
    this->lon = _arg;
    return *this;
  }
  Type & set__lat(
    const double & _arg)
  {
    this->lat = _arg;
    return *this;
  }
  Type & set__speed(
    const double & _arg)
  {
    this->speed = _arg;
    return *this;
  }
  Type & set__accel(
    const double & _arg)
  {
    this->accel = _arg;
    return *this;
  }
  Type & set__heading(
    const double & _arg)
  {
    this->heading = _arg;
    return *this;
  }
  Type & set__availability(
    const int32_t & _arg)
  {
    this->availability = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    common_msgs_humble::msg::Oburoadpoint_<ContainerAllocator> *;
  using ConstRawPtr =
    const common_msgs_humble::msg::Oburoadpoint_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Oburoadpoint_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<common_msgs_humble::msg::Oburoadpoint_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Oburoadpoint_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Oburoadpoint_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      common_msgs_humble::msg::Oburoadpoint_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<common_msgs_humble::msg::Oburoadpoint_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Oburoadpoint_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<common_msgs_humble::msg::Oburoadpoint_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__common_msgs_humble__msg__Oburoadpoint
    std::shared_ptr<common_msgs_humble::msg::Oburoadpoint_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__common_msgs_humble__msg__Oburoadpoint
    std::shared_ptr<common_msgs_humble::msg::Oburoadpoint_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const Oburoadpoint_ & other) const
  {
    if (this->lon != other.lon) {
      return false;
    }
    if (this->lat != other.lat) {
      return false;
    }
    if (this->speed != other.speed) {
      return false;
    }
    if (this->accel != other.accel) {
      return false;
    }
    if (this->heading != other.heading) {
      return false;
    }
    if (this->availability != other.availability) {
      return false;
    }
    return true;
  }
  bool operator!=(const Oburoadpoint_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct Oburoadpoint_

// alias to use template instance with default allocator
using Oburoadpoint =
  common_msgs_humble::msg::Oburoadpoint_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace common_msgs_humble

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__OBUROADPOINT__STRUCT_HPP_
