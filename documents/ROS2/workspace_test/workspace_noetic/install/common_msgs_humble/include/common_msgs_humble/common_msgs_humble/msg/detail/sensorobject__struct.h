﻿// NOLINT: This file starts with a BOM since it contain non-ASCII characters
// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from common_msgs_humble:msg/Sensorobject.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSOROBJECT__STRUCT_H_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSOROBJECT__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'points'
#include "common_msgs_humble/msg/detail/point3d__struct.h"
// Member 'object_history'
#include "common_msgs_humble/msg/detail/objecthistory__struct.h"
// Member 'object_prediction'
#include "common_msgs_humble/msg/detail/objectprediction__struct.h"

/// Struct defined in msg/Sensorobject in the package common_msgs_humble.
typedef struct common_msgs_humble__msg__Sensorobject
{
  /// 跟踪的ID
  uint32_t id;
  /// 横坐标
  float x;
  /// 纵坐标
  float y;
  /// Z坐标
  float z;
  /// 经度
  double longtitude;
  /// 纬度
  double latitude;
  /// 高度
  double altitude;
  /// 纵轴相对速度
  float relspeedy;
  /// 横轴相对速度
  float relspeedx;
  /// 横滚角 rad
  float rollrad;
  /// 俯仰角 rad
  float pitchrad;
  /// 航向角 rad
  float azimuth;
  /// deg/s
  double pitchrate;
  /// deg/s
  double rollrate;
  /// deg/s
  double yawrate;
  /// 宽度
  float width;
  /// 长度
  float length;
  /// 高度
  float height;
  /// 类别
  uint8_t classification;
  /// Cluster 版本用于速度来源-radar
  uint8_t value;
  /// 检测置信度
  float confidence;
  /// 轮廓点数据
  common_msgs_humble__msg__Point3d__Sequence points;
  /// 驾驶意图:0-初始,1-切入
  uint8_t driving_intent;
  /// FORWARD_STATE = 0, STOPPING_STATE = 1, BRANCH_LEFT_STATE = 2, BRANCH_RIGHT_STATE = 3, YIELDING_STATE = 4, ACCELERATING_STATE = 5, SLOWDOWN_STATE = 6
  uint8_t behavior_state;
  /// 相对速度来源
  uint8_t radarindex;
  /// radar跟踪目标ID
  uint8_t radarobjectid;
  /// frenet坐标系的s
  float s;
  /// frenet坐标系的l
  float l;
  /// frenet坐标系的s方向速度
  float speeds;
  /// frenet坐标系的l方向速度
  float speedl;
  /// 障碍物相关决策
  uint8_t object_decision;
  /// 历史轨迹信息
  common_msgs_humble__msg__Objecthistory__Sequence object_history;
  /// 预测信息
  common_msgs_humble__msg__Objectprediction__Sequence object_prediction;
} common_msgs_humble__msg__Sensorobject;

// Struct for a sequence of common_msgs_humble__msg__Sensorobject.
typedef struct common_msgs_humble__msg__Sensorobject__Sequence
{
  common_msgs_humble__msg__Sensorobject * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} common_msgs_humble__msg__Sensorobject__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__SENSOROBJECT__STRUCT_H_
