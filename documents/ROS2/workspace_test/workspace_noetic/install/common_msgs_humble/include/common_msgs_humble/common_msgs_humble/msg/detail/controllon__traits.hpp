// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from common_msgs_humble:msg/Controllon.idl
// generated code does not contain a copyright notice

#ifndef COMMON_MSGS_HUMBLE__MSG__DETAIL__CONTROLLON__TRAITS_HPP_
#define COMMON_MSGS_HUMBLE__MSG__DETAIL__CONTROLLON__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "common_msgs_humble/msg/detail/controllon__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

namespace common_msgs_humble
{

namespace msg
{

inline void to_flow_style_yaml(
  const Controllon & msg,
  std::ostream & out)
{
  out << "{";
  // member: espmethod
  {
    out << "espmethod: ";
    rosidl_generator_traits::value_to_yaml(msg.espmethod, out);
    out << ", ";
  }

  // member: gpsdis
  {
    out << "gpsdis: ";
    rosidl_generator_traits::value_to_yaml(msg.gpsdis, out);
    out << ", ";
  }

  // member: epbmethod
  {
    out << "epbmethod: ";
    rosidl_generator_traits::value_to_yaml(msg.epbmethod, out);
    out << ", ";
  }

  // member: epb
  {
    out << "epb: ";
    rosidl_generator_traits::value_to_yaml(msg.epb, out);
    out << ", ";
  }

  // member: geermethod
  {
    out << "geermethod: ";
    rosidl_generator_traits::value_to_yaml(msg.geermethod, out);
    out << ", ";
  }

  // member: gear
  {
    out << "gear: ";
    rosidl_generator_traits::value_to_yaml(msg.gear, out);
    out << ", ";
  }

  // member: brakemethod
  {
    out << "brakemethod: ";
    rosidl_generator_traits::value_to_yaml(msg.brakemethod, out);
    out << ", ";
  }

  // member: brakepedal
  {
    out << "brakepedal: ";
    rosidl_generator_traits::value_to_yaml(msg.brakepedal, out);
    out << ", ";
  }

  // member: gaspedal
  {
    out << "gaspedal: ";
    rosidl_generator_traits::value_to_yaml(msg.gaspedal, out);
    out << ", ";
  }

  // member: station
  {
    out << "station: ";
    rosidl_generator_traits::value_to_yaml(msg.station, out);
    out << ", ";
  }

  // member: light
  {
    out << "light: ";
    rosidl_generator_traits::value_to_yaml(msg.light, out);
    out << ", ";
  }

  // member: pcmethod
  {
    out << "pcmethod: ";
    rosidl_generator_traits::value_to_yaml(msg.pcmethod, out);
    out << ", ";
  }

  // member: objdis
  {
    out << "objdis: ";
    rosidl_generator_traits::value_to_yaml(msg.objdis, out);
    out << ", ";
  }

  // member: objrel
  {
    out << "objrel: ";
    rosidl_generator_traits::value_to_yaml(msg.objrel, out);
    out << ", ";
  }

  // member: mode
  {
    out << "mode: ";
    rosidl_generator_traits::value_to_yaml(msg.mode, out);
    out << ", ";
  }

  // member: isvalid
  {
    out << "isvalid: ";
    rosidl_generator_traits::value_to_yaml(msg.isvalid, out);
    out << ", ";
  }

  // member: timestamp
  {
    out << "timestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.timestamp, out);
    out << ", ";
  }

  // member: targetspeed
  {
    out << "targetspeed: ";
    rosidl_generator_traits::value_to_yaml(msg.targetspeed, out);
    out << ", ";
  }

  // member: apadis
  {
    out << "apadis: ";
    rosidl_generator_traits::value_to_yaml(msg.apadis, out);
    out << ", ";
  }

  // member: objtype
  {
    out << "objtype: ";
    rosidl_generator_traits::value_to_yaml(msg.objtype, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const Controllon & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: espmethod
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "espmethod: ";
    rosidl_generator_traits::value_to_yaml(msg.espmethod, out);
    out << "\n";
  }

  // member: gpsdis
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "gpsdis: ";
    rosidl_generator_traits::value_to_yaml(msg.gpsdis, out);
    out << "\n";
  }

  // member: epbmethod
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "epbmethod: ";
    rosidl_generator_traits::value_to_yaml(msg.epbmethod, out);
    out << "\n";
  }

  // member: epb
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "epb: ";
    rosidl_generator_traits::value_to_yaml(msg.epb, out);
    out << "\n";
  }

  // member: geermethod
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "geermethod: ";
    rosidl_generator_traits::value_to_yaml(msg.geermethod, out);
    out << "\n";
  }

  // member: gear
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "gear: ";
    rosidl_generator_traits::value_to_yaml(msg.gear, out);
    out << "\n";
  }

  // member: brakemethod
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "brakemethod: ";
    rosidl_generator_traits::value_to_yaml(msg.brakemethod, out);
    out << "\n";
  }

  // member: brakepedal
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "brakepedal: ";
    rosidl_generator_traits::value_to_yaml(msg.brakepedal, out);
    out << "\n";
  }

  // member: gaspedal
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "gaspedal: ";
    rosidl_generator_traits::value_to_yaml(msg.gaspedal, out);
    out << "\n";
  }

  // member: station
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "station: ";
    rosidl_generator_traits::value_to_yaml(msg.station, out);
    out << "\n";
  }

  // member: light
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "light: ";
    rosidl_generator_traits::value_to_yaml(msg.light, out);
    out << "\n";
  }

  // member: pcmethod
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "pcmethod: ";
    rosidl_generator_traits::value_to_yaml(msg.pcmethod, out);
    out << "\n";
  }

  // member: objdis
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "objdis: ";
    rosidl_generator_traits::value_to_yaml(msg.objdis, out);
    out << "\n";
  }

  // member: objrel
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "objrel: ";
    rosidl_generator_traits::value_to_yaml(msg.objrel, out);
    out << "\n";
  }

  // member: mode
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "mode: ";
    rosidl_generator_traits::value_to_yaml(msg.mode, out);
    out << "\n";
  }

  // member: isvalid
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "isvalid: ";
    rosidl_generator_traits::value_to_yaml(msg.isvalid, out);
    out << "\n";
  }

  // member: timestamp
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "timestamp: ";
    rosidl_generator_traits::value_to_yaml(msg.timestamp, out);
    out << "\n";
  }

  // member: targetspeed
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "targetspeed: ";
    rosidl_generator_traits::value_to_yaml(msg.targetspeed, out);
    out << "\n";
  }

  // member: apadis
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "apadis: ";
    rosidl_generator_traits::value_to_yaml(msg.apadis, out);
    out << "\n";
  }

  // member: objtype
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "objtype: ";
    rosidl_generator_traits::value_to_yaml(msg.objtype, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const Controllon & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace common_msgs_humble

namespace rosidl_generator_traits
{

[[deprecated("use common_msgs_humble::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const common_msgs_humble::msg::Controllon & msg,
  std::ostream & out, size_t indentation = 0)
{
  common_msgs_humble::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use common_msgs_humble::msg::to_yaml() instead")]]
inline std::string to_yaml(const common_msgs_humble::msg::Controllon & msg)
{
  return common_msgs_humble::msg::to_yaml(msg);
}

template<>
inline const char * data_type<common_msgs_humble::msg::Controllon>()
{
  return "common_msgs_humble::msg::Controllon";
}

template<>
inline const char * name<common_msgs_humble::msg::Controllon>()
{
  return "common_msgs_humble/msg/Controllon";
}

template<>
struct has_fixed_size<common_msgs_humble::msg::Controllon>
  : std::integral_constant<bool, true> {};

template<>
struct has_bounded_size<common_msgs_humble::msg::Controllon>
  : std::integral_constant<bool, true> {};

template<>
struct is_message<common_msgs_humble::msg::Controllon>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // COMMON_MSGS_HUMBLE__MSG__DETAIL__CONTROLLON__TRAITS_HPP_
