cmake_minimum_required(VERSION 3.5)
cmake_policy(SET CMP0048 NEW)
project(common_msgs_humble)
set(CMAKE_BUILD_TYPE "Release")

set(COMPILE_METHOD COLCON)
#========================
# Project details / setup
#========================
set(PROJECT_NAME common_msgs_humble)

set(targetName send_node)

add_definitions(-DPROJECT_PATH="${PROJECT_SOURCE_DIR}")

if (CMAKE_BUILD_TYPE STREQUAL "")
  set(CMAKE_BUILD_TYPE "Release")
  add_definitions(-O3)
endif()

if($ENV{ROS_DISTRO} STREQUAL "humble")  # the ros2 humble requires c++17
add_definitions(-std=c++17)
else()
add_definitions(-std=c++14)
endif()

# add_compile_options(-Wall)


#========================
# Dependencies Setup
#========================


#ROS2#
find_package(rclcpp QUIET)

if(rclcpp_FOUND AND ${COMPILE_METHOD} STREQUAL "COLCON")

  message(=============================================================)
  message("-- ROS2 Found. ROS2 Support is turned On.")
  message(=============================================================)

  add_definitions(-DROS2_FOUND)     # 增加ROS1_FOUND的宏定义
  #查找依赖
  find_package(ament_cmake REQUIRED)
  find_package(rosidl_default_generators REQUIRED)
  find_package(std_msgs REQUIRED)

  set(CMAKE_CXX_STANDARD 14)

  # ros2   设置msg信息
  rosidl_generate_interfaces(${PROJECT_NAME}
    "msg/Actuator.msg"
    "msg/App.msg"
    "msg/Cloudpant.msg"
    "msg/Cloudpants.msg"
    "msg/Collectmap.msg"
    "msg/Collectpoint.msg"
    "msg/Controllat.msg"
    "msg/Controllon.msg"
    "msg/Decisionbehavior.msg"
    "msg/Elapsedtime.msg"
    "msg/Error.msg"
    "msg/Fusiontrackingobject.msg"
    "msg/Fusiontrackingobjects.msg"
    "msg/Intersectionroad.msg"
    "msg/Intersectionroads.msg"
    "msg/Lane.msg"
    "msg/Lanes.msg"
    "msg/Lonlat.msg"
    "msg/Lonlatmappoints.msg"
    "msg/Monitor.msg"
    "msg/Objecthistory.msg"
    "msg/Objectprediction.msg"
    "msg/Obupant.msg"
    "msg/Obupants.msg"
    "msg/Oburoadlist.msg"
    "msg/Oburoadpoint.msg"
    "msg/Planningmotion.msg"
    "msg/Point3d.msg"
    "msg/Rdcontrol.msg"
    "msg/Remotedrivestatus.msg"
    "msg/Requestmap.msg"
    "msg/Roadpoint.msg"
    "msg/Sensorcameralight.msg"
    "msg/Sensorgps.msg"
    "msg/Sensorobject.msg"
    "msg/Sensorobjects.msg"
    "msg/Sensorstatus.msg"
    "msg/Sl.msg"
    "msg/Trajectorypoints.msg"
    "msg/V2xapp.msg"
    "msg/Obulight.msg"
    "msg/Obutrafficlights.msg"
    "msg/Pullover.msg"
    "msg/Mapformat.msg"
    "msg/Pointformat.msg"
    "msg/Padtohd.msg"
    "msg/Hdintersectiontoglobal.msg"
    "msg/Hdintersectionstoglobal.msg"
    "msg/Hdroute.msg"
    "msg/Hdmap.msg"
    "msg/Hdroutestopad.msg"
    "msg/Hdroutetoglobal.msg"
    "msg/Hdstoppointstoglobal.msg"
    "msg/ParkingActive.msg"
    "msg/Ieku.msg"
    DEPENDENCIES std_msgs
  )


  
           

else(rclcpp_FOUND AND ${COMPILE_METHOD} STREQUAL "COLCON")

  message(=============================================================)
  message("-- ROS2 Not Found. ROS2 Support is turned Off.")
  message(=============================================================)

endif(rclcpp_FOUND AND ${COMPILE_METHOD} STREQUAL "COLCON")



#Ros2#
if(rclcpp_FOUND AND ${COMPILE_METHOD} STREQUAL "COLCON")


    # 安装配置
    install(
      DIRECTORY msg/
      DESTINATION share/${PROJECT_NAME}/msg
    )

    # 导出包含目录
    ament_export_include_directories(include)

    # 导出依赖
    ament_export_dependencies(rosidl_default_runtime)
    ament_export_dependencies(std_msgs)  

  ament_package()

endif(rclcpp_FOUND AND ${COMPILE_METHOD} STREQUAL "COLCON")



