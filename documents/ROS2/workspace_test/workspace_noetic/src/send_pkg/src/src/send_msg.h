#ifdef ROS1_FOUND
#include "commonmsgs_noetic.h"
#endif

#ifdef ROS2_FOUND
#include "commonmsgs_humble.h"
#endif
class send_msg
{
private:
#ifdef ROS2_FOUND
    rclcpp::Publisher<common_msgs_humble::msg::Actuator>::SharedPtr pub_;
    rclcpp::TimerBase::SharedPtr timer_;
    rclcpp::Node::SharedPtr m_node ;
#endif

#ifdef ROS1_FOUND
    ros::Publisher pub_noetic;
#endif

public:
    void sendFun();

#ifdef ROS2_FOUND    // humble public fun 定义
    send_msg(rclcpp::Node::SharedPtr node);

    void ros_humble_send();
    
#endif
    
#ifdef ROS1_FOUND // noetic public fun 定义
send_msg(ros::NodeHandle nh);
void ros_noetic_send();
#endif

    ~send_msg();
};










