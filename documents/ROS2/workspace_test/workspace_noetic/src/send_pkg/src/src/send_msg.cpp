#include "send_msg.h"

#ifdef ROS2_FOUND
send_msg::send_msg(rclcpp::Node::SharedPtr node)
{
    m_node = node;
    rclcpp::PublisherOptions options;
    options.use_intra_process_comm = rclcpp::IntraProcessSetting::Enable;


      // 3. 创建QoS并启用优化
    auto qos = rclcpp::QoS(10).reliable();
    qos.avoid_ros_namespace_conventions(true);  // 关键优化
    pub_ = m_node->create_publisher<common_msgs_humble::msg::Actuator>("shm_topic", qos);    
    // 此函数是定时器，暂时用线程代替保持与ros1 调用一致
//    timer_ = create_wall_timer(
//      std::chrono::seconds(1),
//      [this]() {
//
//      });

    auto l_tmp = std::thread(&send_msg::sendFun, this);
	l_tmp.detach();
}

void send_msg::ros_humble_send()
{
    rclcpp::Rate rate(10);
    while (rclcpp::ok())
    {
        auto loaned_msg = pub_->borrow_loaned_message();
        auto& l_actuator = loaned_msg.get();

        // 分配 1MB 共享内存
        //data.resize(1024*1024);

        // 填充数据（实际应用避免此操作）
        static uint8_t counter = 0;
        l_actuator.speed = counter;
        counter++;
        //std::fill(data.begin(), data.end(), counter++);

        pub_->publish(std::move(loaned_msg));
        RCLCPP_INFO(m_node->get_logger(), "Published actuator via SHM %f",l_actuator.speed);
       rate.sleep();
    }
}

#endif

#ifdef ROS1_FOUND
send_msg::send_msg(ros::NodeHandle nh)
{
    pub_noetic = nh.advertise<common_msgs::actuator>("/actuator_noetic", 10);
    auto l_tmp = std::thread(&send_msg::sendFun, this);
	l_tmp.detach();
}
void send_msg::ros_noetic_send()
{    
    ros::Rate rate(10);
    static int l_count = 0;
    while (ros::ok())
    {
        common_msgs::actuator l_actuator;
        l_actuator.speed = l_count;
        l_count++;
        pub_noetic.publish(l_actuator);
        rate.sleep();

    }
   

}

#endif



void send_msg::sendFun()
{
#ifdef ROS1_FOUND
    ros_noetic_send();
#endif

#ifdef ROS2_FOUND
    ros_humble_send();
#endif
}

send_msg::~send_msg()
{
}


