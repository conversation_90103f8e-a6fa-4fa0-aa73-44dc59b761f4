cmake_minimum_required(VERSION 2.8.3)
project(cloud_msgs)
find_package(roscpp 1.12 QUIET)
if(roscpp_FOUND)
find_package(catkin REQUIRED COMPONENTS
  roscpp
  rospy
  std_msgs
  sensor_msgs
  common_msgs
  message_generation
  
)


 add_message_files(
    DIRECTORY msg
    FILES

    trajPoint.msg
    laneTrajectory.msg
    laneLevelStatistics.msg
    lanelist.msg
    position.msg
    speedLimit.msg
    latlonPoint.msg
    riskAreaWarn.msg
    riskAreaList.msg
    trajPoint.msg
    connectingLane.msg
    connection.msg
    lane.msg
    laneAttributes.msg
    laneLevelStatistics.msg
    link.msg
    
    movement.msg
    node.msg
    nodeReferenceID.msg
    mapInfo.msg
 )


generate_messages(
  DEPENDENCIES 
  std_msgs 
  common_msgs)

catkin_package(
  CATKIN_DEPENDS 
  message_runtime 
  std_msgs
  common_msgs
)

endif(roscpp_FOUND)