# 3 light
uint8 lefttype        #左转灯类型 0无灯或黑灯 1 红灯 2绿灯
uint8 leftisvalid     #左转灯是否有效
uint8 leftnumber      #预留
float32  leftvalue    #预留          

uint8 midtype         #直转灯类型 0无灯或黑灯 1 红灯 2绿灯
uint8 midisvalid      #直转灯是否有效
uint8 midnumber       #预留
float32  midvalue     #预留

uint8 righttype      #右转灯类型 0无灯或黑灯 1 红灯 2绿灯
uint8 rightisvalid   #右转灯是否有效
uint8 rightnumber    #预留
float32  rightvalue  #预留  

int64 timestamp
