int64 timestamp   # 时间戳  123
uint8 stage       #  0  1  2(默认读取planningmotion 话题的轨迹信息)  3(停止)    (泊车不主动发消息,inter需要提供驶出功能)  4(泊出)    5:默认
uint8 tips        # 1:请求   0:默认             ---stage0
uint8 answer      # 1进入 2不进入 0不处理        ---stage0
ieku[]  iekulist  # 车库信息                    ---stage1
uint8  iekutargetid                           #---stage1
uint8 driveout    #  0:不处理，  1 驶出        ---stage3      --------> 规划 stage发4(泊出)
uint8 stopParking
