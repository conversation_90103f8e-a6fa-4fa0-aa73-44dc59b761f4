uint8 drivebehavior     # 驾驶场景,默认1
sensorobject[] obs      # 影响驾驶的障碍物,车辆坐标系右前上
uint8 isvalid           # 有效位
uint8 turnlights        # 转向灯,0-无/1-左/2-右/3-双闪
uint8 laneblock         # 异常类型,默认0
uint8 door              # 所在车道,默认0
int64 timestamp         # 时间戳(同当帧gps时间戳)
uint8 mergetrigger      # nan
float32 guidespeed      # 期望速度,m/s
uint8 avoidsituation    # nan
uint8 alert             # 预测结果有效标志位,默认0
float32 deviation       # 偏离参考线距离
float32 starttime       # nan
float32 endtime         # nan
uint8 carworkstatus     # 车辆行使方向,默认0
bool StationBlock       # 动态巡航触发标志位,默认0
uint8 needreplan        # nan
uint8 virtualpointtype  # 异常显示文字类型,默认0
