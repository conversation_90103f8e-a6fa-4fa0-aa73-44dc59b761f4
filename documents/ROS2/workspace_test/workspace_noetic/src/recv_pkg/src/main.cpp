#include "recv_msg.h"

int main(int argc, char * argv[]) {
#ifdef ROS2_FOUND 
  rclcpp::init(argc, argv);

  auto node = rclcpp::Node::make_shared("subscribe_node");
  auto l_recv_msg = new recv_msg(node);
  //std::make_shared<recv_msg>(node);
  rclcpp::spin(node);
  rclcpp::shutdown();
#endif


#ifdef ROS1_FOUND 
  ros::init(argc,argv,"subscribe_node_noetic");
  ros::NodeHandle nh;
  recv_msg node(nh);
  ros::spin();
#endif
  return 0;
}