#include "recv_msg.h"
#ifdef ROS2_FOUND
recv_msg::recv_msg(rclcpp::Node::SharedPtr node)
{
    m_node = node;
    auto qos = rclcpp::QoS(10).reliable();
    qos.avoid_ros_namespace_conventions(true);
    sub_ = m_node->create_subscription<common_msgs_humble::msg::Actuator>("shm_topic", qos, 
    //std::bind(&recv_msg::humble_callback, this, std::placeholders::_1));
    [this](const common_msgs_humble::msg::Actuator& msg) {
        this->humble_callback(msg);
      });

}



void recv_msg::humble_callback(const common_msgs_humble::msg::Actuator& msg)
{
    RCLCPP_INFO(m_node->get_logger(), "Received %f bytes via SHM", msg.speed);
    std::cout<<"recv speed--->"<< msg.speed<<std::endl;
}
#endif

#ifdef ROS1_FOUND

recv_msg::recv_msg(ros::NodeHandle nh)
{
    m_ActuatorSuber = nh.subscribe("/actuator_noetic", 1000, &recv_msg::noetic_callback, this);   //ros订阅

}


void recv_msg::noetic_callback(const common_msgs::actuator::ConstPtr& msg)
{
    std::cout<<"noetic recv actuator speed:"<<msg->speed<<std::endl;
}
#endif

recv_msg::~recv_msg()
{
}