#include <chrono>
#include <memory>
#ifdef ROS2_FOUND
#include "commonmsgs_humble.h"
#endif
#ifdef ROS1_FOUND
#include "commonmsgs_noetic.h"
#endif
class recv_msg
{
private:
#ifdef ROS2_FOUND
    rclcpp::Node::SharedPtr m_node ;
    rclcpp::Subscription<common_msgs_humble::msg::Actuator>::SharedPtr sub_;
#endif

#ifdef ROS1_FOUND
     ros::Subscriber m_ActuatorSuber;
#endif
public:
#ifdef ROS1_FOUND
    recv_msg(ros::NodeHandle nh);
    void noetic_callback(const common_msgs::actuator::ConstPtr& msg);
#endif


#ifdef ROS2_FOUND
    recv_msg(rclcpp::Node::SharedPtr node);
    void humble_callback(const common_msgs_humble::msg::Actuator& msg);
#endif
    ~recv_msg();
};

