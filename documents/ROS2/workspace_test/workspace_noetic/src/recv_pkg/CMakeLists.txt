cmake_minimum_required(VERSION 3.5)
#cmake_policy(SET CMP0048 NEW)   #ros2 humble 打开此注释
project(workspace_recv)
#set(CMAKE_BUILD_TYPE "Release")

#========================
# ros2 humble 
#========================
#set(COMPILE_METHOD COLCON)


#========================
# ros1 noetic
#========================
set(COMPILE_METHOD CATKIN)
#========================
# Project details / setup
#========================
set(PROJECT_NAME workspace_recv)

set(targetName recv_node)

add_definitions(-DPROJECT_PATH="${PROJECT_SOURCE_DIR}")

if (CMAKE_BUILD_TYPE STREQUAL "")
  set(CMAKE_BUILD_TYPE "Release")
  add_definitions(-O3)
endif()

if($ENV{ROS_DISTRO} STREQUAL "humble")  # the ros2 humble requires c++17
add_definitions(-std=c++17)
else()
add_definitions(-std=c++14)
endif()

# add_compile_options(-Wall)


#========================
# Dependencies Setup
#========================

#ROS#
find_package(roscpp 1.12 QUIET)

if(roscpp_FOUND)

  message(=============================================================)
  message("-- ROS Found. ROS Support is turned On.")
  message(=============================================================)

  add_definitions(-DROS1_FOUND)  # 增加ROS1_FOUND的宏定义

  find_package(roslib QUIET)
  include_directories(${roscpp_INCLUDE_DIRS} ${roslib_INCLUDE_DIRS})
  set(ROS_LIBS ${roscpp_LIBRARIES} ${roslib_LIBRARIES})

  #Catkin#
  if(${COMPILE_METHOD} STREQUAL "CATKIN")

    add_definitions(-DRUN_IN_ROS_WORKSPACE)

    find_package(catkin REQUIRED COMPONENTS
      roscpp
      sensor_msgs
      roslib
      common_msgs)

    catkin_package(CATKIN_DEPENDS 
      sensor_msgs 
      roslib)

  endif(${COMPILE_METHOD} STREQUAL "CATKIN")
  include_directories(
    # include
      ${catkin_INCLUDE_DIRS}
    )
else(roscpp_FOUND)

  message(=============================================================)
  message("-- ROS Not Found. ROS Support is turned Off.")
  message(=============================================================)

endif(roscpp_FOUND)

#ROS2#
find_package(rclcpp QUIET)

if(rclcpp_FOUND AND ${COMPILE_METHOD} STREQUAL "COLCON")

  message(=============================================================)
  message("-- ROS2 Found. ROS2 Support is turned On.")
  message(=============================================================)

  add_definitions(-DROS2_FOUND)     # 增加ROS1_FOUND的宏定义
  include_directories(${rclcpp_INCLUDE_DIRS})
  set(CMAKE_CXX_STANDARD 14)



  
  find_package(ament_cmake REQUIRED)
  find_package(sensor_msgs REQUIRED)
  find_package(std_msgs REQUIRED)         
  find_package(common_msgs_humble REQUIRED)  

else(rclcpp_FOUND AND ${COMPILE_METHOD} STREQUAL "COLCON")

  message(=============================================================)
  message("-- ROS2 Not Found. ROS2 Support is turned Off.")
  message(=============================================================)

endif(rclcpp_FOUND AND ${COMPILE_METHOD} STREQUAL "COLCON")

#Others#
find_package(yaml-cpp REQUIRED)

#Include directory#
include_directories(${PROJECT_SOURCE_DIR}/src
${PROJECT_SOURCE_DIR}/../common_include)

message("common_include",${PROJECT_SOURCE_DIR})

#========================
# Build Setup
#========================

add_executable(${targetName}
               src/recv_msg.cpp
               src/main.cpp)

#target_link_libraries(workspace_recv)

#Ros#
if(roscpp_FOUND)

  target_link_libraries(${targetName} 
    ${ROS_LIBS})

  if(${COMPILE_METHOD} STREQUAL "CATKIN")

    install(TARGETS ${targetName}
            RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
            LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
            ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION})
    install(DIRECTORY launch/
        DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/launch
    )

    install(DIRECTORY config/
        DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/config
    )
    install(DIRECTORY rviz/
        DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/rviz
    )

  endif()

endif(roscpp_FOUND)

#Ros2#
if(rclcpp_FOUND AND ${COMPILE_METHOD} STREQUAL "COLCON")

  ament_target_dependencies(${targetName} 
    rclcpp 
    std_msgs 
    sensor_msgs
    common_msgs_humble)

  install(TARGETS
  ${targetName} 
    DESTINATION lib/${PROJECT_NAME})

  install(DIRECTORY
    #launch
    #rviz
    DESTINATION share/${PROJECT_NAME})

  ament_package()

endif(rclcpp_FOUND AND ${COMPILE_METHOD} STREQUAL "COLCON")



