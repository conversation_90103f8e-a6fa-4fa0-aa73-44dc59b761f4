[0.369s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.369s] DEBUG:colcon:Parsed command line arguments: Namespace(allow_overriding=[], ament_cmake_args=None, base_paths=['.'], build_base='build', catkin_cmake_args=None, catkin_skip_building_tests=False, cmake_args=None, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, cmake_target=None, cmake_target_skip_unavailable=False, continue_on_error=False, event_handlers=None, executor='parallel', ignore_user_meta=False, install_base='install', log_base=None, log_level=None, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7fe6ef3e5070>>, merge_install=False, metas=['./colcon.meta'], mixin=None, mixin_files=None, mixin_verb=('build',), packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_end=None, packages_ignore=None, packages_ignore_regex=None, packages_select=None, packages_select_build_failed=False, packages_select_by_dep=None, packages_select_regex=None, packages_select_test_failures=False, packages_skip=None, packages_skip_build_finished=False, packages_skip_by_dep=None, packages_skip_regex=None, packages_skip_test_passed=False, packages_skip_up_to=None, packages_start=None, packages_up_to=None, packages_up_to_regex=None, parallel_workers=12, paths=None, symlink_install=False, test_result_base=None, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7fe6ef3e5070>, verb_name='build', verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x7fe6ef3e5910>)
[0.654s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.654s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.654s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.654s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.654s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.654s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.654s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic'
[0.654s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.654s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.654s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.654s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.654s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.654s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.654s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.654s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.654s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.665s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.665s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.665s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.665s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.665s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.665s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.665s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.666s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.666s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.666s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.666s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.666s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.666s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.666s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.666s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.666s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.666s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.666s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.666s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.666s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.666s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.666s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.666s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.666s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.666s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.666s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.666s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.666s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.667s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_include) by extensions ['ignore', 'ignore_ament_install']
[0.667s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_include) by extension 'ignore'
[0.667s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_include) by extension 'ignore_ament_install'
[0.667s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_include) by extensions ['colcon_pkg']
[0.667s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_include) by extension 'colcon_pkg'
[0.667s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_include) by extensions ['colcon_meta']
[0.667s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_include) by extension 'colcon_meta'
[0.667s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_include) by extensions ['ros']
[0.667s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_include) by extension 'ros'
[0.667s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_include) by extensions ['cmake', 'python']
[0.667s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_include) by extension 'cmake'
[0.667s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_include) by extension 'python'
[0.667s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_include) by extensions ['python_setup_py']
[0.667s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_include) by extension 'python_setup_py'
[0.667s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_msgs_humble) by extensions ['ignore', 'ignore_ament_install']
[0.667s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_msgs_humble) by extension 'ignore'
[0.667s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_msgs_humble) by extension 'ignore_ament_install'
[0.667s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_msgs_humble) by extensions ['colcon_pkg']
[0.667s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_msgs_humble) by extension 'colcon_pkg'
[0.667s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_msgs_humble) by extensions ['colcon_meta']
[0.667s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_msgs_humble) by extension 'colcon_meta'
[0.667s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_msgs_humble) by extensions ['ros']
[0.667s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_msgs_humble) by extension 'ros'
[0.669s] DEBUG:colcon.colcon_core.package_identification:Package 'src/common_msgs_humble' with type 'ros.catkin' and name 'common_msgs_humble'
[0.670s] Level 1:colcon.colcon_core.package_identification:_identify(src/msgs) by extensions ['ignore', 'ignore_ament_install']
[0.670s] Level 1:colcon.colcon_core.package_identification:_identify(src/msgs) by extension 'ignore'
[0.670s] Level 1:colcon.colcon_core.package_identification:_identify(src/msgs) by extension 'ignore_ament_install'
[0.670s] Level 1:colcon.colcon_core.package_identification:_identify(src/msgs) by extensions ['colcon_pkg']
[0.670s] Level 1:colcon.colcon_core.package_identification:_identify(src/msgs) by extension 'colcon_pkg'
[0.670s] Level 1:colcon.colcon_core.package_identification:_identify(src/msgs) by extensions ['colcon_meta']
[0.670s] Level 1:colcon.colcon_core.package_identification:_identify(src/msgs) by extension 'colcon_meta'
[0.670s] Level 1:colcon.colcon_core.package_identification:_identify(src/msgs) by extensions ['ros']
[0.670s] Level 1:colcon.colcon_core.package_identification:_identify(src/msgs) by extension 'ros'
[0.670s] Level 1:colcon.colcon_core.package_identification:_identify(src/msgs) by extensions ['cmake', 'python']
[0.670s] Level 1:colcon.colcon_core.package_identification:_identify(src/msgs) by extension 'cmake'
[0.670s] Level 1:colcon.colcon_core.package_identification:_identify(src/msgs) by extension 'python'
[0.670s] Level 1:colcon.colcon_core.package_identification:_identify(src/msgs) by extensions ['python_setup_py']
[0.670s] Level 1:colcon.colcon_core.package_identification:_identify(src/msgs) by extension 'python_setup_py'
[0.670s] Level 1:colcon.colcon_core.package_identification:_identify(src/msgs/cloud_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.670s] Level 1:colcon.colcon_core.package_identification:_identify(src/msgs/cloud_msgs) by extension 'ignore'
[0.670s] Level 1:colcon.colcon_core.package_identification:_identify(src/msgs/cloud_msgs) by extension 'ignore_ament_install'
[0.670s] Level 1:colcon.colcon_core.package_identification:_identify(src/msgs/cloud_msgs) by extensions ['colcon_pkg']
[0.670s] Level 1:colcon.colcon_core.package_identification:_identify(src/msgs/cloud_msgs) by extension 'colcon_pkg'
[0.670s] Level 1:colcon.colcon_core.package_identification:_identify(src/msgs/cloud_msgs) by extensions ['colcon_meta']
[0.670s] Level 1:colcon.colcon_core.package_identification:_identify(src/msgs/cloud_msgs) by extension 'colcon_meta'
[0.670s] Level 1:colcon.colcon_core.package_identification:_identify(src/msgs/cloud_msgs) by extensions ['ros']
[0.671s] Level 1:colcon.colcon_core.package_identification:_identify(src/msgs/cloud_msgs) by extension 'ros'
[0.671s] DEBUG:colcon.colcon_core.package_identification:Package 'src/msgs/cloud_msgs' with type 'ros.catkin' and name 'cloud_msgs'
[0.671s] Level 1:colcon.colcon_core.package_identification:_identify(src/msgs/common_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.671s] Level 1:colcon.colcon_core.package_identification:_identify(src/msgs/common_msgs) by extension 'ignore'
[0.671s] Level 1:colcon.colcon_core.package_identification:_identify(src/msgs/common_msgs) by extension 'ignore_ament_install'
[0.671s] Level 1:colcon.colcon_core.package_identification:_identify(src/msgs/common_msgs) by extensions ['colcon_pkg']
[0.672s] Level 1:colcon.colcon_core.package_identification:_identify(src/msgs/common_msgs) by extension 'colcon_pkg'
[0.672s] Level 1:colcon.colcon_core.package_identification:_identify(src/msgs/common_msgs) by extensions ['colcon_meta']
[0.672s] Level 1:colcon.colcon_core.package_identification:_identify(src/msgs/common_msgs) by extension 'colcon_meta'
[0.672s] Level 1:colcon.colcon_core.package_identification:_identify(src/msgs/common_msgs) by extensions ['ros']
[0.672s] Level 1:colcon.colcon_core.package_identification:_identify(src/msgs/common_msgs) by extension 'ros'
[0.672s] DEBUG:colcon.colcon_core.package_identification:Package 'src/msgs/common_msgs' with type 'ros.catkin' and name 'common_msgs'
[0.672s] Level 1:colcon.colcon_core.package_identification:_identify(src/recv_pkg) by extensions ['ignore', 'ignore_ament_install']
[0.673s] Level 1:colcon.colcon_core.package_identification:_identify(src/recv_pkg) by extension 'ignore'
[0.673s] Level 1:colcon.colcon_core.package_identification:_identify(src/recv_pkg) by extension 'ignore_ament_install'
[0.673s] Level 1:colcon.colcon_core.package_identification:_identify(src/recv_pkg) by extensions ['colcon_pkg']
[0.673s] Level 1:colcon.colcon_core.package_identification:_identify(src/recv_pkg) by extension 'colcon_pkg'
[0.673s] Level 1:colcon.colcon_core.package_identification:_identify(src/recv_pkg) by extensions ['colcon_meta']
[0.673s] Level 1:colcon.colcon_core.package_identification:_identify(src/recv_pkg) by extension 'colcon_meta'
[0.673s] Level 1:colcon.colcon_core.package_identification:_identify(src/recv_pkg) by extensions ['ros']
[0.673s] Level 1:colcon.colcon_core.package_identification:_identify(src/recv_pkg) by extension 'ros'
[0.673s] DEBUG:colcon.colcon_core.package_identification:Package 'src/recv_pkg' with type 'ros.catkin' and name 'workspace_recv'
[0.673s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_pkg) by extensions ['ignore', 'ignore_ament_install']
[0.674s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_pkg) by extension 'ignore'
[0.674s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_pkg) by extension 'ignore_ament_install'
[0.674s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_pkg) by extensions ['colcon_pkg']
[0.674s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_pkg) by extension 'colcon_pkg'
[0.674s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_pkg) by extensions ['colcon_meta']
[0.674s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_pkg) by extension 'colcon_meta'
[0.674s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_pkg) by extensions ['ros']
[0.674s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_pkg) by extension 'ros'
[0.674s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_pkg) by extensions ['cmake', 'python']
[0.674s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_pkg) by extension 'cmake'
[0.674s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_pkg) by extension 'python'
[0.674s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_pkg) by extensions ['python_setup_py']
[0.674s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_pkg) by extension 'python_setup_py'
[0.674s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_pkg/src) by extensions ['ignore', 'ignore_ament_install']
[0.674s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_pkg/src) by extension 'ignore'
[0.674s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_pkg/src) by extension 'ignore_ament_install'
[0.674s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_pkg/src) by extensions ['colcon_pkg']
[0.674s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_pkg/src) by extension 'colcon_pkg'
[0.674s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_pkg/src) by extensions ['colcon_meta']
[0.674s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_pkg/src) by extension 'colcon_meta'
[0.674s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_pkg/src) by extensions ['ros']
[0.674s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_pkg/src) by extension 'ros'
[0.675s] DEBUG:colcon.colcon_core.package_identification:Package 'src/send_pkg/src' with type 'ros.ament_cmake' and name 'workspace_send'
[0.675s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.675s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.675s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.675s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.675s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.706s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.706s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.725s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 346 installed packages in /home/<USER>/ros2_humble/install
[0.727s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /home/<USER>/ros2_humble/install/orocos_kdl_vendor
[0.728s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /opt/ros/noetic
[0.728s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.754s] Level 5:colcon.colcon_core.verb:set package 'common_msgs' build argument 'cmake_args' from command line to 'None'
[0.754s] Level 5:colcon.colcon_core.verb:set package 'common_msgs' build argument 'cmake_target' from command line to 'None'
[0.754s] Level 5:colcon.colcon_core.verb:set package 'common_msgs' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.754s] Level 5:colcon.colcon_core.verb:set package 'common_msgs' build argument 'cmake_clean_cache' from command line to 'False'
[0.754s] Level 5:colcon.colcon_core.verb:set package 'common_msgs' build argument 'cmake_clean_first' from command line to 'False'
[0.754s] Level 5:colcon.colcon_core.verb:set package 'common_msgs' build argument 'cmake_force_configure' from command line to 'False'
[0.754s] Level 5:colcon.colcon_core.verb:set package 'common_msgs' build argument 'ament_cmake_args' from command line to 'None'
[0.754s] Level 5:colcon.colcon_core.verb:set package 'common_msgs' build argument 'catkin_cmake_args' from command line to 'None'
[0.754s] Level 5:colcon.colcon_core.verb:set package 'common_msgs' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.754s] DEBUG:colcon.colcon_core.verb:Building package 'common_msgs' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/common_msgs', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs', 'merge_install': False, 'path': '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/src/msgs/common_msgs', 'symlink_install': False, 'test_result_base': None}
[0.754s] Level 5:colcon.colcon_core.verb:set package 'common_msgs_humble' build argument 'cmake_args' from command line to 'None'
[0.754s] Level 5:colcon.colcon_core.verb:set package 'common_msgs_humble' build argument 'cmake_target' from command line to 'None'
[0.754s] Level 5:colcon.colcon_core.verb:set package 'common_msgs_humble' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.754s] Level 5:colcon.colcon_core.verb:set package 'common_msgs_humble' build argument 'cmake_clean_cache' from command line to 'False'
[0.754s] Level 5:colcon.colcon_core.verb:set package 'common_msgs_humble' build argument 'cmake_clean_first' from command line to 'False'
[0.754s] Level 5:colcon.colcon_core.verb:set package 'common_msgs_humble' build argument 'cmake_force_configure' from command line to 'False'
[0.755s] Level 5:colcon.colcon_core.verb:set package 'common_msgs_humble' build argument 'ament_cmake_args' from command line to 'None'
[0.755s] Level 5:colcon.colcon_core.verb:set package 'common_msgs_humble' build argument 'catkin_cmake_args' from command line to 'None'
[0.755s] Level 5:colcon.colcon_core.verb:set package 'common_msgs_humble' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.755s] DEBUG:colcon.colcon_core.verb:Building package 'common_msgs_humble' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/common_msgs_humble', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble', 'merge_install': False, 'path': '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/src/common_msgs_humble', 'symlink_install': False, 'test_result_base': None}
[0.755s] Level 5:colcon.colcon_core.verb:set package 'workspace_recv' build argument 'cmake_args' from command line to 'None'
[0.755s] Level 5:colcon.colcon_core.verb:set package 'workspace_recv' build argument 'cmake_target' from command line to 'None'
[0.755s] Level 5:colcon.colcon_core.verb:set package 'workspace_recv' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.755s] Level 5:colcon.colcon_core.verb:set package 'workspace_recv' build argument 'cmake_clean_cache' from command line to 'False'
[0.755s] Level 5:colcon.colcon_core.verb:set package 'workspace_recv' build argument 'cmake_clean_first' from command line to 'False'
[0.755s] Level 5:colcon.colcon_core.verb:set package 'workspace_recv' build argument 'cmake_force_configure' from command line to 'False'
[0.755s] Level 5:colcon.colcon_core.verb:set package 'workspace_recv' build argument 'ament_cmake_args' from command line to 'None'
[0.755s] Level 5:colcon.colcon_core.verb:set package 'workspace_recv' build argument 'catkin_cmake_args' from command line to 'None'
[0.755s] Level 5:colcon.colcon_core.verb:set package 'workspace_recv' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.755s] DEBUG:colcon.colcon_core.verb:Building package 'workspace_recv' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/workspace_recv', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/workspace_recv', 'merge_install': False, 'path': '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/src/recv_pkg', 'symlink_install': False, 'test_result_base': None}
[0.755s] Level 5:colcon.colcon_core.verb:set package 'workspace_send' build argument 'cmake_args' from command line to 'None'
[0.755s] Level 5:colcon.colcon_core.verb:set package 'workspace_send' build argument 'cmake_target' from command line to 'None'
[0.755s] Level 5:colcon.colcon_core.verb:set package 'workspace_send' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.755s] Level 5:colcon.colcon_core.verb:set package 'workspace_send' build argument 'cmake_clean_cache' from command line to 'False'
[0.755s] Level 5:colcon.colcon_core.verb:set package 'workspace_send' build argument 'cmake_clean_first' from command line to 'False'
[0.755s] Level 5:colcon.colcon_core.verb:set package 'workspace_send' build argument 'cmake_force_configure' from command line to 'False'
[0.755s] Level 5:colcon.colcon_core.verb:set package 'workspace_send' build argument 'ament_cmake_args' from command line to 'None'
[0.756s] Level 5:colcon.colcon_core.verb:set package 'workspace_send' build argument 'catkin_cmake_args' from command line to 'None'
[0.756s] Level 5:colcon.colcon_core.verb:set package 'workspace_send' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.756s] DEBUG:colcon.colcon_core.verb:Building package 'workspace_send' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/workspace_send', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/workspace_send', 'merge_install': False, 'path': '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/src/send_pkg/src', 'symlink_install': False, 'test_result_base': None}
[0.756s] Level 5:colcon.colcon_core.verb:set package 'cloud_msgs' build argument 'cmake_args' from command line to 'None'
[0.756s] Level 5:colcon.colcon_core.verb:set package 'cloud_msgs' build argument 'cmake_target' from command line to 'None'
[0.756s] Level 5:colcon.colcon_core.verb:set package 'cloud_msgs' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.756s] Level 5:colcon.colcon_core.verb:set package 'cloud_msgs' build argument 'cmake_clean_cache' from command line to 'False'
[0.756s] Level 5:colcon.colcon_core.verb:set package 'cloud_msgs' build argument 'cmake_clean_first' from command line to 'False'
[0.756s] Level 5:colcon.colcon_core.verb:set package 'cloud_msgs' build argument 'cmake_force_configure' from command line to 'False'
[0.756s] Level 5:colcon.colcon_core.verb:set package 'cloud_msgs' build argument 'ament_cmake_args' from command line to 'None'
[0.756s] Level 5:colcon.colcon_core.verb:set package 'cloud_msgs' build argument 'catkin_cmake_args' from command line to 'None'
[0.756s] Level 5:colcon.colcon_core.verb:set package 'cloud_msgs' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.756s] DEBUG:colcon.colcon_core.verb:Building package 'cloud_msgs' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/cloud_msgs', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/cloud_msgs', 'merge_install': False, 'path': '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/src/msgs/cloud_msgs', 'symlink_install': False, 'test_result_base': None}
[0.756s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.757s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.757s] INFO:colcon.colcon_ros.task.catkin.build:Building ROS package in '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/src/msgs/common_msgs' with build type 'catkin'
[0.757s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/src/msgs/common_msgs'
[0.762s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.762s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.762s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.783s] INFO:colcon.colcon_ros.task.catkin.build:Building ROS package in '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/src/common_msgs_humble' with build type 'catkin'
[0.783s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/src/common_msgs_humble'
[0.784s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.784s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.805s] INFO:colcon.colcon_ros.task.catkin.build:Building ROS package in '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/src/recv_pkg' with build type 'catkin'
[0.805s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/src/recv_pkg'
[0.806s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.806s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.828s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/src/send_pkg/src' with build type 'ament_cmake'
[0.829s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/src/send_pkg/src'
[0.829s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.829s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.854s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/common_msgs': /usr/bin/cmake /mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/src/msgs/common_msgs -DCATKIN_INSTALL_INTO_PREFIX_ROOT=0 -DCMAKE_INSTALL_PREFIX=/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs
[0.866s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/common_msgs_humble': /usr/bin/cmake /mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/src/common_msgs_humble -DCATKIN_INSTALL_INTO_PREFIX_ROOT=0 -DCMAKE_INSTALL_PREFIX=/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble
[0.870s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/workspace_recv': /usr/bin/cmake /mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/src/recv_pkg -DCATKIN_INSTALL_INTO_PREFIX_ROOT=0 -DCMAKE_INSTALL_PREFIX=/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/workspace_recv
[0.922s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/workspace_send': CMAKE_PREFIX_PATH=/home/<USER>/ros2_humble/install/rosbag2_storage_mcap:/home/<USER>/ros2_humble/install/rosbag2:/home/<USER>/ros2_humble/install/rosbag2_compression_zstd:/home/<USER>/ros2_humble/install/mcap_vendor:/home/<USER>/ros2_humble/install/zstd_vendor:/home/<USER>/ros2_humble/install/rviz_visual_testing_framework:/home/<USER>/ros2_humble/install/rviz2:/home/<USER>/ros2_humble/install/rviz_default_plugins:/home/<USER>/ros2_humble/install/rviz_common:/home/<USER>/ros2_humble/install/rosbag2_py:/home/<USER>/ros2_humble/install/rosbag2_transport:/home/<USER>/ros2_humble/install/rosbag2_storage_default_plugins:/home/<USER>/ros2_humble/install/rosbag2_performance_benchmarking:/home/<USER>/ros2_humble/install/rosbag2_compression:/home/<USER>/ros2_humble/install/rosbag2_cpp:/home/<USER>/ros2_humble/install/rosbag2_storage:/home/<USER>/ros2_humble/install/image_common:/home/<USER>/ros2_humble/install/camera_info_manager:/home/<USER>/ros2_humble/install/camera_calibration_parsers:/home/<USER>/ros2_humble/install/yaml_cpp_vendor:/home/<USER>/ros2_humble/install/interactive_markers:/home/<USER>/ros2_humble/install/common_interfaces:/home/<USER>/ros2_humble/install/visualization_msgs:/home/<USER>/ros2_humble/install/dummy_robot_bringup:/home/<USER>/ros2_humble/install/robot_state_publisher:/home/<USER>/ros2_humble/install/kdl_parser:/home/<USER>/ros2_humble/install/urdf:/home/<USER>/ros2_humble/install/urdfdom:/home/<USER>/ros2_humble/install/urdf_parser_plugin:/home/<USER>/ros2_humble/install/urdfdom_headers:/home/<USER>/ros2_humble/install/turtlesim:/home/<USER>/ros2_humble/install/geometry2:/home/<USER>/ros2_humble/install/tf2_sensor_msgs:/home/<USER>/ros2_humble/install/test_tf2:/home/<USER>/ros2_humble/install/tf2_kdl:/home/<USER>/ros2_humble/install/tf2_geometry_msgs:/home/<USER>/ros2_humble/install/tf2_eigen:/home/<USER>/ros2_humble/install/tf2_bullet:/home/<USER>/ros2_humble/install/tf2_ros:/home/<USER>/ros2_humble/install/tf2_py:/home/<USER>/ros2_humble/install/tf2_msgs:/home/<USER>/ros2_humble/install/test_msgs:/home/<USER>/ros2_humble/install/sros2_cmake:/home/<USER>/ros2_humble/install/ros2cli_common_extensions:/home/<USER>/ros2_humble/install/rqt_py_common:/home/<USER>/ros2_humble/install/rosbag2_storage_mcap_testdata:/home/<USER>/ros2_humble/install/ros_testing:/home/<USER>/ros2_humble/install/ros2cli_test_interfaces:/home/<USER>/ros2_humble/install/quality_of_service_demo_cpp:/home/<USER>/ros2_humble/install/image_transport:/home/<USER>/ros2_humble/install/message_filters:/home/<USER>/ros2_humble/install/demo_nodes_cpp:/home/<USER>/ros2_humble/install/composition:/home/<USER>/ros2_humble/install/laser_geometry:/home/<USER>/ros2_humble/install/rclpy:/home/<USER>/ros2_humble/install/examples_rclcpp_minimal_action_server:/home/<USER>/ros2_humble/install/examples_rclcpp_minimal_action_client:/home/<USER>/ros2_humble/install/action_tutorials_cpp:/home/<USER>/ros2_humble/install/rclcpp_action:/home/<USER>/ros2_humble/install/rcl_action:/home/<USER>/ros2_humble/install/examples_rclcpp_wait_set:/home/<USER>/ros2_humble/install/examples_rclcpp_minimal_service:/home/<USER>/ros2_humble/install/examples_rclcpp_minimal_client:/home/<USER>/ros2_humble/install/examples_rclcpp_async_client:/home/<USER>/ros2_humble/install/example_interfaces:/home/<USER>/ros2_humble/install/action_tutorials_interfaces:/home/<USER>/ros2_humble/install/action_msgs:/home/<USER>/ros2_humble/install/unique_identifier_msgs:/home/<USER>/ros2_humble/install/ament_lint_common:/home/<USER>/ros2_humble/install/ament_cmake_uncrustify:/home/<USER>/ros2_humble/install/uncrustify_vendor:/home/<USER>/ros2_humble/install/trajectory_msgs:/home/<USER>/ros2_humble/install/topic_statistics_demo:/home/<USER>/ros2_humble/install/pendulum_control:/home/<USER>/ros2_humble/install/tlsf_cpp:/home/<USER>/ros2_humble/install/test_tracetools:/home/<USER>/ros2_humble/install/rqt_gui_cpp:/home/<USER>/ros2_humble/install/rosbag2_test_common:/home/<USER>/ros2_humble/install/ros2lifecycle_test_fixtures:/home/<USER>/ros2_humble/install/lifecycle:/home/<USER>/ros2_humble/install/rclcpp_lifecycle:/home/<USER>/ros2_humble/install/logging_demo:/home/<USER>/ros2_humble/install/image_tools:/home/<USER>/ros2_humble/install/examples_rclcpp_minimal_subscriber:/home/<USER>/ros2_humble/install/examples_rclcpp_minimal_composition:/home/<USER>/ros2_humble/install/demo_nodes_cpp_native:/home/<USER>/ros2_humble/install/rclcpp_components:/home/<USER>/ros2_humble/install/intra_process_demo:/home/<USER>/ros2_humble/install/examples_rclcpp_multithreaded_executor:/home/<USER>/ros2_humble/install/examples_rclcpp_minimal_timer:/home/<USER>/ros2_humble/install/examples_rclcpp_minimal_publisher:/home/<USER>/ros2_humble/install/examples_rclcpp_cbg_executor:/home/<USER>/ros2_humble/install/dummy_sensors:/home/<USER>/ros2_humble/install/dummy_map_server:/home/<USER>/ros2_humble/install/rclcpp:/home/<USER>/ros2_humble/install/rcl_lifecycle:/home/<USER>/ros2_humble/install/libstatistics_collector:/home/<USER>/ros2_humble/install/rcl:/home/<USER>/ros2_humble/install/rmw_implementation:/home/<USER>/ros2_humble/install/rmw_fastrtps_dynamic_cpp:/home/<USER>/ros2_humble/install/rmw_fastrtps_cpp:/home/<USER>/ros2_humble/install/rmw_fastrtps_shared_cpp:/home/<USER>/ros2_humble/install/rmw_cyclonedds_cpp:/home/<USER>/ros2_humble/install/tracetools:/home/<USER>/ros2_humble/install/tlsf:/home/<USER>/ros2_humble/install/tinyxml_vendor:/home/<USER>/ros2_humble/install/qt_gui_core:/home/<USER>/ros2_humble/install/qt_gui_cpp:/home/<USER>/ros2_humble/install/pluginlib:/home/<USER>/ros2_humble/install/tinyxml2_vendor:/home/<USER>/ros2_humble/install/tf2_eigen_kdl:/home/<USER>/ros2_humble/install/tf2:/home/<USER>/ros2_humble/install/test_security:/home/<USER>/ros2_humble/install/test_rmw_implementation:/home/<USER>/ros2_humble/install/test_rclcpp:/home/<USER>/ros2_humble/install/test_quality_of_service:/home/<USER>/ros2_humble/install/test_launch_testing:/home/<USER>/ros2_humble/install/test_interface_files:/home/<USER>/ros2_humble/install/test_communication:/home/<USER>/ros2_humble/install/test_cli_remapping:/home/<USER>/ros2_humble/install/test_cli:/home/<USER>/ros2_humble/install/qt_gui_app:/home/<USER>/ros2_humble/install/qt_gui:/home/<USER>/ros2_humble/install/tango_icons_vendor:/home/<USER>/ros2_humble/install/stereo_msgs:/home/<USER>/ros2_humble/install/std_srvs:/home/<USER>/ros2_humble/install/shape_msgs:/home/<USER>/ros2_humble/install/map_msgs:/home/<USER>/ros2_humble/install/sensor_msgs:/home/<USER>/ros2_humble/install/nav_msgs:/home/<USER>/ros2_humble/install/diagnostic_msgs:/home/<USER>/ros2_humble/install/geometry_msgs:/home/<USER>/ros2_humble/install/actionlib_msgs:/home/<USER>/ros2_humble/install/std_msgs:/home/<USER>/ros2_humble/install/statistics_msgs:/home/<USER>/ros2_humble/install/sqlite3_vendor:/home/<USER>/ros2_humble/install/rcl_logging_spdlog:/home/<USER>/ros2_humble/install/spdlog_vendor:/home/<USER>/ros2_humble/install/shared_queues_vendor:/home/<USER>/ros2_humble/install/rviz_rendering_tests:/home/<USER>/ros2_humble/install/rviz_rendering:/home/<USER>/ros2_humble/install/rviz_ogre_vendor:/home/<USER>/ros2_humble/install/rviz_assimp_vendor:/home/<USER>/ros2_humble/install/rttest:/home/<USER>/ros2_humble/install/rmw_connextddsmicro:/home/<USER>/ros2_humble/install/rmw_connextdds:/home/<USER>/ros2_humble/install/rmw_connextdds_common:/home/<USER>/ros2_humble/install/rti_connext_dds_cmake_module:/home/<USER>/ros2_humble/install/rosgraph_msgs:/home/<USER>/ros2_humble/install/rosbag2_interfaces:/home/<USER>/ros2_humble/install/rmw_dds_common:/home/<USER>/ros2_humble/install/composition_interfaces:/home/<USER>/ros2_humble/install/rcl_interfaces:/home/<USER>/ros2_humble/install/pendulum_msgs:/home/<USER>/ros2_humble/install/lifecycle_msgs:/home/<USER>/ros2_humble/install/builtin_interfaces:/home/<USER>/ros2_humble/install/rosidl_default_runtime:/home/<USER>/ros2_humble/install/rosidl_default_generators:/home/<USER>/ros2_humble/install/rosidl_generator_py:/home/<USER>/ros2_humble/install/rosidl_typesupport_introspection_tests:/home/<USER>/ros2_humble/install/rosidl_typesupport_cpp:/home/<USER>/ros2_humble/install/rosidl_typesupport_introspection_cpp:/home/<USER>/ros2_humble/install/rosidl_typesupport_c:/home/<USER>/ros2_humble/install/rosidl_typesupport_introspection_c:/home/<USER>/ros2_humble/install/rosidl_typesupport_fastrtps_c:/home/<USER>/ros2_humble/install/rosidl_typesupport_fastrtps_cpp:/home/<USER>/ros2_humble/install/rosidl_generator_cpp:/home/<USER>/ros2_humble/install/rosidl_runtime_cpp:/home/<USER>/ros2_humble/install/rcl_yaml_param_parser:/home/<USER>/ros2_humble/install/rmw:/home/<USER>/ros2_humble/install/rosidl_runtime_c:/home/<USER>/ros2_humble/install/rosidl_generator_c:/home/<USER>/ros2_humble/install/rosidl_typesupport_interface:/home/<USER>/ros2_humble/install/rosidl_generator_dds_idl:/home/<USER>/ros2_humble/install/rosidl_cmake:/home/<USER>/ros2_humble/install/rosidl_parser:/home/<USER>/ros2_humble/install/rosidl_adapter:/home/<USER>/ros2_humble/install/rosbag2_tests:/home/<USER>/ros2_humble/install/ros_environment:/home/<USER>/ros2_humble/install/rmw_implementation_cmake:/home/<USER>/ros2_humble/install/resource_retriever:/home/<USER>/ros2_humble/install/class_loader:/home/<USER>/ros2_humble/install/rcpputils:/home/<USER>/ros2_humble/install/rcl_logging_noop:/home/<USER>/ros2_humble/install/rcl_logging_interface:/home/<USER>/ros2_humble/install/rcutils:/home/<USER>/ros2_humble/install/qt_gui_py_common:/home/<USER>/ros2_humble/install/qt_dotgraph:/home/<USER>/ros2_humble/install/python_qt_binding:/home/<USER>/ros2_humble/install/python_orocos_kdl_vendor:/home/<USER>/ros2_humble/install/launch_testing_ament_cmake:/home/<USER>/ros2_humble/install/python_cmake_module:/home/<USER>/ros2_humble/install/pybind11_vendor:/home/<USER>/ros2_humble/install/performance_test_fixture:/home/<USER>/ros2_humble/install/osrf_testing_tools_cpp:/home/<USER>/ros2_humble/install/rqt_bag_plugins:/home/<USER>/ros2_humble/install/rqt_bag:/home/<USER>/ros2_humble/install/launch_testing_examples:/home/<USER>/ros2_humble/install/ros2bag:/home/<USER>/ros2_humble/install/tracetools_test:/home/<USER>/ros2_humble/install/tracetools_launch:/home/<USER>/ros2_humble/install/topic_monitor:/home/<USER>/ros2_humble/install/tf2_tools:/home/<USER>/ros2_humble/install/examples_tf2_py:/home/<USER>/ros2_humble/install/tf2_ros_py:/home/<USER>/ros2_humble/install/sros2:/home/<USER>/ros2_humble/install/rqt_topic:/home/<USER>/ros2_humble/install/rqt_srv:/home/<USER>/ros2_humble/install/rqt_shell:/home/<USER>/ros2_humble/install/rqt_service_caller:/home/<USER>/ros2_humble/install/rqt_reconfigure:/home/<USER>/ros2_humble/install/rqt_py_console:/home/<USER>/ros2_humble/install/rqt_publisher:/home/<USER>/ros2_humble/install/rqt_plot:/home/<USER>/ros2_humble/install/rqt_action:/home/<USER>/ros2_humble/install/rqt_msg:/home/<USER>/ros2_humble/install/rqt_console:/home/<USER>/ros2_humble/install/rqt:/home/<USER>/ros2_humble/install/rqt_graph:/home/<USER>/ros2_humble/install/rqt_gui_py:/home/<USER>/ros2_humble/install/rqt_gui:/home/<USER>/ros2_humble/install/ros2trace:/home/<USER>/ros2_humble/install/ros2topic:/home/<USER>/ros2_humble/install/ros2test:/home/<USER>/ros2_humble/install/ros2component:/home/<USER>/ros2_humble/install/ros2param:/home/<USER>/ros2_humble/install/ros2lifecycle:/home/<USER>/ros2_humble/install/ros2service:/home/<USER>/ros2_humble/install/ros2run:/home/<USER>/ros2_humble/install/ros2launch:/home/<USER>/ros2_humble/install/ros2pkg:/home/<USER>/ros2_humble/install/ros2node:/home/<USER>/ros2_humble/install/ros2multicast:/home/<USER>/ros2_humble/install/ros2interface:/home/<USER>/ros2_humble/install/ros2doctor:/home/<USER>/ros2_humble/install/ros2action:/home/<USER>/ros2_humble/install/ros2cli:/home/<USER>/ros2_humble/install/quality_of_service_demo_py:/home/<USER>/ros2_humble/install/lifecycle_py:/home/<USER>/ros2_humble/install/launch_testing_ros:/home/<USER>/ros2_humble/install/launch_ros:/home/<USER>/ros2_humble/install/examples_rclpy_pointcloud_publisher:/home/<USER>/ros2_humble/install/examples_rclpy_minimal_subscriber:/home/<USER>/ros2_humble/install/examples_rclpy_minimal_service:/home/<USER>/ros2_humble/install/examples_rclpy_minimal_publisher:/home/<USER>/ros2_humble/install/examples_rclpy_minimal_client:/home/<USER>/ros2_humble/install/examples_rclpy_minimal_action_server:/home/<USER>/ros2_humble/install/examples_rclpy_minimal_action_client:/home/<USER>/ros2_humble/install/examples_rclpy_guard_conditions:/home/<USER>/ros2_humble/install/examples_rclpy_executors:/home/<USER>/ros2_humble/install/demo_nodes_py:/home/<USER>/ros2_humble/install/camera_info_manager_py:/home/<USER>/ros2_humble/install/action_tutorials_py:/home/<USER>/ros2_humble/install/ament_uncrustify:/home/<USER>/ros2_humble/install/tracetools_trace:/home/<USER>/ros2_humble/install/tracetools_read:/home/<USER>/ros2_humble/install/test_tracetools_launch:/home/<USER>/ros2_humble/install/test_launch_ros:/home/<USER>/ros2_humble/install/sensor_msgs_py:/home/<USER>/ros2_humble/install/rpyutils:/home/<USER>/ros2_humble/install/rosidl_runtime_py:/home/<USER>/ros2_humble/install/rosidl_cli:/home/<USER>/ros2_humble/install/launch_pytest:/home/<USER>/ros2_humble/install/launch_testing:/home/<USER>/ros2_humble/install/launch_yaml:/home/<USER>/ros2_humble/install/launch_xml:/home/<USER>/ros2_humble/install/launch:/home/<USER>/ros2_humble/install/osrf_pycommon:/home/<USER>/ros2_humble/install/domain_coordinator:/home/<USER>/ros2_humble/install/ament_xmllint:/home/<USER>/ros2_humble/install/ament_pyflakes:/home/<USER>/ros2_humble/install/ament_pycodestyle:/home/<USER>/ros2_humble/install/ament_pep257:/home/<USER>/ros2_humble/install/ament_pclint:/home/<USER>/ros2_humble/install/ament_package:/home/<USER>/ros2_humble/install/ament_mypy:/home/<USER>/ros2_humble/install/ament_lint_cmake:/home/<USER>/ros2_humble/install/ament_flake8:/home/<USER>/ros2_humble/install/ament_copyright:/home/<USER>/ros2_humble/install/ament_lint:/home/<USER>/ros2_humble/install/ament_index_python:/home/<USER>/ros2_humble/install/ament_cpplint:/home/<USER>/ros2_humble/install/ament_cppcheck:/home/<USER>/ros2_humble/install/ament_clang_tidy:/home/<USER>/ros2_humble/install/ament_clang_format:/home/<USER>/ros2_humble/install/orocos_kdl_vendor:/home/<USER>/ros2_humble/install/mimick_vendor:/home/<USER>/ros2_humble/install/libyaml_vendor:/home/<USER>/ros2_humble/install/libcurl_vendor:/home/<USER>/ros2_humble/install/keyboard_handler:/home/<USER>/ros2_humble/install/iceoryx_introspection:/home/<USER>/ros2_humble/install/cyclonedds:/home/<USER>/ros2_humble/install/iceoryx_posh:/home/<USER>/ros2_humble/install/iceoryx_hoofs:/home/<USER>/ros2_humble/install/iceoryx_binding_c:/home/<USER>/ros2_humble/install/ament_cmake_ros:/home/<USER>/ros2_humble/install/ament_cmake_auto:/home/<USER>/ros2_humble/install/ament_cmake_gmock:/home/<USER>/ros2_humble/install/gmock_vendor:/home/<USER>/ros2_humble/install/ament_cmake_gtest:/home/<USER>/ros2_humble/install/gtest_vendor:/home/<USER>/ros2_humble/install/ament_cmake_google_benchmark:/home/<USER>/ros2_humble/install/google_benchmark_vendor:/home/<USER>/ros2_humble/install/fastrtps:/home/<USER>/ros2_humble/install/foonathan_memory_vendor:/home/<USER>/ros2_humble/install/fastrtps_cmake_module:/home/<USER>/ros2_humble/install/fastcdr:/home/<USER>/ros2_humble/install/eigen3_cmake_module:/home/<USER>/ros2_humble/install/console_bridge_vendor:/home/<USER>/ros2_humble/install/ament_cmake_xmllint:/home/<USER>/ros2_humble/install/ament_cmake_pyflakes:/home/<USER>/ros2_humble/install/ament_cmake_pycodestyle:/home/<USER>/ros2_humble/install/ament_cmake_pep257:/home/<USER>/ros2_humble/install/ament_cmake_pclint:/home/<USER>/ros2_humble/install/ament_lint_auto:/home/<USER>/ros2_humble/install/ament_cmake:/home/<USER>/ros2_humble/install/ament_cmake_version:/home/<USER>/ros2_humble/install/ament_cmake_vendor_package:/home/<USER>/ros2_humble/install/ament_cmake_pytest:/home/<USER>/ros2_humble/install/ament_cmake_nose:/home/<USER>/ros2_humble/install/ament_cmake_mypy:/home/<USER>/ros2_humble/install/ament_cmake_lint_cmake:/home/<USER>/ros2_humble/install/ament_cmake_flake8:/home/<USER>/ros2_humble/install/ament_cmake_cpplint:/home/<USER>/ros2_humble/install/ament_cmake_cppcheck:/home/<USER>/ros2_humble/install/ament_cmake_copyright:/home/<USER>/ros2_humble/install/ament_cmake_clang_tidy:/home/<USER>/ros2_humble/install/ament_cmake_clang_format:/home/<USER>/ros2_humble/install/ament_cmake_test:/home/<USER>/ros2_humble/install/ament_cmake_target_dependencies:/home/<USER>/ros2_humble/install/ament_cmake_python:/home/<USER>/ros2_humble/install/ament_cmake_export_dependencies:/home/<USER>/ros2_humble/install/ament_cmake_libraries:/home/<USER>/ros2_humble/install/ament_cmake_include_directories:/home/<USER>/ros2_humble/install/ament_cmake_gen_version_h:/home/<USER>/ros2_humble/install/ament_cmake_export_targets:/home/<USER>/ros2_humble/install/ament_cmake_export_link_flags:/home/<USER>/ros2_humble/install/ament_cmake_export_interfaces:/home/<USER>/ros2_humble/install/ament_cmake_export_libraries:/home/<USER>/ros2_humble/install/ament_cmake_export_include_directories:/home/<USER>/ros2_humble/install/ament_cmake_export_definitions:/home/<USER>/ros2_humble/install/ament_cmake_core:/home/<USER>/ros2_humble/install/ament_index_cpp:/opt/ros/noetic /usr/bin/cmake /mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/src/send_pkg/src -DCMAKE_INSTALL_PREFIX=/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/workspace_send
[7.863s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/workspace_send' returned '1': CMAKE_PREFIX_PATH=/home/<USER>/ros2_humble/install/rosbag2_storage_mcap:/home/<USER>/ros2_humble/install/rosbag2:/home/<USER>/ros2_humble/install/rosbag2_compression_zstd:/home/<USER>/ros2_humble/install/mcap_vendor:/home/<USER>/ros2_humble/install/zstd_vendor:/home/<USER>/ros2_humble/install/rviz_visual_testing_framework:/home/<USER>/ros2_humble/install/rviz2:/home/<USER>/ros2_humble/install/rviz_default_plugins:/home/<USER>/ros2_humble/install/rviz_common:/home/<USER>/ros2_humble/install/rosbag2_py:/home/<USER>/ros2_humble/install/rosbag2_transport:/home/<USER>/ros2_humble/install/rosbag2_storage_default_plugins:/home/<USER>/ros2_humble/install/rosbag2_performance_benchmarking:/home/<USER>/ros2_humble/install/rosbag2_compression:/home/<USER>/ros2_humble/install/rosbag2_cpp:/home/<USER>/ros2_humble/install/rosbag2_storage:/home/<USER>/ros2_humble/install/image_common:/home/<USER>/ros2_humble/install/camera_info_manager:/home/<USER>/ros2_humble/install/camera_calibration_parsers:/home/<USER>/ros2_humble/install/yaml_cpp_vendor:/home/<USER>/ros2_humble/install/interactive_markers:/home/<USER>/ros2_humble/install/common_interfaces:/home/<USER>/ros2_humble/install/visualization_msgs:/home/<USER>/ros2_humble/install/dummy_robot_bringup:/home/<USER>/ros2_humble/install/robot_state_publisher:/home/<USER>/ros2_humble/install/kdl_parser:/home/<USER>/ros2_humble/install/urdf:/home/<USER>/ros2_humble/install/urdfdom:/home/<USER>/ros2_humble/install/urdf_parser_plugin:/home/<USER>/ros2_humble/install/urdfdom_headers:/home/<USER>/ros2_humble/install/turtlesim:/home/<USER>/ros2_humble/install/geometry2:/home/<USER>/ros2_humble/install/tf2_sensor_msgs:/home/<USER>/ros2_humble/install/test_tf2:/home/<USER>/ros2_humble/install/tf2_kdl:/home/<USER>/ros2_humble/install/tf2_geometry_msgs:/home/<USER>/ros2_humble/install/tf2_eigen:/home/<USER>/ros2_humble/install/tf2_bullet:/home/<USER>/ros2_humble/install/tf2_ros:/home/<USER>/ros2_humble/install/tf2_py:/home/<USER>/ros2_humble/install/tf2_msgs:/home/<USER>/ros2_humble/install/test_msgs:/home/<USER>/ros2_humble/install/sros2_cmake:/home/<USER>/ros2_humble/install/ros2cli_common_extensions:/home/<USER>/ros2_humble/install/rqt_py_common:/home/<USER>/ros2_humble/install/rosbag2_storage_mcap_testdata:/home/<USER>/ros2_humble/install/ros_testing:/home/<USER>/ros2_humble/install/ros2cli_test_interfaces:/home/<USER>/ros2_humble/install/quality_of_service_demo_cpp:/home/<USER>/ros2_humble/install/image_transport:/home/<USER>/ros2_humble/install/message_filters:/home/<USER>/ros2_humble/install/demo_nodes_cpp:/home/<USER>/ros2_humble/install/composition:/home/<USER>/ros2_humble/install/laser_geometry:/home/<USER>/ros2_humble/install/rclpy:/home/<USER>/ros2_humble/install/examples_rclcpp_minimal_action_server:/home/<USER>/ros2_humble/install/examples_rclcpp_minimal_action_client:/home/<USER>/ros2_humble/install/action_tutorials_cpp:/home/<USER>/ros2_humble/install/rclcpp_action:/home/<USER>/ros2_humble/install/rcl_action:/home/<USER>/ros2_humble/install/examples_rclcpp_wait_set:/home/<USER>/ros2_humble/install/examples_rclcpp_minimal_service:/home/<USER>/ros2_humble/install/examples_rclcpp_minimal_client:/home/<USER>/ros2_humble/install/examples_rclcpp_async_client:/home/<USER>/ros2_humble/install/example_interfaces:/home/<USER>/ros2_humble/install/action_tutorials_interfaces:/home/<USER>/ros2_humble/install/action_msgs:/home/<USER>/ros2_humble/install/unique_identifier_msgs:/home/<USER>/ros2_humble/install/ament_lint_common:/home/<USER>/ros2_humble/install/ament_cmake_uncrustify:/home/<USER>/ros2_humble/install/uncrustify_vendor:/home/<USER>/ros2_humble/install/trajectory_msgs:/home/<USER>/ros2_humble/install/topic_statistics_demo:/home/<USER>/ros2_humble/install/pendulum_control:/home/<USER>/ros2_humble/install/tlsf_cpp:/home/<USER>/ros2_humble/install/test_tracetools:/home/<USER>/ros2_humble/install/rqt_gui_cpp:/home/<USER>/ros2_humble/install/rosbag2_test_common:/home/<USER>/ros2_humble/install/ros2lifecycle_test_fixtures:/home/<USER>/ros2_humble/install/lifecycle:/home/<USER>/ros2_humble/install/rclcpp_lifecycle:/home/<USER>/ros2_humble/install/logging_demo:/home/<USER>/ros2_humble/install/image_tools:/home/<USER>/ros2_humble/install/examples_rclcpp_minimal_subscriber:/home/<USER>/ros2_humble/install/examples_rclcpp_minimal_composition:/home/<USER>/ros2_humble/install/demo_nodes_cpp_native:/home/<USER>/ros2_humble/install/rclcpp_components:/home/<USER>/ros2_humble/install/intra_process_demo:/home/<USER>/ros2_humble/install/examples_rclcpp_multithreaded_executor:/home/<USER>/ros2_humble/install/examples_rclcpp_minimal_timer:/home/<USER>/ros2_humble/install/examples_rclcpp_minimal_publisher:/home/<USER>/ros2_humble/install/examples_rclcpp_cbg_executor:/home/<USER>/ros2_humble/install/dummy_sensors:/home/<USER>/ros2_humble/install/dummy_map_server:/home/<USER>/ros2_humble/install/rclcpp:/home/<USER>/ros2_humble/install/rcl_lifecycle:/home/<USER>/ros2_humble/install/libstatistics_collector:/home/<USER>/ros2_humble/install/rcl:/home/<USER>/ros2_humble/install/rmw_implementation:/home/<USER>/ros2_humble/install/rmw_fastrtps_dynamic_cpp:/home/<USER>/ros2_humble/install/rmw_fastrtps_cpp:/home/<USER>/ros2_humble/install/rmw_fastrtps_shared_cpp:/home/<USER>/ros2_humble/install/rmw_cyclonedds_cpp:/home/<USER>/ros2_humble/install/tracetools:/home/<USER>/ros2_humble/install/tlsf:/home/<USER>/ros2_humble/install/tinyxml_vendor:/home/<USER>/ros2_humble/install/qt_gui_core:/home/<USER>/ros2_humble/install/qt_gui_cpp:/home/<USER>/ros2_humble/install/pluginlib:/home/<USER>/ros2_humble/install/tinyxml2_vendor:/home/<USER>/ros2_humble/install/tf2_eigen_kdl:/home/<USER>/ros2_humble/install/tf2:/home/<USER>/ros2_humble/install/test_security:/home/<USER>/ros2_humble/install/test_rmw_implementation:/home/<USER>/ros2_humble/install/test_rclcpp:/home/<USER>/ros2_humble/install/test_quality_of_service:/home/<USER>/ros2_humble/install/test_launch_testing:/home/<USER>/ros2_humble/install/test_interface_files:/home/<USER>/ros2_humble/install/test_communication:/home/<USER>/ros2_humble/install/test_cli_remapping:/home/<USER>/ros2_humble/install/test_cli:/home/<USER>/ros2_humble/install/qt_gui_app:/home/<USER>/ros2_humble/install/qt_gui:/home/<USER>/ros2_humble/install/tango_icons_vendor:/home/<USER>/ros2_humble/install/stereo_msgs:/home/<USER>/ros2_humble/install/std_srvs:/home/<USER>/ros2_humble/install/shape_msgs:/home/<USER>/ros2_humble/install/map_msgs:/home/<USER>/ros2_humble/install/sensor_msgs:/home/<USER>/ros2_humble/install/nav_msgs:/home/<USER>/ros2_humble/install/diagnostic_msgs:/home/<USER>/ros2_humble/install/geometry_msgs:/home/<USER>/ros2_humble/install/actionlib_msgs:/home/<USER>/ros2_humble/install/std_msgs:/home/<USER>/ros2_humble/install/statistics_msgs:/home/<USER>/ros2_humble/install/sqlite3_vendor:/home/<USER>/ros2_humble/install/rcl_logging_spdlog:/home/<USER>/ros2_humble/install/spdlog_vendor:/home/<USER>/ros2_humble/install/shared_queues_vendor:/home/<USER>/ros2_humble/install/rviz_rendering_tests:/home/<USER>/ros2_humble/install/rviz_rendering:/home/<USER>/ros2_humble/install/rviz_ogre_vendor:/home/<USER>/ros2_humble/install/rviz_assimp_vendor:/home/<USER>/ros2_humble/install/rttest:/home/<USER>/ros2_humble/install/rmw_connextddsmicro:/home/<USER>/ros2_humble/install/rmw_connextdds:/home/<USER>/ros2_humble/install/rmw_connextdds_common:/home/<USER>/ros2_humble/install/rti_connext_dds_cmake_module:/home/<USER>/ros2_humble/install/rosgraph_msgs:/home/<USER>/ros2_humble/install/rosbag2_interfaces:/home/<USER>/ros2_humble/install/rmw_dds_common:/home/<USER>/ros2_humble/install/composition_interfaces:/home/<USER>/ros2_humble/install/rcl_interfaces:/home/<USER>/ros2_humble/install/pendulum_msgs:/home/<USER>/ros2_humble/install/lifecycle_msgs:/home/<USER>/ros2_humble/install/builtin_interfaces:/home/<USER>/ros2_humble/install/rosidl_default_runtime:/home/<USER>/ros2_humble/install/rosidl_default_generators:/home/<USER>/ros2_humble/install/rosidl_generator_py:/home/<USER>/ros2_humble/install/rosidl_typesupport_introspection_tests:/home/<USER>/ros2_humble/install/rosidl_typesupport_cpp:/home/<USER>/ros2_humble/install/rosidl_typesupport_introspection_cpp:/home/<USER>/ros2_humble/install/rosidl_typesupport_c:/home/<USER>/ros2_humble/install/rosidl_typesupport_introspection_c:/home/<USER>/ros2_humble/install/rosidl_typesupport_fastrtps_c:/home/<USER>/ros2_humble/install/rosidl_typesupport_fastrtps_cpp:/home/<USER>/ros2_humble/install/rosidl_generator_cpp:/home/<USER>/ros2_humble/install/rosidl_runtime_cpp:/home/<USER>/ros2_humble/install/rcl_yaml_param_parser:/home/<USER>/ros2_humble/install/rmw:/home/<USER>/ros2_humble/install/rosidl_runtime_c:/home/<USER>/ros2_humble/install/rosidl_generator_c:/home/<USER>/ros2_humble/install/rosidl_typesupport_interface:/home/<USER>/ros2_humble/install/rosidl_generator_dds_idl:/home/<USER>/ros2_humble/install/rosidl_cmake:/home/<USER>/ros2_humble/install/rosidl_parser:/home/<USER>/ros2_humble/install/rosidl_adapter:/home/<USER>/ros2_humble/install/rosbag2_tests:/home/<USER>/ros2_humble/install/ros_environment:/home/<USER>/ros2_humble/install/rmw_implementation_cmake:/home/<USER>/ros2_humble/install/resource_retriever:/home/<USER>/ros2_humble/install/class_loader:/home/<USER>/ros2_humble/install/rcpputils:/home/<USER>/ros2_humble/install/rcl_logging_noop:/home/<USER>/ros2_humble/install/rcl_logging_interface:/home/<USER>/ros2_humble/install/rcutils:/home/<USER>/ros2_humble/install/qt_gui_py_common:/home/<USER>/ros2_humble/install/qt_dotgraph:/home/<USER>/ros2_humble/install/python_qt_binding:/home/<USER>/ros2_humble/install/python_orocos_kdl_vendor:/home/<USER>/ros2_humble/install/launch_testing_ament_cmake:/home/<USER>/ros2_humble/install/python_cmake_module:/home/<USER>/ros2_humble/install/pybind11_vendor:/home/<USER>/ros2_humble/install/performance_test_fixture:/home/<USER>/ros2_humble/install/osrf_testing_tools_cpp:/home/<USER>/ros2_humble/install/rqt_bag_plugins:/home/<USER>/ros2_humble/install/rqt_bag:/home/<USER>/ros2_humble/install/launch_testing_examples:/home/<USER>/ros2_humble/install/ros2bag:/home/<USER>/ros2_humble/install/tracetools_test:/home/<USER>/ros2_humble/install/tracetools_launch:/home/<USER>/ros2_humble/install/topic_monitor:/home/<USER>/ros2_humble/install/tf2_tools:/home/<USER>/ros2_humble/install/examples_tf2_py:/home/<USER>/ros2_humble/install/tf2_ros_py:/home/<USER>/ros2_humble/install/sros2:/home/<USER>/ros2_humble/install/rqt_topic:/home/<USER>/ros2_humble/install/rqt_srv:/home/<USER>/ros2_humble/install/rqt_shell:/home/<USER>/ros2_humble/install/rqt_service_caller:/home/<USER>/ros2_humble/install/rqt_reconfigure:/home/<USER>/ros2_humble/install/rqt_py_console:/home/<USER>/ros2_humble/install/rqt_publisher:/home/<USER>/ros2_humble/install/rqt_plot:/home/<USER>/ros2_humble/install/rqt_action:/home/<USER>/ros2_humble/install/rqt_msg:/home/<USER>/ros2_humble/install/rqt_console:/home/<USER>/ros2_humble/install/rqt:/home/<USER>/ros2_humble/install/rqt_graph:/home/<USER>/ros2_humble/install/rqt_gui_py:/home/<USER>/ros2_humble/install/rqt_gui:/home/<USER>/ros2_humble/install/ros2trace:/home/<USER>/ros2_humble/install/ros2topic:/home/<USER>/ros2_humble/install/ros2test:/home/<USER>/ros2_humble/install/ros2component:/home/<USER>/ros2_humble/install/ros2param:/home/<USER>/ros2_humble/install/ros2lifecycle:/home/<USER>/ros2_humble/install/ros2service:/home/<USER>/ros2_humble/install/ros2run:/home/<USER>/ros2_humble/install/ros2launch:/home/<USER>/ros2_humble/install/ros2pkg:/home/<USER>/ros2_humble/install/ros2node:/home/<USER>/ros2_humble/install/ros2multicast:/home/<USER>/ros2_humble/install/ros2interface:/home/<USER>/ros2_humble/install/ros2doctor:/home/<USER>/ros2_humble/install/ros2action:/home/<USER>/ros2_humble/install/ros2cli:/home/<USER>/ros2_humble/install/quality_of_service_demo_py:/home/<USER>/ros2_humble/install/lifecycle_py:/home/<USER>/ros2_humble/install/launch_testing_ros:/home/<USER>/ros2_humble/install/launch_ros:/home/<USER>/ros2_humble/install/examples_rclpy_pointcloud_publisher:/home/<USER>/ros2_humble/install/examples_rclpy_minimal_subscriber:/home/<USER>/ros2_humble/install/examples_rclpy_minimal_service:/home/<USER>/ros2_humble/install/examples_rclpy_minimal_publisher:/home/<USER>/ros2_humble/install/examples_rclpy_minimal_client:/home/<USER>/ros2_humble/install/examples_rclpy_minimal_action_server:/home/<USER>/ros2_humble/install/examples_rclpy_minimal_action_client:/home/<USER>/ros2_humble/install/examples_rclpy_guard_conditions:/home/<USER>/ros2_humble/install/examples_rclpy_executors:/home/<USER>/ros2_humble/install/demo_nodes_py:/home/<USER>/ros2_humble/install/camera_info_manager_py:/home/<USER>/ros2_humble/install/action_tutorials_py:/home/<USER>/ros2_humble/install/ament_uncrustify:/home/<USER>/ros2_humble/install/tracetools_trace:/home/<USER>/ros2_humble/install/tracetools_read:/home/<USER>/ros2_humble/install/test_tracetools_launch:/home/<USER>/ros2_humble/install/test_launch_ros:/home/<USER>/ros2_humble/install/sensor_msgs_py:/home/<USER>/ros2_humble/install/rpyutils:/home/<USER>/ros2_humble/install/rosidl_runtime_py:/home/<USER>/ros2_humble/install/rosidl_cli:/home/<USER>/ros2_humble/install/launch_pytest:/home/<USER>/ros2_humble/install/launch_testing:/home/<USER>/ros2_humble/install/launch_yaml:/home/<USER>/ros2_humble/install/launch_xml:/home/<USER>/ros2_humble/install/launch:/home/<USER>/ros2_humble/install/osrf_pycommon:/home/<USER>/ros2_humble/install/domain_coordinator:/home/<USER>/ros2_humble/install/ament_xmllint:/home/<USER>/ros2_humble/install/ament_pyflakes:/home/<USER>/ros2_humble/install/ament_pycodestyle:/home/<USER>/ros2_humble/install/ament_pep257:/home/<USER>/ros2_humble/install/ament_pclint:/home/<USER>/ros2_humble/install/ament_package:/home/<USER>/ros2_humble/install/ament_mypy:/home/<USER>/ros2_humble/install/ament_lint_cmake:/home/<USER>/ros2_humble/install/ament_flake8:/home/<USER>/ros2_humble/install/ament_copyright:/home/<USER>/ros2_humble/install/ament_lint:/home/<USER>/ros2_humble/install/ament_index_python:/home/<USER>/ros2_humble/install/ament_cpplint:/home/<USER>/ros2_humble/install/ament_cppcheck:/home/<USER>/ros2_humble/install/ament_clang_tidy:/home/<USER>/ros2_humble/install/ament_clang_format:/home/<USER>/ros2_humble/install/orocos_kdl_vendor:/home/<USER>/ros2_humble/install/mimick_vendor:/home/<USER>/ros2_humble/install/libyaml_vendor:/home/<USER>/ros2_humble/install/libcurl_vendor:/home/<USER>/ros2_humble/install/keyboard_handler:/home/<USER>/ros2_humble/install/iceoryx_introspection:/home/<USER>/ros2_humble/install/cyclonedds:/home/<USER>/ros2_humble/install/iceoryx_posh:/home/<USER>/ros2_humble/install/iceoryx_hoofs:/home/<USER>/ros2_humble/install/iceoryx_binding_c:/home/<USER>/ros2_humble/install/ament_cmake_ros:/home/<USER>/ros2_humble/install/ament_cmake_auto:/home/<USER>/ros2_humble/install/ament_cmake_gmock:/home/<USER>/ros2_humble/install/gmock_vendor:/home/<USER>/ros2_humble/install/ament_cmake_gtest:/home/<USER>/ros2_humble/install/gtest_vendor:/home/<USER>/ros2_humble/install/ament_cmake_google_benchmark:/home/<USER>/ros2_humble/install/google_benchmark_vendor:/home/<USER>/ros2_humble/install/fastrtps:/home/<USER>/ros2_humble/install/foonathan_memory_vendor:/home/<USER>/ros2_humble/install/fastrtps_cmake_module:/home/<USER>/ros2_humble/install/fastcdr:/home/<USER>/ros2_humble/install/eigen3_cmake_module:/home/<USER>/ros2_humble/install/console_bridge_vendor:/home/<USER>/ros2_humble/install/ament_cmake_xmllint:/home/<USER>/ros2_humble/install/ament_cmake_pyflakes:/home/<USER>/ros2_humble/install/ament_cmake_pycodestyle:/home/<USER>/ros2_humble/install/ament_cmake_pep257:/home/<USER>/ros2_humble/install/ament_cmake_pclint:/home/<USER>/ros2_humble/install/ament_lint_auto:/home/<USER>/ros2_humble/install/ament_cmake:/home/<USER>/ros2_humble/install/ament_cmake_version:/home/<USER>/ros2_humble/install/ament_cmake_vendor_package:/home/<USER>/ros2_humble/install/ament_cmake_pytest:/home/<USER>/ros2_humble/install/ament_cmake_nose:/home/<USER>/ros2_humble/install/ament_cmake_mypy:/home/<USER>/ros2_humble/install/ament_cmake_lint_cmake:/home/<USER>/ros2_humble/install/ament_cmake_flake8:/home/<USER>/ros2_humble/install/ament_cmake_cpplint:/home/<USER>/ros2_humble/install/ament_cmake_cppcheck:/home/<USER>/ros2_humble/install/ament_cmake_copyright:/home/<USER>/ros2_humble/install/ament_cmake_clang_tidy:/home/<USER>/ros2_humble/install/ament_cmake_clang_format:/home/<USER>/ros2_humble/install/ament_cmake_test:/home/<USER>/ros2_humble/install/ament_cmake_target_dependencies:/home/<USER>/ros2_humble/install/ament_cmake_python:/home/<USER>/ros2_humble/install/ament_cmake_export_dependencies:/home/<USER>/ros2_humble/install/ament_cmake_libraries:/home/<USER>/ros2_humble/install/ament_cmake_include_directories:/home/<USER>/ros2_humble/install/ament_cmake_gen_version_h:/home/<USER>/ros2_humble/install/ament_cmake_export_targets:/home/<USER>/ros2_humble/install/ament_cmake_export_link_flags:/home/<USER>/ros2_humble/install/ament_cmake_export_interfaces:/home/<USER>/ros2_humble/install/ament_cmake_export_libraries:/home/<USER>/ros2_humble/install/ament_cmake_export_include_directories:/home/<USER>/ros2_humble/install/ament_cmake_export_definitions:/home/<USER>/ros2_humble/install/ament_cmake_core:/home/<USER>/ros2_humble/install/ament_index_cpp:/opt/ros/noetic /usr/bin/cmake /mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/src/send_pkg/src -DCMAKE_INSTALL_PREFIX=/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/workspace_send
[7.888s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(workspace_send)
[7.894s] Level 1:colcon.colcon_core.environment:checking '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/workspace_send' for CMake module files
[7.895s] Level 1:colcon.colcon_core.environment:checking '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/workspace_send' for CMake config files
[7.895s] Level 1:colcon.colcon_core.environment:checking '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/workspace_send/bin'
[7.895s] Level 1:colcon.colcon_core.environment:checking '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/workspace_send/lib/pkgconfig/workspace_send.pc'
[7.895s] Level 1:colcon.colcon_core.environment:checking '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/workspace_send/lib/python3.8/site-packages'
[7.896s] Level 1:colcon.colcon_core.environment:checking '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/workspace_send/bin'
[7.896s] INFO:colcon.colcon_core.shell:Creating package script '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/workspace_send/share/workspace_send/package.ps1'
[7.897s] INFO:colcon.colcon_core.shell:Creating package descriptor '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/workspace_send/share/workspace_send/package.dsv'
[7.897s] INFO:colcon.colcon_core.shell:Creating package script '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/workspace_send/share/workspace_send/package.sh'
[7.898s] INFO:colcon.colcon_core.shell:Creating package script '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/workspace_send/share/workspace_send/package.bash'
[7.898s] INFO:colcon.colcon_core.shell:Creating package script '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/workspace_send/share/workspace_send/package.zsh'
[7.899s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/workspace_send/share/colcon-core/packages/workspace_send)
[27.573s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[27.573s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[27.573s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '1'
[27.573s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[27.591s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[27.592s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[27.592s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[27.629s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[27.629s] INFO:colcon.colcon_core.shell:Creating prefix script '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/local_setup.ps1'
[27.630s] INFO:colcon.colcon_core.shell:Creating prefix util module '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/_local_setup_util_ps1.py'
[27.632s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/setup.ps1'
[27.650s] INFO:colcon.colcon_core.shell:Creating prefix script '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/local_setup.sh'
[27.650s] INFO:colcon.colcon_core.shell:Creating prefix util module '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/_local_setup_util_sh.py'
[27.651s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/setup.sh'
[27.669s] INFO:colcon.colcon_core.shell:Creating prefix script '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/local_setup.bash'
[27.669s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/setup.bash'
[27.688s] INFO:colcon.colcon_core.shell:Creating prefix script '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/local_setup.zsh'
[27.688s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/setup.zsh'
