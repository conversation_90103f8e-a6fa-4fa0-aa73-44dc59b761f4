CMake Warning (dev) at CMakeLists.txt:126:
  Syntax Warning in cmake code at column 26

  Argument not separated from preceding token by whitespace.
This warning is for project developers.  Use -Wno-dev to suppress it.

-- The C compiler identification is GNU 9.4.0
-- The CXX compiler identification is GNU 9.4.0
-- Check for working C compiler: /usr/bin/cc
-- Check for working C compiler: /usr/bin/cc -- works
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Detecting C compile features
-- Detecting C compile features - done
-- Check for working CXX compiler: /usr/bin/c++
-- Check for working CXX compiler: /usr/bin/c++ -- works
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found rosgraph_msgs: 1.2.1 (/home/<USER>/ros2_humble/install/rosgraph_msgs/share/rosgraph_msgs/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.8.10") found components: Interpreter 
-- Found rosidl_generator_c: 3.1.6 (/home/<USER>/ros2_humble/install/rosidl_generator_c/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.6 (/home/<USER>/ros2_humble/install/rosidl_adapter/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.6 (/home/<USER>/ros2_humble/install/rosidl_generator_cpp/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_introspection_c;rosidl_typesupport_fastrtps_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_introspection_cpp;rosidl_typesupport_fastrtps_cpp
-- Found std_msgs: 4.9.0 (/home/<USER>/ros2_humble/install/std_msgs/share/std_msgs/cmake)
=============================================================
-- ROS Found. ROS Support is turned On.
=============================================================
-- Found rmw_implementation_cmake: 6.1.2 (/home/<USER>/ros2_humble/install/rmw_implementation_cmake/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.7 (/home/<USER>/ros2_humble/install/rmw_fastrtps_cpp/share/rmw_fastrtps_cpp/cmake)
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "1.1.1f")  
-- Found FastRTPS: /home/<USER>/ros2_humble/install/fastrtps/include  
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Looking for pthread.h
-- Looking for pthread.h - found
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Failed
-- Looking for pthread_create in pthreads
-- Looking for pthread_create in pthreads - not found
-- Looking for pthread_create in pthread
-- Looking for pthread_create in pthread - found
-- Found Threads: TRUE  
=============================================================
-- ROS2 Found. ROS2 Support is turned On.
=============================================================
-- Found sensor_msgs: 4.9.0 (/home/<USER>/ros2_humble/install/sensor_msgs/share/sensor_msgs/cmake)
CMake Error at CMakeLists.txt:107 (find_package):
  By not providing "Findcommon_msgs_humble.cmake" in CMAKE_MODULE_PATH this
  project has asked CMake to find a package configuration file provided by
  "common_msgs_humble", but CMake did not find one.

  Could not find a package configuration file provided by
  "common_msgs_humble" with any of the following names:

    common_msgs_humbleConfig.cmake
    common_msgs_humble-config.cmake

  Add the installation prefix of "common_msgs_humble" to CMAKE_PREFIX_PATH or
  set "common_msgs_humble_DIR" to a directory containing one of the above
  files.  If "common_msgs_humble" provides a separate development package or
  SDK, be sure it has been installed.


-- Configuring incomplete, errors occurred!
See also "/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/workspace_send/CMakeFiles/CMakeOutput.log".
See also "/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/workspace_send/CMakeFiles/CMakeError.log".
