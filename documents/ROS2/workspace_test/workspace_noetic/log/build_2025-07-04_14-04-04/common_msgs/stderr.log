Traceback (most recent call last):
  File "/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/common_msgs/catkin_generated/generate_cached_setup.py", line 22, in <module>
    code = generate_environment_script('/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/common_msgs/devel/env.sh')
  File "/opt/ros/noetic/lib/python3/dist-packages/catkin/environment_cache.py", line 63, in generate_environment_script
    env_after = ast.literal_eval(output.decode('utf8'))
  File "/usr/lib/python3.8/ast.py", line 59, in literal_eval
    node_or_string = parse(node_or_string, mode='eval')
  File "/usr/lib/python3.8/ast.py", line 47, in parse
    return compile(source, filename, mode, flags,
  File "<unknown>", line 1
    ROS_DISTRO was set to 'humble' before. Please make sure that the environment does not mix paths from different distributions.
               ^
SyntaxError: invalid syntax
CMake Error at /opt/ros/noetic/share/catkin/cmake/safe_execute_process.cmake:11 (message):
  execute_process(/usr/bin/python3
  "/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/common_msgs/catkin_generated/generate_cached_setup.py")
  returned error code 1
Call Stack (most recent call first):
  /opt/ros/noetic/share/catkin/cmake/all.cmake:208 (safe_execute_process)
  /opt/ros/noetic/share/catkin/cmake/catkinConfig.cmake:20 (include)
  CMakeLists.txt:10 (find_package)


