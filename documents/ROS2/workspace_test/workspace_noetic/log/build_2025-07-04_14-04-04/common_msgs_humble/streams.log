[0.082s] Invoking command in '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/common_msgs_humble': /usr/bin/cmake /mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/src/common_msgs_humble -DCATKIN_INSTALL_INTO_PREFIX_ROOT=0 -DCMAKE_INSTALL_PREFIX=/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble
[0.198s] -- The C compiler identification is GNU 9.4.0
[0.309s] -- The CXX compiler identification is GNU 9.4.0
[0.325s] -- Check for working C compiler: /usr/bin/cc
[0.469s] -- Check for working C compiler: /usr/bin/cc -- works
[0.469s] -- Detecting C compiler ABI info
[0.609s] -- Detecting C compiler ABI info - done
[0.618s] -- Detecting C compile features
[0.618s] -- Detecting C compile features - done
[0.621s] -- Check for working CXX compiler: /usr/bin/c++
[0.770s] -- Check for working CXX compiler: /usr/bin/c++ -- works
[0.771s] -- Detecting CXX compiler ABI info
[0.918s] -- Detecting CXX compiler ABI info - done
[0.931s] -- Detecting CXX compile features
[0.932s] -- Detecting CXX compile features - done
[2.702s] -- Found Python3: /usr/bin/python3 (found version "3.8.10") found components: Interpreter 
[3.857s] -- Found rosidl_generator_c: 3.1.6 (/home/<USER>/ros2_humble/install/rosidl_generator_c/share/rosidl_generator_c/cmake)
[3.887s] -- Found rosidl_adapter: 3.1.6 (/home/<USER>/ros2_humble/install/rosidl_adapter/share/rosidl_adapter/cmake)
[3.922s] -- Found rosidl_generator_cpp: 3.1.6 (/home/<USER>/ros2_humble/install/rosidl_generator_cpp/share/rosidl_generator_cpp/cmake)
[4.010s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_introspection_c;rosidl_typesupport_fastrtps_c
[4.095s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_introspection_cpp;rosidl_typesupport_fastrtps_cpp
[4.320s] -- Found rmw_implementation_cmake: 6.1.2 (/home/<USER>/ros2_humble/install/rmw_implementation_cmake/share/rmw_implementation_cmake/cmake)
[4.373s] -- Found rmw_fastrtps_cpp: 6.2.7 (/home/<USER>/ros2_humble/install/rmw_fastrtps_cpp/share/rmw_fastrtps_cpp/cmake)
[4.691s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "1.1.1f")  
[4.781s] -- Found FastRTPS: /home/<USER>/ros2_humble/install/fastrtps/include  
[4.909s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[4.960s] -- Looking for pthread.h
[5.089s] -- Looking for pthread.h - found
[5.090s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[5.225s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Failed
[5.225s] -- Looking for pthread_create in pthreads
[5.348s] -- Looking for pthread_create in pthreads - not found
[5.348s] -- Looking for pthread_create in pthread
[5.495s] -- Looking for pthread_create in pthread - found
[5.496s] -- Found Threads: TRUE  
[5.594s] =============================================================
[5.594s] -- ROS2 Found. ROS2 Support is turned On.
[5.594s] =============================================================
[5.607s] -- Found rosidl_default_generators: 1.2.0 (/home/<USER>/ros2_humble/install/rosidl_default_generators/share/rosidl_default_generators/cmake)
[10.627s] -- Found ament_cmake_ros: 0.10.0 (/home/<USER>/ros2_humble/install/ament_cmake_ros/share/ament_cmake_ros/cmake)
[19.620s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_introspection_c;rosidl_typesupport_fastrtps_c
[23.142s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_introspection_cpp;rosidl_typesupport_fastrtps_cpp
[23.793s] -- Found PythonInterp: /usr/bin/python3 (found suitable version "3.8.10", minimum required is "3.6") 
[23.811s] -- Found python_cmake_module: 0.10.0 (/home/<USER>/ros2_humble/install/python_cmake_module/share/python_cmake_module/cmake)
[24.441s] -- Found PythonLibs: /usr/lib/x86_64-linux-gnu/libpython3.8.so (found suitable version "3.8.10", minimum required is "3.5") 
[24.441s] -- Using PYTHON_EXECUTABLE: /usr/bin/python3
[24.441s] -- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.8
[24.442s] -- Using PYTHON_LIBRARIES: /usr/lib/x86_64-linux-gnu/libpython3.8.so
[24.952s] -- Found PythonExtra: .so  
[26.076s] -- Configuring done
[26.729s] -- Generating done
[26.733s] CMake Warning:
[26.733s]   Manually-specified variables were not used by the project:
[26.733s] 
[26.733s]     CATKIN_INSTALL_INTO_PREFIX_ROOT
[26.733s] 
[26.733s] 
[26.759s] -- Build files have been written to: /mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/common_msgs_humble
