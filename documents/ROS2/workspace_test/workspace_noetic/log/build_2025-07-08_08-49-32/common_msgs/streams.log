[0.126s] Invoking command in '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/common_msgs': /usr/bin/cmake /mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/msgs/common_msgs -GNinja -DCATKIN_INSTALL_INTO_PREFIX_ROOT=0 -DCATKIN_SYMLINK_INSTALL=ON -DCMAKE_INSTALL_PREFIX=/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs
[0.165s] -- Found rosgraph_msgs: 1.2.1 (/home/<USER>/ros2_humble/install/rosgraph_msgs/share/rosgraph_msgs/cmake)
[2.427s] -- Found rosidl_generator_c: 3.1.6 (/home/<USER>/ros2_humble/install/rosidl_generator_c/share/rosidl_generator_c/cmake)
[2.431s] -- Found rosidl_adapter: 3.1.6 (/home/<USER>/ros2_humble/install/rosidl_adapter/share/rosidl_adapter/cmake)
[2.439s] -- Found rosidl_generator_cpp: 3.1.6 (/home/<USER>/ros2_humble/install/rosidl_generator_cpp/share/rosidl_generator_cpp/cmake)
[2.483s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_introspection_c;rosidl_typesupport_fastrtps_c
[2.558s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_introspection_cpp;rosidl_typesupport_fastrtps_cpp
[3.122s] -- Found std_msgs: 4.9.0 (/home/<USER>/ros2_humble/install/std_msgs/share/std_msgs/cmake)
[3.574s] -- Using CATKIN_DEVEL_PREFIX: /mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/common_msgs/devel
[3.575s] -- Using CMAKE_PREFIX_PATH: /home/<USER>/ros2_humble/install/rosbag2_storage_mcap;/home/<USER>/ros2_humble/install/rosbag2;/home/<USER>/ros2_humble/install/rosbag2_compression_zstd;/home/<USER>/ros2_humble/install/mcap_vendor;/home/<USER>/ros2_humble/install/zstd_vendor;/home/<USER>/ros2_humble/install/rviz_visual_testing_framework;/home/<USER>/ros2_humble/install/rviz2;/home/<USER>/ros2_humble/install/rviz_default_plugins;/home/<USER>/ros2_humble/install/rviz_common;/home/<USER>/ros2_humble/install/rosbag2_py;/home/<USER>/ros2_humble/install/rosbag2_transport;/home/<USER>/ros2_humble/install/rosbag2_storage_default_plugins;/home/<USER>/ros2_humble/install/rosbag2_performance_benchmarking;/home/<USER>/ros2_humble/install/rosbag2_compression;/home/<USER>/ros2_humble/install/rosbag2_cpp;/home/<USER>/ros2_humble/install/rosbag2_storage;/home/<USER>/ros2_humble/install/image_common;/home/<USER>/ros2_humble/install/camera_info_manager;/home/<USER>/ros2_humble/install/camera_calibration_parsers;/home/<USER>/ros2_humble/install/yaml_cpp_vendor;/home/<USER>/ros2_humble/install/interactive_markers;/home/<USER>/ros2_humble/install/common_interfaces;/home/<USER>/ros2_humble/install/visualization_msgs;/home/<USER>/ros2_humble/install/dummy_robot_bringup;/home/<USER>/ros2_humble/install/robot_state_publisher;/home/<USER>/ros2_humble/install/kdl_parser;/home/<USER>/ros2_humble/install/urdf;/home/<USER>/ros2_humble/install/urdfdom;/home/<USER>/ros2_humble/install/urdf_parser_plugin;/home/<USER>/ros2_humble/install/urdfdom_headers;/home/<USER>/ros2_humble/install/turtlesim;/home/<USER>/ros2_humble/install/geometry2;/home/<USER>/ros2_humble/install/tf2_sensor_msgs;/home/<USER>/ros2_humble/install/test_tf2;/home/<USER>/ros2_humble/install/tf2_kdl;/home/<USER>/ros2_humble/install/tf2_geometry_msgs;/home/<USER>/ros2_humble/install/tf2_eigen;/home/<USER>/ros2_humble/install/tf2_bullet;/home/<USER>/ros2_humble/install/tf2_ros;/home/<USER>/ros2_humble/install/tf2_py;/home/<USER>/ros2_humble/install/tf2_msgs;/home/<USER>/ros2_humble/install/test_msgs;/home/<USER>/ros2_humble/install/sros2_cmake;/home/<USER>/ros2_humble/install/ros2cli_common_extensions;/home/<USER>/ros2_humble/install/rqt_py_common;/home/<USER>/ros2_humble/install/rosbag2_storage_mcap_testdata;/home/<USER>/ros2_humble/install/ros_testing;/home/<USER>/ros2_humble/install/ros2cli_test_interfaces;/home/<USER>/ros2_humble/install/quality_of_service_demo_cpp;/home/<USER>/ros2_humble/install/image_transport;/home/<USER>/ros2_humble/install/message_filters;/home/<USER>/ros2_humble/install/demo_nodes_cpp;/home/<USER>/ros2_humble/install/composition;/home/<USER>/ros2_humble/install/laser_geometry;/home/<USER>/ros2_humble/install/rclpy;/home/<USER>/ros2_humble/install/examples_rclcpp_minimal_action_server;/home/<USER>/ros2_humble/install/examples_rclcpp_minimal_action_client;/home/<USER>/ros2_humble/install/action_tutorials_cpp;/home/<USER>/ros2_humble/install/rclcpp_action;/home/<USER>/ros2_humble/install/rcl_action;/home/<USER>/ros2_humble/install/examples_rclcpp_wait_set;/home/<USER>/ros2_humble/install/examples_rclcpp_minimal_service;/home/<USER>/ros2_humble/install/examples_rclcpp_minimal_client;/home/<USER>/ros2_humble/install/examples_rclcpp_async_client;/home/<USER>/ros2_humble/install/example_interfaces;/home/<USER>/ros2_humble/install/action_tutorials_interfaces;/home/<USER>/ros2_humble/install/action_msgs;/home/<USER>/ros2_humble/install/unique_identifier_msgs;/home/<USER>/ros2_humble/install/ament_lint_common;/home/<USER>/ros2_humble/install/ament_cmake_uncrustify;/home/<USER>/ros2_humble/install/uncrustify_vendor;/home/<USER>/ros2_humble/install/trajectory_msgs;/home/<USER>/ros2_humble/install/topic_statistics_demo;/home/<USER>/ros2_humble/install/pendulum_control;/home/<USER>/ros2_humble/install/tlsf_cpp;/home/<USER>/ros2_humble/install/test_tracetools;/home/<USER>/ros2_humble/install/rqt_gui_cpp;/home/<USER>/ros2_humble/install/rosbag2_test_common;/home/<USER>/ros2_humble/install/ros2lifecycle_test_fixtures;/home/<USER>/ros2_humble/install/lifecycle;/home/<USER>/ros2_humble/install/rclcpp_lifecycle;/home/<USER>/ros2_humble/install/logging_demo;/home/<USER>/ros2_humble/install/image_tools;/home/<USER>/ros2_humble/install/examples_rclcpp_minimal_subscriber;/home/<USER>/ros2_humble/install/examples_rclcpp_minimal_composition;/home/<USER>/ros2_humble/install/demo_nodes_cpp_native;/home/<USER>/ros2_humble/install/rclcpp_components;/home/<USER>/ros2_humble/install/intra_process_demo;/home/<USER>/ros2_humble/install/examples_rclcpp_multithreaded_executor;/home/<USER>/ros2_humble/install/examples_rclcpp_minimal_timer;/home/<USER>/ros2_humble/install/examples_rclcpp_minimal_publisher;/home/<USER>/ros2_humble/install/examples_rclcpp_cbg_executor;/home/<USER>/ros2_humble/install/dummy_sensors;/home/<USER>/ros2_humble/install/dummy_map_server;/home/<USER>/ros2_humble/install/rclcpp;/home/<USER>/ros2_humble/install/rcl_lifecycle;/home/<USER>/ros2_humble/install/libstatistics_collector;/home/<USER>/ros2_humble/install/rcl;/home/<USER>/ros2_humble/install/rmw_implementation;/home/<USER>/ros2_humble/install/rmw_fastrtps_dynamic_cpp;/home/<USER>/ros2_humble/install/rmw_fastrtps_cpp;/home/<USER>/ros2_humble/install/rmw_fastrtps_shared_cpp;/home/<USER>/ros2_humble/install/rmw_cyclonedds_cpp;/home/<USER>/ros2_humble/install/tracetools;/home/<USER>/ros2_humble/install/tlsf;/home/<USER>/ros2_humble/install/tinyxml_vendor;/home/<USER>/ros2_humble/install/qt_gui_core;/home/<USER>/ros2_humble/install/qt_gui_cpp;/home/<USER>/ros2_humble/install/pluginlib;/home/<USER>/ros2_humble/install/tinyxml2_vendor;/home/<USER>/ros2_humble/install/tf2_eigen_kdl;/home/<USER>/ros2_humble/install/tf2;/home/<USER>/ros2_humble/install/test_security;/home/<USER>/ros2_humble/install/test_rmw_implementation;/home/<USER>/ros2_humble/install/test_rclcpp;/home/<USER>/ros2_humble/install/test_quality_of_service;/home/<USER>/ros2_humble/install/test_launch_testing;/home/<USER>/ros2_humble/install/test_interface_files;/home/<USER>/ros2_humble/install/test_communication;/home/<USER>/ros2_humble/install/test_cli_remapping;/home/<USER>/ros2_humble/install/test_cli;/home/<USER>/ros2_humble/install/qt_gui_app;/home/<USER>/ros2_humble/install/qt_gui;/home/<USER>/ros2_humble/install/tango_icons_vendor;/home/<USER>/ros2_humble/install/stereo_msgs;/home/<USER>/ros2_humble/install/std_srvs;/home/<USER>/ros2_humble/install/shape_msgs;/home/<USER>/ros2_humble/install/map_msgs;/home/<USER>/ros2_humble/install/sensor_msgs;/home/<USER>/ros2_humble/install/nav_msgs;/home/<USER>/ros2_humble/install/diagnostic_msgs;/home/<USER>/ros2_humble/install/geometry_msgs;/home/<USER>/ros2_humble/install/actionlib_msgs;/home/<USER>/ros2_humble/install/std_msgs;/home/<USER>/ros2_humble/install/statistics_msgs;/home/<USER>/ros2_humble/install/sqlite3_vendor;/home/<USER>/ros2_humble/install/rcl_logging_spdlog;/home/<USER>/ros2_humble/install/spdlog_vendor;/home/<USER>/ros2_humble/install/shared_queues_vendor;/home/<USER>/ros2_humble/install/rviz_rendering_tests;/home/<USER>/ros2_humble/install/rviz_rendering;/home/<USER>/ros2_humble/install/rviz_ogre_vendor;/home/<USER>/ros2_humble/install/rviz_assimp_vendor;/home/<USER>/ros2_humble/install/rttest;/home/<USER>/ros2_humble/install/rmw_connextddsmicro;/home/<USER>/ros2_humble/install/rmw_connextdds;/home/<USER>/ros2_humble/install/rmw_connextdds_common;/home/<USER>/ros2_humble/install/rti_connext_dds_cmake_module;/home/<USER>/ros2_humble/install/rosgraph_msgs;/home/<USER>/ros2_humble/install/rosbag2_interfaces;/home/<USER>/ros2_humble/install/rmw_dds_common;/home/<USER>/ros2_humble/install/composition_interfaces;/home/<USER>/ros2_humble/install/rcl_interfaces;/home/<USER>/ros2_humble/install/pendulum_msgs;/home/<USER>/ros2_humble/install/lifecycle_msgs;/home/<USER>/ros2_humble/install/builtin_interfaces;/home/<USER>/ros2_humble/install/rosidl_default_runtime;/home/<USER>/ros2_humble/install/rosidl_default_generators;/home/<USER>/ros2_humble/install/rosidl_generator_py;/home/<USER>/ros2_humble/install/rosidl_typesupport_introspection_tests;/home/<USER>/ros2_humble/install/rosidl_typesupport_cpp;/home/<USER>/ros2_humble/install/rosidl_typesupport_introspection_cpp;/home/<USER>/ros2_humble/install/rosidl_typesupport_c;/home/<USER>/ros2_humble/install/rosidl_typesupport_introspection_c;/home/<USER>/ros2_humble/install/rosidl_typesupport_fastrtps_c;/home/<USER>/ros2_humble/install/rosidl_typesupport_fastrtps_cpp;/home/<USER>/ros2_humble/install/rosidl_generator_cpp;/home/<USER>/ros2_humble/install/rosidl_runtime_cpp;/home/<USER>/ros2_humble/install/rcl_yaml_param_parser;/home/<USER>/ros2_humble/install/rmw;/home/<USER>/ros2_humble/install/rosidl_runtime_c;/home/<USER>/ros2_humble/install/rosidl_generator_c;/home/<USER>/ros2_humble/install/rosidl_typesupport_interface;/home/<USER>/ros2_humble/install/rosidl_generator_dds_idl;/home/<USER>/ros2_humble/install/rosidl_cmake;/home/<USER>/ros2_humble/install/rosidl_parser;/home/<USER>/ros2_humble/install/rosidl_adapter;/home/<USER>/ros2_humble/install/rosbag2_tests;/home/<USER>/ros2_humble/install/ros_environment;/home/<USER>/ros2_humble/install/rmw_implementation_cmake;/home/<USER>/ros2_humble/install/resource_retriever;/home/<USER>/ros2_humble/install/class_loader;/home/<USER>/ros2_humble/install/rcpputils;/home/<USER>/ros2_humble/install/rcl_logging_noop;/home/<USER>/ros2_humble/install/rcl_logging_interface;/home/<USER>/ros2_humble/install/rcutils;/home/<USER>/ros2_humble/install/qt_gui_py_common;/home/<USER>/ros2_humble/install/qt_dotgraph;/home/<USER>/ros2_humble/install/python_qt_binding;/home/<USER>/ros2_humble/install/python_orocos_kdl_vendor;/home/<USER>/ros2_humble/install/launch_testing_ament_cmake;/home/<USER>/ros2_humble/install/python_cmake_module;/home/<USER>/ros2_humble/install/pybind11_vendor;/home/<USER>/ros2_humble/install/performance_test_fixture;/home/<USER>/ros2_humble/install/osrf_testing_tools_cpp;/home/<USER>/ros2_humble/install/orocos_kdl_vendor;/home/<USER>/ros2_humble/install/mimick_vendor;/home/<USER>/ros2_humble/install/libyaml_vendor;/home/<USER>/ros2_humble/install/libcurl_vendor;/home/<USER>/ros2_humble/install/keyboard_handler;/home/<USER>/ros2_humble/install/iceoryx_introspection;/home/<USER>/ros2_humble/install/cyclonedds;/home/<USER>/ros2_humble/install/iceoryx_posh;/home/<USER>/ros2_humble/install/iceoryx_hoofs;/home/<USER>/ros2_humble/install/iceoryx_binding_c;/home/<USER>/ros2_humble/install/ament_cmake_ros;/home/<USER>/ros2_humble/install/ament_cmake_auto;/home/<USER>/ros2_humble/install/ament_cmake_gmock;/home/<USER>/ros2_humble/install/gmock_vendor;/home/<USER>/ros2_humble/install/ament_cmake_gtest;/home/<USER>/ros2_humble/install/gtest_vendor;/home/<USER>/ros2_humble/install/ament_cmake_google_benchmark;/home/<USER>/ros2_humble/install/google_benchmark_vendor;/home/<USER>/ros2_humble/install/fastrtps;/home/<USER>/ros2_humble/install/foonathan_memory_vendor;/home/<USER>/ros2_humble/install/fastrtps_cmake_module;/home/<USER>/ros2_humble/install/fastcdr;/home/<USER>/ros2_humble/install/eigen3_cmake_module;/home/<USER>/ros2_humble/install/console_bridge_vendor;/home/<USER>/ros2_humble/install/ament_cmake_xmllint;/home/<USER>/ros2_humble/install/ament_cmake_pyflakes;/home/<USER>/ros2_humble/install/ament_cmake_pycodestyle;/home/<USER>/ros2_humble/install/ament_cmake_pep257;/home/<USER>/ros2_humble/install/ament_cmake_pclint;/home/<USER>/ros2_humble/install/ament_lint_auto;/home/<USER>/ros2_humble/install/ament_cmake;/home/<USER>/ros2_humble/install/ament_cmake_version;/home/<USER>/ros2_humble/install/ament_cmake_vendor_package;/home/<USER>/ros2_humble/install/ament_cmake_pytest;/home/<USER>/ros2_humble/install/ament_cmake_nose;/home/<USER>/ros2_humble/install/ament_cmake_mypy;/home/<USER>/ros2_humble/install/ament_cmake_lint_cmake;/home/<USER>/ros2_humble/install/ament_cmake_flake8;/home/<USER>/ros2_humble/install/ament_cmake_cpplint;/home/<USER>/ros2_humble/install/ament_cmake_cppcheck;/home/<USER>/ros2_humble/install/ament_cmake_copyright;/home/<USER>/ros2_humble/install/ament_cmake_clang_tidy;/home/<USER>/ros2_humble/install/ament_cmake_clang_format;/home/<USER>/ros2_humble/install/ament_cmake_test;/home/<USER>/ros2_humble/install/ament_cmake_target_dependencies;/home/<USER>/ros2_humble/install/ament_cmake_python;/home/<USER>/ros2_humble/install/ament_cmake_export_dependencies;/home/<USER>/ros2_humble/install/ament_cmake_libraries;/home/<USER>/ros2_humble/install/ament_cmake_include_directories;/home/<USER>/ros2_humble/install/ament_cmake_gen_version_h;/home/<USER>/ros2_humble/install/ament_cmake_export_targets;/home/<USER>/ros2_humble/install/ament_cmake_export_link_flags;/home/<USER>/ros2_humble/install/ament_cmake_export_interfaces;/home/<USER>/ros2_humble/install/ament_cmake_export_libraries;/home/<USER>/ros2_humble/install/ament_cmake_export_include_directories;/home/<USER>/ros2_humble/install/ament_cmake_export_definitions;/home/<USER>/ros2_humble/install/ament_cmake_core;/home/<USER>/ros2_humble/install/ament_index_cpp;/opt/ros/noetic
[3.576s] -- This workspace overlays: /home/<USER>/ros2_humble/install/orocos_kdl_vendor;/opt/ros/noetic
[3.578s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[3.991s] -- Found PythonInterp: /usr/bin/python3 (found suitable version "3.8.10", minimum required is "3") 
[3.991s] -- Using PYTHON_EXECUTABLE: /usr/bin/python3
[3.991s] -- Using Debian Python package layout
[3.991s] -- Using empy: /usr/lib/python3/dist-packages/em.py
[4.124s] -- Using CATKIN_ENABLE_TESTING: ON
[4.124s] -- Call enable_testing()
[4.124s] -- Using CATKIN_TEST_RESULTS_DIR: /mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/common_msgs/test_results
[4.234s] -- Forcing gtest/gmock from source, though one was otherwise available.
[4.234s] -- Found gtest sources under '/usr/src/googletest': gtests will be built
[4.234s] -- Found gmock sources under '/usr/src/googletest': gmock will be built
[4.640s] -- Found PythonInterp: /usr/bin/python3 (found version "3.8.10") 
[4.646s] -- Using Python nosetests: /usr/bin/nosetests3
[5.144s] -- catkin 0.8.10
[5.144s] -- BUILD_SHARED_LIBS is on
[7.389s] Traceback (most recent call last):
[7.390s]   File "/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/common_msgs/catkin_generated/generate_cached_setup.py", line 22, in <module>
[7.390s]     code = generate_environment_script('/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/common_msgs/devel/env.sh')
[7.390s]   File "/opt/ros/noetic/lib/python3/dist-packages/catkin/environment_cache.py", line 63, in generate_environment_script
[7.390s]     env_after = ast.literal_eval(output.decode('utf8'))
[7.390s]   File "/usr/lib/python3.8/ast.py", line 59, in literal_eval
[7.390s]     node_or_string = parse(node_or_string, mode='eval')
[7.390s]   File "/usr/lib/python3.8/ast.py", line 47, in parse
[7.390s]     return compile(source, filename, mode, flags,
[7.390s]   File "<unknown>", line 1
[7.390s]     ROS_DISTRO was set to 'humble' before. Please make sure that the environment does not mix paths from different distributions.
[7.390s]                ^
[7.390s] SyntaxError: invalid syntax
[7.395s] CMake Error at /opt/ros/noetic/share/catkin/cmake/safe_execute_process.cmake:11 (message):
[7.395s]   execute_process(/usr/bin/python3
[7.395s]   "/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/common_msgs/catkin_generated/generate_cached_setup.py")
[7.395s]   returned error code 1
[7.395s] Call Stack (most recent call first):
[7.395s]   /opt/ros/noetic/share/catkin/cmake/all.cmake:208 (safe_execute_process)
[7.395s]   /opt/ros/noetic/share/catkin/cmake/catkinConfig.cmake:20 (include)
[7.395s]   CMakeLists.txt:10 (find_package)
[7.396s] 
[7.396s] 
[7.397s] -- Configuring incomplete, errors occurred!
[7.398s] See also "/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/common_msgs/CMakeFiles/CMakeOutput.log".
[7.398s] See also "/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/common_msgs/CMakeFiles/CMakeError.log".
