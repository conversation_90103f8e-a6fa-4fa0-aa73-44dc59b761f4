[0.383s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'common_msgs_humble']
[0.383s] DEBUG:colcon:Parsed command line arguments: Namespace(allow_overriding=[], ament_cmake_args=None, base_paths=['.'], build_base='build', catkin_cmake_args=None, catkin_skip_building_tests=False, cmake_args=None, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, cmake_target=None, cmake_target_skip_unavailable=False, continue_on_error=False, event_handlers=None, executor='parallel', ignore_user_meta=False, install_base='install', log_base=None, log_level=None, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7f09e0c51070>>, merge_install=False, metas=['./colcon.meta'], mixin=None, mixin_files=None, mixin_verb=('build',), packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_end=None, packages_ignore=None, packages_ignore_regex=None, packages_select=['common_msgs_humble'], packages_select_build_failed=False, packages_select_by_dep=None, packages_select_regex=None, packages_select_test_failures=False, packages_skip=None, packages_skip_build_finished=False, packages_skip_by_dep=None, packages_skip_regex=None, packages_skip_test_passed=False, packages_skip_up_to=None, packages_start=None, packages_up_to=None, packages_up_to_regex=None, parallel_workers=12, paths=None, symlink_install=False, test_result_base=None, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7f09e0c51070>, verb_name='build', verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x7f09e0c51910>)
[0.678s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.678s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.678s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.678s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.678s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.678s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.678s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic'
[0.678s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.678s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.678s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.678s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.678s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.678s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.678s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.678s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.678s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.689s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.689s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.689s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.689s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.689s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.690s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.690s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.690s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.690s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.690s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.690s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.690s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.690s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.690s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.690s] Level 1:colcon.colcon_core.package_identification:_identify(msgs) by extensions ['ignore', 'ignore_ament_install']
[0.690s] Level 1:colcon.colcon_core.package_identification:_identify(msgs) by extension 'ignore'
[0.690s] Level 1:colcon.colcon_core.package_identification:_identify(msgs) by extension 'ignore_ament_install'
[0.690s] Level 1:colcon.colcon_core.package_identification:_identify(msgs) by extensions ['colcon_pkg']
[0.690s] Level 1:colcon.colcon_core.package_identification:_identify(msgs) by extension 'colcon_pkg'
[0.690s] Level 1:colcon.colcon_core.package_identification:_identify(msgs) by extensions ['colcon_meta']
[0.690s] Level 1:colcon.colcon_core.package_identification:_identify(msgs) by extension 'colcon_meta'
[0.691s] Level 1:colcon.colcon_core.package_identification:_identify(msgs) by extensions ['ros']
[0.691s] Level 1:colcon.colcon_core.package_identification:_identify(msgs) by extension 'ros'
[0.691s] Level 1:colcon.colcon_core.package_identification:_identify(msgs) by extensions ['cmake', 'python']
[0.691s] Level 1:colcon.colcon_core.package_identification:_identify(msgs) by extension 'cmake'
[0.691s] Level 1:colcon.colcon_core.package_identification:_identify(msgs) by extension 'python'
[0.691s] Level 1:colcon.colcon_core.package_identification:_identify(msgs) by extensions ['python_setup_py']
[0.691s] Level 1:colcon.colcon_core.package_identification:_identify(msgs) by extension 'python_setup_py'
[0.691s] Level 1:colcon.colcon_core.package_identification:_identify(msgs/cloud_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.691s] Level 1:colcon.colcon_core.package_identification:_identify(msgs/cloud_msgs) by extension 'ignore'
[0.691s] Level 1:colcon.colcon_core.package_identification:_identify(msgs/cloud_msgs) by extension 'ignore_ament_install'
[0.691s] Level 1:colcon.colcon_core.package_identification:_identify(msgs/cloud_msgs) by extensions ['colcon_pkg']
[0.691s] Level 1:colcon.colcon_core.package_identification:_identify(msgs/cloud_msgs) by extension 'colcon_pkg'
[0.691s] Level 1:colcon.colcon_core.package_identification:_identify(msgs/cloud_msgs) by extensions ['colcon_meta']
[0.691s] Level 1:colcon.colcon_core.package_identification:_identify(msgs/cloud_msgs) by extension 'colcon_meta'
[0.691s] Level 1:colcon.colcon_core.package_identification:_identify(msgs/cloud_msgs) by extensions ['ros']
[0.691s] Level 1:colcon.colcon_core.package_identification:_identify(msgs/cloud_msgs) by extension 'ros'
[0.693s] DEBUG:colcon.colcon_core.package_identification:Package 'msgs/cloud_msgs' with type 'ros.catkin' and name 'cloud_msgs'
[0.694s] Level 1:colcon.colcon_core.package_identification:_identify(msgs/common_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.694s] Level 1:colcon.colcon_core.package_identification:_identify(msgs/common_msgs) by extension 'ignore'
[0.694s] Level 1:colcon.colcon_core.package_identification:_identify(msgs/common_msgs) by extension 'ignore_ament_install'
[0.694s] Level 1:colcon.colcon_core.package_identification:_identify(msgs/common_msgs) by extensions ['colcon_pkg']
[0.694s] Level 1:colcon.colcon_core.package_identification:_identify(msgs/common_msgs) by extension 'colcon_pkg'
[0.694s] Level 1:colcon.colcon_core.package_identification:_identify(msgs/common_msgs) by extensions ['colcon_meta']
[0.694s] Level 1:colcon.colcon_core.package_identification:_identify(msgs/common_msgs) by extension 'colcon_meta'
[0.694s] Level 1:colcon.colcon_core.package_identification:_identify(msgs/common_msgs) by extensions ['ros']
[0.694s] Level 1:colcon.colcon_core.package_identification:_identify(msgs/common_msgs) by extension 'ros'
[0.695s] DEBUG:colcon.colcon_core.package_identification:Package 'msgs/common_msgs' with type 'ros.catkin' and name 'common_msgs'
[0.695s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.695s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.695s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.695s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.695s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.695s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.695s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.695s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.695s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.695s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.695s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.695s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.695s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.695s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.695s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_include) by extensions ['ignore', 'ignore_ament_install']
[0.695s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_include) by extension 'ignore'
[0.695s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_include) by extension 'ignore_ament_install'
[0.695s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_include) by extensions ['colcon_pkg']
[0.696s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_include) by extension 'colcon_pkg'
[0.696s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_include) by extensions ['colcon_meta']
[0.696s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_include) by extension 'colcon_meta'
[0.696s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_include) by extensions ['ros']
[0.696s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_include) by extension 'ros'
[0.696s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_include) by extensions ['cmake', 'python']
[0.696s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_include) by extension 'cmake'
[0.696s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_include) by extension 'python'
[0.696s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_include) by extensions ['python_setup_py']
[0.696s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_include) by extension 'python_setup_py'
[0.696s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_msgs_humble) by extensions ['ignore', 'ignore_ament_install']
[0.696s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_msgs_humble) by extension 'ignore'
[0.696s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_msgs_humble) by extension 'ignore_ament_install'
[0.696s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_msgs_humble) by extensions ['colcon_pkg']
[0.696s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_msgs_humble) by extension 'colcon_pkg'
[0.696s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_msgs_humble) by extensions ['colcon_meta']
[0.696s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_msgs_humble) by extension 'colcon_meta'
[0.696s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_msgs_humble) by extensions ['ros']
[0.696s] Level 1:colcon.colcon_core.package_identification:_identify(src/common_msgs_humble) by extension 'ros'
[0.697s] DEBUG:colcon.colcon_core.package_identification:Package 'src/common_msgs_humble' with type 'ros.catkin' and name 'common_msgs_humble'
[0.697s] Level 1:colcon.colcon_core.package_identification:_identify(src/recv_pkg) by extensions ['ignore', 'ignore_ament_install']
[0.697s] Level 1:colcon.colcon_core.package_identification:_identify(src/recv_pkg) by extension 'ignore'
[0.697s] Level 1:colcon.colcon_core.package_identification:_identify(src/recv_pkg) by extension 'ignore_ament_install'
[0.697s] Level 1:colcon.colcon_core.package_identification:_identify(src/recv_pkg) by extensions ['colcon_pkg']
[0.697s] Level 1:colcon.colcon_core.package_identification:_identify(src/recv_pkg) by extension 'colcon_pkg'
[0.697s] Level 1:colcon.colcon_core.package_identification:_identify(src/recv_pkg) by extensions ['colcon_meta']
[0.697s] Level 1:colcon.colcon_core.package_identification:_identify(src/recv_pkg) by extension 'colcon_meta'
[0.697s] Level 1:colcon.colcon_core.package_identification:_identify(src/recv_pkg) by extensions ['ros']
[0.697s] Level 1:colcon.colcon_core.package_identification:_identify(src/recv_pkg) by extension 'ros'
[0.698s] DEBUG:colcon.colcon_core.package_identification:Package 'src/recv_pkg' with type 'ros.ament_cmake' and name 'workspace_recv'
[0.698s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_pkg) by extensions ['ignore', 'ignore_ament_install']
[0.698s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_pkg) by extension 'ignore'
[0.698s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_pkg) by extension 'ignore_ament_install'
[0.698s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_pkg) by extensions ['colcon_pkg']
[0.698s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_pkg) by extension 'colcon_pkg'
[0.698s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_pkg) by extensions ['colcon_meta']
[0.698s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_pkg) by extension 'colcon_meta'
[0.698s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_pkg) by extensions ['ros']
[0.698s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_pkg) by extension 'ros'
[0.698s] DEBUG:colcon.colcon_core.package_identification:Package 'src/send_pkg' with type 'ros.ament_cmake' and name 'workspace_send'
[0.699s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.699s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.699s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.699s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.699s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.729s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'common_msgs' in 'msgs/common_msgs'
[0.729s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'workspace_recv' in 'src/recv_pkg'
[0.729s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'workspace_send' in 'src/send_pkg'
[0.729s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'cloud_msgs' in 'msgs/cloud_msgs'
[0.729s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.729s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.741s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 2 installed packages in /mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install
[0.749s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 346 installed packages in /home/<USER>/ros2_humble/install
[0.751s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /home/<USER>/ros2_humble/install/orocos_kdl_vendor
[0.751s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /opt/ros/noetic
[0.751s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.780s] Level 5:colcon.colcon_core.verb:set package 'common_msgs_humble' build argument 'cmake_args' from command line to 'None'
[0.780s] Level 5:colcon.colcon_core.verb:set package 'common_msgs_humble' build argument 'cmake_target' from command line to 'None'
[0.780s] Level 5:colcon.colcon_core.verb:set package 'common_msgs_humble' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.780s] Level 5:colcon.colcon_core.verb:set package 'common_msgs_humble' build argument 'cmake_clean_cache' from command line to 'False'
[0.780s] Level 5:colcon.colcon_core.verb:set package 'common_msgs_humble' build argument 'cmake_clean_first' from command line to 'False'
[0.780s] Level 5:colcon.colcon_core.verb:set package 'common_msgs_humble' build argument 'cmake_force_configure' from command line to 'False'
[0.780s] Level 5:colcon.colcon_core.verb:set package 'common_msgs_humble' build argument 'ament_cmake_args' from command line to 'None'
[0.780s] Level 5:colcon.colcon_core.verb:set package 'common_msgs_humble' build argument 'catkin_cmake_args' from command line to 'None'
[0.780s] Level 5:colcon.colcon_core.verb:set package 'common_msgs_humble' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.780s] DEBUG:colcon.colcon_core.verb:Building package 'common_msgs_humble' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/common_msgs_humble', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble', 'merge_install': False, 'path': '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/src/common_msgs_humble', 'symlink_install': False, 'test_result_base': None}
[0.780s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.781s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.781s] INFO:colcon.colcon_ros.task.catkin.build:Building ROS package in '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/src/common_msgs_humble' with build type 'catkin'
[0.781s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/src/common_msgs_humble'
[0.786s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.786s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.786s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.816s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/common_msgs_humble': /usr/bin/cmake /mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/src/common_msgs_humble -DCATKIN_INSTALL_INTO_PREFIX_ROOT=0 -DCMAKE_INSTALL_PREFIX=/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble
[20.585s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/common_msgs_humble' returned '0': /usr/bin/cmake /mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/src/common_msgs_humble -DCATKIN_INSTALL_INTO_PREFIX_ROOT=0 -DCMAKE_INSTALL_PREFIX=/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble
[20.586s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/common_msgs_humble': /usr/bin/cmake --build /mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/common_msgs_humble -- -j12 -l12
[67.360s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/common_msgs_humble' returned '0': /usr/bin/cmake --build /mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/common_msgs_humble -- -j12 -l12
[67.383s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/common_msgs_humble': /usr/bin/cmake --install /mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/common_msgs_humble
[68.176s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/common_msgs_humble' returned '0': /usr/bin/cmake --install /mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/common_msgs_humble
[68.177s] Level 1:colcon.colcon_core.shell:create_environment_hook('common_msgs_humble', 'ros_package_path')
[68.177s] INFO:colcon.colcon_core.shell:Creating environment hook '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble/share/common_msgs_humble/hook/ros_package_path.ps1'
[68.178s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble/share/common_msgs_humble/hook/ros_package_path.dsv'
[68.178s] INFO:colcon.colcon_core.shell:Creating environment hook '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble/share/common_msgs_humble/hook/ros_package_path.sh'
[68.179s] Level 1:colcon.colcon_ros.task.catkin:checking '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble/lib/python3.8/site-packages'
[68.179s] Level 1:colcon.colcon_core.shell:create_environment_hook('common_msgs_humble', 'catkin_pythonpath')
[68.179s] INFO:colcon.colcon_core.shell:Creating environment hook '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble/share/common_msgs_humble/hook/catkin_pythonpath.ps1'
[68.179s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble/share/common_msgs_humble/hook/catkin_pythonpath.dsv'
[68.180s] INFO:colcon.colcon_core.shell:Creating environment hook '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble/share/common_msgs_humble/hook/catkin_pythonpath.sh'
[68.180s] Level 1:colcon.colcon_core.shell:create_environment_hook('common_msgs_humble', 'pkg_config_path')
[68.180s] INFO:colcon.colcon_core.shell:Creating environment hook '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble/share/common_msgs_humble/hook/pkg_config_path.ps1'
[68.180s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble/share/common_msgs_humble/hook/pkg_config_path.dsv'
[68.181s] INFO:colcon.colcon_core.shell:Creating environment hook '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble/share/common_msgs_humble/hook/pkg_config_path.sh'
[68.187s] Level 1:colcon.colcon_core.shell:create_environment_hook('common_msgs_humble', 'pkg_config_path_multiarch')
[68.188s] INFO:colcon.colcon_core.shell:Creating environment hook '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble/share/common_msgs_humble/hook/pkg_config_path_multiarch.ps1'
[68.188s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble/share/common_msgs_humble/hook/pkg_config_path_multiarch.dsv'
[68.189s] INFO:colcon.colcon_core.shell:Creating environment hook '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble/share/common_msgs_humble/hook/pkg_config_path_multiarch.sh'
[68.190s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(common_msgs_humble)
[68.195s] Level 1:colcon.colcon_core.environment:checking '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble' for CMake module files
[68.196s] Level 1:colcon.colcon_core.environment:checking '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble' for CMake config files
[68.196s] Level 1:colcon.colcon_core.shell:create_environment_hook('common_msgs_humble', 'cmake_prefix_path')
[68.196s] INFO:colcon.colcon_core.shell:Creating environment hook '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble/share/common_msgs_humble/hook/cmake_prefix_path.ps1'
[68.197s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble/share/common_msgs_humble/hook/cmake_prefix_path.dsv'
[68.197s] INFO:colcon.colcon_core.shell:Creating environment hook '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble/share/common_msgs_humble/hook/cmake_prefix_path.sh'
[68.198s] Level 1:colcon.colcon_core.environment:checking '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble/lib'
[68.198s] Level 1:colcon.colcon_core.shell:create_environment_hook('common_msgs_humble', 'ld_library_path_lib')
[68.198s] INFO:colcon.colcon_core.shell:Creating environment hook '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble/share/common_msgs_humble/hook/ld_library_path_lib.ps1'
[68.198s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble/share/common_msgs_humble/hook/ld_library_path_lib.dsv'
[68.198s] INFO:colcon.colcon_core.shell:Creating environment hook '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble/share/common_msgs_humble/hook/ld_library_path_lib.sh'
[68.199s] Level 1:colcon.colcon_core.environment:checking '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble/bin'
[68.199s] Level 1:colcon.colcon_core.environment:checking '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble/lib/pkgconfig/common_msgs_humble.pc'
[68.199s] Level 1:colcon.colcon_core.environment:checking '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble/lib/python3.8/site-packages'
[68.199s] Level 1:colcon.colcon_core.shell:create_environment_hook('common_msgs_humble', 'pythonpath')
[68.199s] INFO:colcon.colcon_core.shell:Creating environment hook '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble/share/common_msgs_humble/hook/pythonpath.ps1'
[68.199s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble/share/common_msgs_humble/hook/pythonpath.dsv'
[68.200s] INFO:colcon.colcon_core.shell:Creating environment hook '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble/share/common_msgs_humble/hook/pythonpath.sh'
[68.200s] Level 1:colcon.colcon_core.environment:checking '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble/bin'
[68.201s] INFO:colcon.colcon_core.shell:Creating package script '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble/share/common_msgs_humble/package.ps1'
[68.201s] INFO:colcon.colcon_core.shell:Creating package descriptor '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble/share/common_msgs_humble/package.dsv'
[68.202s] INFO:colcon.colcon_core.shell:Creating package script '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble/share/common_msgs_humble/package.sh'
[68.203s] INFO:colcon.colcon_core.shell:Creating package script '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble/share/common_msgs_humble/package.bash'
[68.203s] INFO:colcon.colcon_core.shell:Creating package script '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble/share/common_msgs_humble/package.zsh'
[68.204s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/common_msgs_humble/share/colcon-core/packages/common_msgs_humble)
[68.204s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[68.205s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[68.205s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[68.205s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[68.216s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[68.216s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[68.216s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[68.228s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[68.229s] INFO:colcon.colcon_core.shell:Creating prefix script '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/local_setup.ps1'
[68.230s] INFO:colcon.colcon_core.shell:Creating prefix util module '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/_local_setup_util_ps1.py'
[68.232s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/setup.ps1'
[68.251s] INFO:colcon.colcon_core.shell:Creating prefix script '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/local_setup.sh'
[68.252s] INFO:colcon.colcon_core.shell:Creating prefix util module '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/_local_setup_util_sh.py'
[68.252s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/setup.sh'
[68.265s] INFO:colcon.colcon_core.shell:Creating prefix script '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/local_setup.bash'
[68.265s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/setup.bash'
[68.278s] INFO:colcon.colcon_core.shell:Creating prefix script '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/local_setup.zsh'
[68.278s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/install/setup.zsh'
