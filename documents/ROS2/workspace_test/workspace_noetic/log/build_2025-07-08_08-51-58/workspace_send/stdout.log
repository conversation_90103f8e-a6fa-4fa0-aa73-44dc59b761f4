-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found rosidl_generator_c: 3.1.6 (/home/<USER>/ros2_humble/install/rosidl_generator_c/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.6 (/home/<USER>/ros2_humble/install/rosidl_adapter/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.6 (/home/<USER>/ros2_humble/install/rosidl_generator_cpp/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_introspection_c;rosidl_typesupport_fastrtps_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_introspection_cpp;rosidl_typesupport_fastrtps_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/home/<USER>/ros2_humble/install/rmw_implementation_cmake/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.7 (/home/<USER>/ros2_humble/install/rmw_fastrtps_cpp/share/rmw_fastrtps_cpp/cmake)
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found sensor_msgs: 4.9.0 (/home/<USER>/ros2_humble/install/sensor_msgs/share/sensor_msgs/cmake)
-- Configuring incomplete, errors occurred!
See also "/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/workspace_send/CMakeFiles/CMakeOutput.log".
See also "/mnt/data/autoDriving/HongQi2/documents/ROS2/workspace_test/workspace_noetic/build/workspace_send/CMakeFiles/CMakeError.log".
